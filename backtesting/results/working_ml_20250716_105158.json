{"strategy": "Working_ML_Enhanced", "results": {"total_return": 0.0, "total_trades": 0, "winning_trades": 0, "losing_trades": 0, "win_rate": 0, "max_drawdown": 0.0, "sharpe_ratio": 0, "final_value": 10000.0, "parameters": {"ml_confidence_threshold": 0.6, "risk_reward_ratio": 2.0, "min_range_pips": 8.0}, "avg_ml_confidence": 0.46300991806667163, "ml_predictions_count": 12296, "trades_log": [{"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.60000000000083}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 26.600000000001067}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.200000000000209}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.500000000000064}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 21.100000000000563}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.099999999999444}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.099999999999664}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.199999999997878}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.399999999999189}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.099999999999554}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.500000000000064}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.000000000001119}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.799999999999919}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.299999999998423}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.499999999999954}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.700000000001264}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.799999999999699}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 58.700000000000415}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.300000000000974}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.200000000000099}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.00000000000123}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.399999999999299}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.399999999999409}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.900000000000244}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 27.89999999999848}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.399999999999189}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 13.000000000000789}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.799999999999478}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.399999999999078}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 14.699999999998603}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.500000000000064}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.899999999998133}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.900000000000574}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 15.400000000000968}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 15.000000000000568}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.099999999999444}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 18.700000000000383}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.799999999999919}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.500000000000174}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.80000000000203}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.500000000000064}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.099999999999664}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.499999999999954}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.100000000001774}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.80000000000192}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 34.600000000000186}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.900000000000574}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.799999999999919}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.600000000000499}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.299999999998533}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.699999999999044}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.500000000000174}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.799999999999919}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 19.400000000000528}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 15.300000000000313}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 18.599999999999728}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.300000000000974}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 21.299999999999653}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.500000000000174}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.300000000000864}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.000000000000899}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.099999999999774}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.099999999999334}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 15.700000000000713}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 19.800000000000928}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.299999999998533}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.100000000001884}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.799999999999919}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.999999999998899}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 15.100000000001224}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.299999999998754}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.099999999999664}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.699999999999154}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.80000000000192}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 14.600000000000168}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 20.899999999999253}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 17.199999999999438}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.299999999998644}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 39.29999999999989}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 16.899999999999693}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 16.199999999999548}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 15.499999999999403}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.999999999998789}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 17.799999999998928}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.799999999999589}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.200000000000319}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 13.099999999999223}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 16.799999999999038}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.799999999999589}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 14.400000000001079}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.699999999999154}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.899999999998023}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 18.800000000001038}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 45.600000000001195}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 29.399999999999427}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 42.9999999999997}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 22.199999999998887}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 39.39999999999833}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 17.900000000001803}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 52.599999999998204}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 35.00000000000058}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 19.100000000000783}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 44.39999999999999}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 26.89999999999859}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.399999999999409}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 19.899999999999363}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 29.200000000000337}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 28.799999999999937}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 14.899999999999913}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 16.100000000001113}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.799999999999919}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.300000000000644}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.599999999998168}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 18.799999999998818}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.499999999999844}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 17.300000000000093}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.099999999999334}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 24.100000000000232}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 21.400000000000308}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 18.500000000001293}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 16.499999999999293}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.699999999999044}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 13.699999999998713}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 17.80000000000115}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.000000000000899}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 16.80000000000126}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.399999999999189}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.299999999998644}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 17.80000000000115}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 18.100000000000893}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.399999999999078}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 19.800000000000928}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 19.800000000000928}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.400000000001409}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 16.599999999999948}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 15.500000000001624}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 14.799999999999258}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.099999999999664}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.500000000000064}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.300000000000644}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 15.799999999999148}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.700000000001374}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 21.799999999998487}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 25.399999999999867}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 26.500000000000412}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.699999999998823}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 18.499999999999073}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.999999999998899}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 23.899999999998922}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 12.799999999999478}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 11.100000000001664}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.399999999999299}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 8.799999999999919}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 14.999999999998348}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.600000000000719}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 13.600000000000279}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.300000000000974}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.399999999999299}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.999999999998789}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.600000000000609}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 9.200000000000319}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 13.699999999998713}, {"action": "skipped", "ml_confidence": 0.5, "range_pips": 10.300000000000864}]}, "best_parameters": {"ml_confidence_threshold": 0.6, "risk_reward_ratio": 2.0, "min_range_pips": 8.0}, "timestamp": "20250716_105158"}