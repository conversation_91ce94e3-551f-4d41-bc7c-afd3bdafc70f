//+------------------------------------------------------------------+
//|                                 FirstCandleBreakout_Enhanced.mq5 |
//|                      Copyright 2025, Roo - AI Software Engineer |
//|                                Enhanced with High Impact Features |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Roo - AI Software Engineer"
#property link      ""
#property version   "2.00"
#property strict

#include <Trade\Trade.mqh>

//--- Custom Enumerations
enum ENUM_SESSION
{
    SESSION_LONDON,
    SESSION_NEW_YORK
};

//--- EA Input Parameters
//--- Session and Magic Number
input group           "Session Settings"
input ENUM_SESSION    SessionToTrade      = SESSION_LONDON; // Session to trade (London or New York)
input ulong           MagicNumber         = 12345;          // Unique ID for EA's trades

//--- Risk Management
input group           "Risk Management"
input double          RiskPercent         = 1.0;            // Risk percentage of account equity per trade
input double          MaxSpread           = 2.0;            // Maximum allowed spread in pips
input double          RiskRewardRatio     = 2.0;            // Risk-to-Reward ratio for final Take Profit

//--- Enhanced Filters
input group           "Enhanced Filters"
input bool            UseVolumeFilter     = true;           // Enable volume confirmation filter
input double          VolumeMultiplier    = 1.2;            // Minimum volume multiplier vs average
input bool            UseATRFilter        = true;           // Enable ATR volatility filter
input int             ATRPeriod           = 14;             // ATR calculation period
input double          MinATRMultiplier    = 1.0;            // Minimum ATR multiplier vs average
input double          MaxATRMultiplier    = 3.0;            // Maximum ATR multiplier vs average

//--- Multiple Profit Targets
input group           "Profit Management"
input bool            UseMultipleTargets  = true;           // Enable multiple profit targets
input double          FirstTargetRatio    = 1.0;            // First target R:R ratio
input double          SecondTargetRatio   = 1.5;            // Second target R:R ratio
input double          FinalTargetRatio    = 2.5;            // Final target R:R ratio
input double          FirstTargetPercent  = 30.0;           // % of position to close at first target
input double          SecondTargetPercent = 40.0;           // % of position to close at second target

//--- Order Settings
input group           "Order Settings"
input double          OrderOffsetPips     = 1.0;            // Pips to offset pending orders from the candle's high/low
input int             MaxSlippage         = 3;              // Maximum allowed slippage in points

//--- Enhanced Trade Management
input group           "Enhanced Trade Management"
input bool            UseDynamicBreakeven = true;           // Enable dynamic breakeven logic
input double          BreakevenAtRatio    = 1.2;            // Move to BE when position reaches this R:R ratio
input int             TrailingStopPips    = 15;             // Trailing stop distance in pips (0 to disable)
input int             MaxTradeHours       = 8;              // Maximum hours to keep trade open (0 to disable)

//--- Global Variables
//--- Session Timing
datetime sessionStartTime;
datetime sessionEndTime;
int      lastTradeDay = 0;

//--- First Candle Data
double   firstCandleHigh = 0;
double   firstCandleLow = 0;
bool     firstCandleIdentified = false;
double   firstCandleVolume = 0;

//--- Trade State
bool     tradePlacedForSession = false;

//--- Enhanced Variables
double   averageVolume = 0;
double   averageATR = 0;
double   currentATR = 0;

//--- Position Management
struct PositionInfo
{
    double originalLotSize;
    double remainingLotSize;
    bool   firstTargetHit;
    bool   secondTargetHit;
    bool   movedToBreakeven;
    datetime openTime;
};
PositionInfo positionInfo;

//--- MQL5 Objects
CTrade   trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("==================================================");
    Print("FirstCandleBreakout Enhanced EA v2.00 Initializing...");
    Print("DEBUG [INIT]: Symbol: ", _Symbol);
    Print("DEBUG [INIT]: Account Currency: ", AccountInfoString(ACCOUNT_CURRENCY));
    Print("DEBUG [INIT]: Account Equity: ", AccountInfoDouble(ACCOUNT_EQUITY));
    Print("DEBUG [INIT]: Session to Trade: ", (SessionToTrade == SESSION_LONDON ? "LONDON" : "NEW_YORK"));
    Print("DEBUG [INIT]: Magic Number: ", MagicNumber);
    Print("DEBUG [INIT]: Risk Percent: ", RiskPercent, "%");
    Print("DEBUG [INIT]: Max Spread: ", MaxSpread, " pips");
    Print("DEBUG [INIT]: Final R:R Ratio: ", RiskRewardRatio);
    
    // Enhanced features status
    Print("=== ENHANCED FEATURES ===");
    Print("DEBUG [INIT]: Volume Filter: ", (UseVolumeFilter ? "ENABLED" : "DISABLED"));
    Print("DEBUG [INIT]: ATR Filter: ", (UseATRFilter ? "ENABLED" : "DISABLED"));
    Print("DEBUG [INIT]: Multiple Targets: ", (UseMultipleTargets ? "ENABLED" : "DISABLED"));
    Print("DEBUG [INIT]: Dynamic Breakeven: ", (UseDynamicBreakeven ? "ENABLED" : "DISABLED"));
    
    if (UseMultipleTargets)
    {
        Print("DEBUG [INIT]: Target 1: ", FirstTargetRatio, "R (", FirstTargetPercent, "%)");
        Print("DEBUG [INIT]: Target 2: ", SecondTargetRatio, "R (", SecondTargetPercent, "%)");
        Print("DEBUG [INIT]: Target 3: ", FinalTargetRatio, "R (", 100-FirstTargetPercent-SecondTargetPercent, "%)");
    }
    
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(MaxSlippage);
    trade.SetTypeFillingBySymbol(_Symbol);
    Print("DEBUG [INIT]: Trade object configured successfully");

    // Initialize enhanced variables
    ResetPositionInfo();
    CalculateAverageVolume();
    CalculateAverageATR();

    // This ensures the session times are set correctly on the very first run
    lastTradeDay = TimeCurrent();
    Print("DEBUG [INIT]: Initial trade day set to: ", TimeToString(lastTradeDay));
    UpdateSessionTimings();
    Print("EA Initialized. Enhanced session tracking is active.");
    Print("==================================================");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("==================================================");
    Print("DEBUG [DEINIT]: FirstCandleBreakout Enhanced EA Deinitializing...");
    Print("DEBUG [DEINIT]: Reason code: ", reason);
    
    string reasonText = "";
    switch(reason)
    {
        case REASON_PROGRAM: reasonText = "EA terminated by user"; break;
        case REASON_REMOVE: reasonText = "EA removed from chart"; break;
        case REASON_RECOMPILE: reasonText = "EA recompiled"; break;
        case REASON_CHARTCHANGE: reasonText = "Chart symbol or period changed"; break;
        case REASON_CHARTCLOSE: reasonText = "Chart closed"; break;
        case REASON_PARAMETERS: reasonText = "Input parameters changed"; break;
        case REASON_ACCOUNT: reasonText = "Account changed"; break;
        case REASON_TEMPLATE: reasonText = "Template applied"; break;
        case REASON_INITFAILED: reasonText = "Initialization failed"; break;
        case REASON_CLOSE: reasonText = "Terminal closing"; break;
        default: reasonText = "Unknown reason"; break;
    }
    
    Print("DEBUG [DEINIT]: Reason: ", reasonText);
    Print("DEBUG [DEINIT]: Final state - firstCandleIdentified: ", firstCandleIdentified);
    Print("DEBUG [DEINIT]: Final state - tradePlacedForSession: ", tradePlacedForSession);
    
    // Check for any remaining orders or positions
    int pendingOrders = 0;
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderGetTicket(i) > 0 && OrderGetString(ORDER_SYMBOL) == _Symbol && OrderGetInteger(ORDER_MAGIC) == MagicNumber)
        {
            pendingOrders++;
        }
    }
    
    bool hasPosition = PositionSelect(_Symbol) && PositionGetInteger(POSITION_MAGIC) == MagicNumber;
    
    Print("DEBUG [DEINIT]: Pending orders remaining: ", pendingOrders);
    Print("DEBUG [DEINIT]: Position open: ", (hasPosition ? "YES" : "NO"));
    Print("DEBUG [DEINIT]: Deinitialization complete");
    Print("==================================================");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static int tickCount = 0;
    tickCount++;
    
    // Log every 100 ticks to avoid spam but show activity
    if (tickCount % 100 == 0)
    {
        Print("DEBUG [TICK]: Tick #", tickCount, " at ", TimeToString(TimeCurrent()));
    }

    // --- Daily Reset ---
    if (TimeCurrent() / (24 * 3600) != lastTradeDay / (24 * 3600))
    {
        Print("=== DEBUG [DAILY_RESET]: New day detected ===");
        Print("DEBUG [DAILY_RESET]: Previous day: ", TimeToString(lastTradeDay));
        Print("DEBUG [DAILY_RESET]: Current time: ", TimeToString(TimeCurrent()));
        Print("DEBUG [DAILY_RESET]: Resetting session variables...");
        
        lastTradeDay = TimeCurrent();
        tradePlacedForSession = false;
        firstCandleIdentified = false;
        ResetPositionInfo();
        
        // Recalculate averages for new day
        CalculateAverageVolume();
        CalculateAverageATR();
        
        Print("DEBUG [DAILY_RESET]: tradePlacedForSession = ", tradePlacedForSession);
        Print("DEBUG [DAILY_RESET]: firstCandleIdentified = ", firstCandleIdentified);
        
        UpdateSessionTimings();
        Print("=== DEBUG [DAILY_RESET]: Reset complete ===");
    }

    // --- Session Check ---
    bool sessionActive = IsTradeWindowActive();
    if (!sessionActive)
    {
        static datetime lastInactiveLog = 0;
        if (TimeCurrent() - lastInactiveLog > 3600) // Log every hour when inactive
        {
            Print("DEBUG [SESSION]: Outside trading window - managing existing trades only");
            lastInactiveLog = TimeCurrent();
        }
        ManageActiveTrades(); // Still manage trades outside the session window
        return;
    }
    else
    {
        static bool sessionStartLogged = false;
        if (!sessionStartLogged)
        {
            Print("=== DEBUG [SESSION]: Trading window is ACTIVE ===");
            sessionStartLogged = true;
        }
    }

    // --- First Candle Identification Logic ---
    if (!firstCandleIdentified)
    {
        Print("DEBUG [CANDLE]: Attempting to identify first candle...");
        if (!IdentifyFirstCandle())
        {
            Print("DEBUG [CANDLE]: First candle not yet identified, waiting...");
            return; // Wait for the next tick if candle not found yet
        }
        Print("DEBUG [CANDLE]: First candle successfully identified!");
    }

    // --- Enhanced Pre-Trade Logic (Run once per session) ---
    if (firstCandleIdentified && !tradePlacedForSession)
    {
        Print("=== DEBUG [TRADE_SETUP]: Starting enhanced trade setup process ===");
        Print("DEBUG [TRADE_SETUP]: First candle identified: ", firstCandleIdentified);
        Print("DEBUG [TRADE_SETUP]: Trade placed for session: ", tradePlacedForSession);
        
        if (!IsSpreadAcceptable())
        {
            Print("DEBUG [TRADE_SETUP]: Spread check FAILED - aborting trade setup");
            tradePlacedForSession = true;
            return;
        }
        Print("DEBUG [TRADE_SETUP]: Spread check PASSED");

        if (!PassesEnhancedFilters())
        {
            Print("DEBUG [TRADE_SETUP]: Enhanced filters FAILED - aborting trade setup");
            tradePlacedForSession = true;
            return;
        }
        Print("DEBUG [TRADE_SETUP]: Enhanced filters PASSED");

        double lotSize = CalculateLotSize();
        Print("DEBUG [TRADE_SETUP]: Calculated lot size: ", lotSize);
        
        if (lotSize <= 0)
        {
            Print("DEBUG [TRADE_SETUP]: Lot size calculation FAILED - aborting trade setup");
            tradePlacedForSession = true;
            return;
        }

        Print("DEBUG [TRADE_SETUP]: Proceeding to place enhanced pending orders...");
        PlaceEnhancedPendingOrders(lotSize);
        tradePlacedForSession = true;
        Print("=== DEBUG [TRADE_SETUP]: Enhanced trade setup process complete ===");
    }
    
    // --- Enhanced Active Trade Management ---
    ManageActiveTrades();
}

//+------------------------------------------------------------------+
//| TIME MANAGEMENT FUNCTIONS                                        |
//+------------------------------------------------------------------+
void UpdateSessionTimings()
{
    Print("=== DEBUG [SESSION_TIMING]: Updating session timings ===");

    long gmtOffset = TimeGMTOffset();
    int sessionStartHourGMT = (SessionToTrade == SESSION_LONDON) ? 8 : 13; // London 8:00 GMT, NY 13:00 GMT

    Print("DEBUG [SESSION_TIMING]: Selected session: ", (SessionToTrade == SESSION_LONDON ? "LONDON" : "NEW_YORK"));
    Print("DEBUG [SESSION_TIMING]: Session start hour GMT: ", sessionStartHourGMT, ":00");
    Print("DEBUG [SESSION_TIMING]: GMT Offset: ", gmtOffset, " seconds (", gmtOffset/3600.0, " hours)");

    // Get today's date at 00:00 GMT
    datetime today = TimeCurrent() - (TimeCurrent() % (24 * 3600));
    Print("DEBUG [SESSION_TIMING]: Today's date (00:00 GMT): ", TimeToString(today));

    // Calculate session start time in GMT, then convert to broker time
    datetime sessionStartGMT = today + sessionStartHourGMT * 3600;
    sessionStartTime = sessionStartGMT + gmtOffset; // Add GMT offset to convert to broker time
    sessionEndTime = sessionStartTime + 9 * 3600; // 9-hour session window

    Print("DEBUG [SESSION_TIMING]: Session Start GMT: ", TimeToString(sessionStartGMT));
    Print("DEBUG [SESSION_TIMING]: Session Start Broker Time: ", TimeToString(sessionStartTime));
    Print("DEBUG [SESSION_TIMING]: Session End Broker Time: ", TimeToString(sessionEndTime));
    Print("DEBUG [SESSION_TIMING]: Current Broker Time: ", TimeToString(TimeCurrent()));
    Print("=== DEBUG [SESSION_TIMING]: Session timing update complete ===");
}

bool IsTradeWindowActive()
{
    datetime currentTime = TimeCurrent();
    bool isActive = (currentTime >= sessionStartTime && currentTime < sessionEndTime);

    // Enhanced diagnostic logging
    static datetime lastLogTime = 0;
    static bool lastActiveState = false;

    // Log when state changes or every hour
    if(isActive != lastActiveState || currentTime - lastLogTime > 3600)
    {
        Print("=== DEBUG [TRADE_WINDOW]: Trade Window Status Check ===");
        Print("DEBUG [TRADE_WINDOW]: Current Time: ", TimeToString(currentTime));
        Print("DEBUG [TRADE_WINDOW]: Session Start: ", TimeToString(sessionStartTime));
        Print("DEBUG [TRADE_WINDOW]: Session End: ", TimeToString(sessionEndTime));
        Print("DEBUG [TRADE_WINDOW]: Window Active: ", (isActive ? "TRUE" : "FALSE"));

        if (isActive)
        {
            long timeInSession = currentTime - sessionStartTime;
            Print("DEBUG [TRADE_WINDOW]: Time in session: ", timeInSession/60, " minutes");
        }
        else
        {
            if (currentTime < sessionStartTime)
            {
                long timeToStart = sessionStartTime - currentTime;
                Print("DEBUG [TRADE_WINDOW]: Time until session start: ", timeToStart/60, " minutes");
            }
            else
            {
                long timeAfterEnd = currentTime - sessionEndTime;
                Print("DEBUG [TRADE_WINDOW]: Time after session end: ", timeAfterEnd/60, " minutes");
            }
        }

        lastLogTime = currentTime;
        lastActiveState = isActive;
        Print("=== DEBUG [TRADE_WINDOW]: Status check complete ===");
    }

    return isActive;
}

//+------------------------------------------------------------------+
//| ENHANCED HELPER FUNCTIONS                                        |
//+------------------------------------------------------------------+
void ResetPositionInfo()
{
    positionInfo.originalLotSize = 0;
    positionInfo.remainingLotSize = 0;
    positionInfo.firstTargetHit = false;
    positionInfo.secondTargetHit = false;
    positionInfo.movedToBreakeven = false;
    positionInfo.openTime = 0;
}

void CalculateAverageVolume()
{
    Print("=== DEBUG [VOLUME_CALC]: Calculating average volume ===");

    long volumes[];
    int volumesCopied = CopyTickVolume(_Symbol, PERIOD_M30, 1, 20, volumes);

    if (volumesCopied < 20)
    {
        Print("DEBUG [VOLUME_CALC]: WARNING - Only ", volumesCopied, " volume bars available");
        averageVolume = 1000; // Default fallback
        return;
    }

    long totalVolume = 0;
    for (int i = 0; i < volumesCopied; i++)
    {
        totalVolume += volumes[i];
    }

    averageVolume = (double)totalVolume / volumesCopied;
    Print("DEBUG [VOLUME_CALC]: Average volume over ", volumesCopied, " bars: ", averageVolume);
    Print("=== DEBUG [VOLUME_CALC]: Volume calculation complete ===");
}

void CalculateAverageATR()
{
    Print("=== DEBUG [ATR_CALC]: Calculating average ATR ===");

    double atrValues[];
    int atrHandle = iATR(_Symbol, PERIOD_M30, ATRPeriod);

    if (atrHandle == INVALID_HANDLE)
    {
        Print("DEBUG [ATR_CALC]: ERROR - Failed to create ATR indicator");
        averageATR = 0.001; // Default fallback
        return;
    }

    int atrCopied = CopyBuffer(atrHandle, 0, 1, 20, atrValues);

    if (atrCopied < 20)
    {
        Print("DEBUG [ATR_CALC]: WARNING - Only ", atrCopied, " ATR values available");
        averageATR = 0.001; // Default fallback
        IndicatorRelease(atrHandle);
        return;
    }

    double totalATR = 0;
    for (int i = 0; i < atrCopied; i++)
    {
        totalATR += atrValues[i];
    }

    averageATR = totalATR / atrCopied;

    // Get current ATR
    double currentATRArray[];
    if (CopyBuffer(atrHandle, 0, 0, 1, currentATRArray) > 0)
    {
        currentATR = currentATRArray[0];
    }

    Print("DEBUG [ATR_CALC]: Average ATR over ", atrCopied, " bars: ", averageATR);
    Print("DEBUG [ATR_CALC]: Current ATR: ", currentATR);
    Print("=== DEBUG [ATR_CALC]: ATR calculation complete ===");

    IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| ENHANCED CORE STRATEGY FUNCTIONS                                |
//+------------------------------------------------------------------+
bool IdentifyFirstCandle()
{
    Print("=== DEBUG [FIRST_CANDLE]: Starting enhanced first candle identification ===");

    MqlRates rates[];
    int ratesCopied = CopyRates(_Symbol, PERIOD_M30, 0, 10, rates);

    Print("DEBUG [FIRST_CANDLE]: Requested 10 M30 candles, copied: ", ratesCopied);

    if(ratesCopied < 10)
    {
        Print("DEBUG [FIRST_CANDLE]: ERROR - Could not copy sufficient M30 rates");
        Print("DEBUG [FIRST_CANDLE]: Only ", ratesCopied, " candles available");
        return false;
    }

    Print("DEBUG [FIRST_CANDLE]: Session start time: ", TimeToString(sessionStartTime));
    Print("DEBUG [FIRST_CANDLE]: Current time: ", TimeToString(TimeCurrent()));

    // Look for the first complete candle that starts at or after the session start time
    for(int i = 1; i < 10; i++) // Start from index 1 (most recent closed candle)
    {
        datetime candleOpenTime = rates[i].time;
        datetime candleCloseTime = candleOpenTime + 30 * 60; // 30 minutes later

        Print("DEBUG [FIRST_CANDLE]: Checking candle #", i);
        Print("DEBUG [FIRST_CANDLE]: Candle open time: ", TimeToString(candleOpenTime));
        Print("DEBUG [FIRST_CANDLE]: Candle close time: ", TimeToString(candleCloseTime));
        Print("DEBUG [FIRST_CANDLE]: Candle OHLC: O=", rates[i].open, " H=", rates[i].high,
              " L=", rates[i].low, " C=", rates[i].close);

        bool startsAfterSession = (candleOpenTime >= sessionStartTime);
        bool isComplete = (TimeCurrent() >= candleCloseTime);

        Print("DEBUG [FIRST_CANDLE]: Starts after session: ", startsAfterSession);
        Print("DEBUG [FIRST_CANDLE]: Is complete: ", isComplete);

        // Check if this candle starts at or after session start and is complete
        if (startsAfterSession && isComplete)
        {
            firstCandleHigh = rates[i].high;
            firstCandleLow = rates[i].low;
            firstCandleIdentified = true;

            // Get volume for this candle
            long volumes[];
            if (CopyTickVolume(_Symbol, PERIOD_M30, i, 1, volumes) > 0)
            {
                firstCandleVolume = (double)volumes[0];
            }
            else
            {
                firstCandleVolume = 0;
                Print("DEBUG [FIRST_CANDLE]: WARNING - Could not get volume data");
            }

            double candleRangePips = (firstCandleHigh - firstCandleLow) / (_Point * 10);

            Print("=== DEBUG [FIRST_CANDLE]: FIRST CANDLE IDENTIFIED! ===");
            Print("DEBUG [FIRST_CANDLE]: Candle time: ", TimeToString(candleOpenTime));
            Print("DEBUG [FIRST_CANDLE]: High: ", NormalizeDouble(firstCandleHigh, _Digits));
            Print("DEBUG [FIRST_CANDLE]: Low: ", NormalizeDouble(firstCandleLow, _Digits));
            Print("DEBUG [FIRST_CANDLE]: Range: ", NormalizeDouble(candleRangePips, 1), " pips");
            Print("DEBUG [FIRST_CANDLE]: Volume: ", firstCandleVolume);
            Print("=== DEBUG [FIRST_CANDLE]: Identification complete ===");
            return true;
        }
        else
        {
            Print("DEBUG [FIRST_CANDLE]: Candle #", i, " does not meet criteria");
        }
    }

    Print("DEBUG [FIRST_CANDLE]: No suitable first candle found yet");
    Print("=== DEBUG [FIRST_CANDLE]: Identification attempt complete ===");
    return false;
}

bool PassesEnhancedFilters()
{
    Print("=== DEBUG [ENHANCED_FILTERS]: Starting enhanced filter checks ===");

    bool allFiltersPassed = true;

    // Volume Filter
    if (UseVolumeFilter)
    {
        Print("--- DEBUG [VOLUME_FILTER]: Checking volume filter ---");
        Print("DEBUG [VOLUME_FILTER]: First candle volume: ", firstCandleVolume);
        Print("DEBUG [VOLUME_FILTER]: Average volume: ", averageVolume);
        Print("DEBUG [VOLUME_FILTER]: Required multiplier: ", VolumeMultiplier);

        double requiredVolume = averageVolume * VolumeMultiplier;
        bool volumePassed = (firstCandleVolume >= requiredVolume);

        Print("DEBUG [VOLUME_FILTER]: Required volume: ", requiredVolume);
        Print("DEBUG [VOLUME_FILTER]: Volume filter: ", (volumePassed ? "PASSED" : "FAILED"));

        if (!volumePassed)
        {
            allFiltersPassed = false;
            Print("DEBUG [VOLUME_FILTER]: Volume too low - rejecting trade");
        }
    }
    else
    {
        Print("DEBUG [ENHANCED_FILTERS]: Volume filter DISABLED");
    }

    // ATR Filter
    if (UseATRFilter)
    {
        Print("--- DEBUG [ATR_FILTER]: Checking ATR filter ---");
        Print("DEBUG [ATR_FILTER]: Current ATR: ", currentATR);
        Print("DEBUG [ATR_FILTER]: Average ATR: ", averageATR);
        Print("DEBUG [ATR_FILTER]: Min multiplier: ", MinATRMultiplier);
        Print("DEBUG [ATR_FILTER]: Max multiplier: ", MaxATRMultiplier);

        double minATR = averageATR * MinATRMultiplier;
        double maxATR = averageATR * MaxATRMultiplier;
        bool atrPassed = (currentATR >= minATR && currentATR <= maxATR);

        Print("DEBUG [ATR_FILTER]: Min required ATR: ", minATR);
        Print("DEBUG [ATR_FILTER]: Max allowed ATR: ", maxATR);
        Print("DEBUG [ATR_FILTER]: ATR filter: ", (atrPassed ? "PASSED" : "FAILED"));

        if (!atrPassed)
        {
            allFiltersPassed = false;
            if (currentATR < minATR)
                Print("DEBUG [ATR_FILTER]: ATR too low - market not volatile enough");
            else
                Print("DEBUG [ATR_FILTER]: ATR too high - market too volatile");
        }
    }
    else
    {
        Print("DEBUG [ENHANCED_FILTERS]: ATR filter DISABLED");
    }

    Print("DEBUG [ENHANCED_FILTERS]: All filters passed: ", (allFiltersPassed ? "YES" : "NO"));
    Print("=== DEBUG [ENHANCED_FILTERS]: Enhanced filter checks complete ===");

    return allFiltersPassed;
}

bool IsSpreadAcceptable()
{
    Print("=== DEBUG [SPREAD_CHECK]: Checking spread conditions ===");

    long spreadPoints = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD);
    double currentSpread = spreadPoints * _Point;
    double currentSpreadPips = currentSpread / (10 * _Point);
    double maxSpreadPips = MaxSpread;

    Print("DEBUG [SPREAD_CHECK]: Current spread (points): ", spreadPoints);
    Print("DEBUG [SPREAD_CHECK]: Current spread (pips): ", NormalizeDouble(currentSpreadPips, 1));
    Print("DEBUG [SPREAD_CHECK]: Maximum allowed spread (pips): ", maxSpreadPips);

    bool acceptable = (currentSpreadPips <= maxSpreadPips);

    Print("DEBUG [SPREAD_CHECK]: Spread acceptable: ", (acceptable ? "YES" : "NO"));

    if (!acceptable)
    {
        Print("DEBUG [SPREAD_CHECK]: SPREAD CHECK FAILED!");
        Print("DEBUG [SPREAD_CHECK]: Current: ", NormalizeDouble(currentSpreadPips, 1),
              " pips > Max allowed: ", maxSpreadPips, " pips");
    }

    Print("=== DEBUG [SPREAD_CHECK]: Spread check complete ===");
    return acceptable;
}

double CalculateLotSize()
{
    Print("=== DEBUG [LOT_CALC]: Starting lot size calculation ===");

    double accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    double riskAmount = accountEquity * (RiskPercent / 100.0);
    double stopLossPips = (firstCandleHigh - firstCandleLow) / (_Point * 10);

    Print("DEBUG [LOT_CALC]: Account equity: ", accountEquity);
    Print("DEBUG [LOT_CALC]: Risk percentage: ", RiskPercent, "%");
    Print("DEBUG [LOT_CALC]: Risk amount: ", riskAmount);
    Print("DEBUG [LOT_CALC]: First candle high: ", firstCandleHigh);
    Print("DEBUG [LOT_CALC]: First candle low: ", firstCandleLow);
    Print("DEBUG [LOT_CALC]: Stop loss distance: ", NormalizeDouble(stopLossPips, 1), " pips");

    if (stopLossPips <= 0)
    {
        Print("DEBUG [LOT_CALC]: ERROR - Stop loss distance is zero or negative!");
        return 0;
    }

    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    Print("DEBUG [LOT_CALC]: Symbol digits: ", _Digits);
    Print("DEBUG [LOT_CALC]: Point value: ", _Point);
    Print("DEBUG [LOT_CALC]: Tick value: ", tickValue);
    Print("DEBUG [LOT_CALC]: Tick size: ", tickSize);

    // Correctly determine pip size based on symbol's digits
    double pointValue = _Point;
    if(_Digits == 3 || _Digits == 5)
    {
        pointValue = _Point * 10;
        Print("DEBUG [LOT_CALC]: Using 10x point value for 3/5 digit symbol");
    }

    double valuePerPip = tickValue * (pointValue / tickSize);
    Print("DEBUG [LOT_CALC]: Value per pip: ", valuePerPip);

    double riskPerLot = stopLossPips * valuePerPip;
    double lotSize = (riskPerLot > 0) ? riskAmount / riskPerLot : 0;

    Print("DEBUG [LOT_CALC]: Risk per lot: ", riskPerLot);
    Print("DEBUG [LOT_CALC]: Initial lot size calculation: ", lotSize);

    // Get symbol constraints
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    Print("DEBUG [LOT_CALC]: Symbol constraints - Min: ", minLot, ", Max: ", maxLot, ", Step: ", lotStep);

    // Normalize lot size to step
    lotSize = floor(lotSize / lotStep) * lotStep;
    Print("DEBUG [LOT_CALC]: Lot size after step normalization: ", lotSize);

    // Apply constraints
    if (lotSize < minLot)
    {
        Print("DEBUG [LOT_CALC]: Lot size below minimum, adjusting from ", lotSize, " to ", minLot);
        lotSize = minLot;
    }
    if (lotSize > maxLot)
    {
        Print("DEBUG [LOT_CALC]: Lot size above maximum, adjusting from ", lotSize, " to ", maxLot);
        lotSize = maxLot;
    }

    // Final risk validation
    double actualRisk = lotSize * riskPerLot;
    double actualRiskPercent = (actualRisk / accountEquity) * 100;

    Print("DEBUG [LOT_CALC]: Final lot size: ", lotSize);
    Print("DEBUG [LOT_CALC]: Actual risk amount: ", actualRisk);
    Print("DEBUG [LOT_CALC]: Actual risk percentage: ", NormalizeDouble(actualRiskPercent, 2), "%");

    if(actualRiskPercent > RiskPercent && lotSize == minLot)
    {
        Print("DEBUG [LOT_CALC]: WARNING - Minimum lot size exceeds desired risk percentage!");
    }

    Print("=== DEBUG [LOT_CALC]: Lot size calculation complete ===");
    return lotSize;
}

void PlaceEnhancedPendingOrders(double lotSize)
{
    Print("=== DEBUG [ENHANCED_ORDER]: Starting enhanced pending order placement ===");

    double point = _Point;
    double offset = OrderOffsetPips * 10 * point;

    Print("DEBUG [ENHANCED_ORDER]: Lot size: ", lotSize);
    Print("DEBUG [ENHANCED_ORDER]: Point value: ", point);
    Print("DEBUG [ENHANCED_ORDER]: Offset pips: ", OrderOffsetPips);
    Print("DEBUG [ENHANCED_ORDER]: Offset price: ", offset);

    // Store original lot size for position management
    positionInfo.originalLotSize = lotSize;
    positionInfo.remainingLotSize = lotSize;

    // Calculate target levels
    double buyPrice = NormalizeDouble(firstCandleHigh + offset, _Digits);
    double buySL = NormalizeDouble(firstCandleLow, _Digits);
    double stopDistance = buyPrice - buySL;

    double sellPrice = NormalizeDouble(firstCandleLow - offset, _Digits);
    double sellSL = NormalizeDouble(firstCandleHigh, _Digits);
    double sellStopDistance = sellSL - sellPrice;

    Print("DEBUG [ENHANCED_ORDER]: Buy setup - Price: ", buyPrice, ", SL: ", buySL, ", Distance: ", NormalizeDouble(stopDistance / (_Point * 10), 1), " pips");
    Print("DEBUG [ENHANCED_ORDER]: Sell setup - Price: ", sellPrice, ", SL: ", sellSL, ", Distance: ", NormalizeDouble(sellStopDistance / (_Point * 10), 1), " pips");

    // Calculate multiple target levels for buy
    double buyTP1 = 0, buyTP2 = 0, buyTP3 = 0;
    if (UseMultipleTargets)
    {
        buyTP1 = NormalizeDouble(buyPrice + (stopDistance * FirstTargetRatio), _Digits);
        buyTP2 = NormalizeDouble(buyPrice + (stopDistance * SecondTargetRatio), _Digits);
        buyTP3 = NormalizeDouble(buyPrice + (stopDistance * FinalTargetRatio), _Digits);

        Print("DEBUG [ENHANCED_ORDER]: Buy targets - TP1: ", buyTP1, " (", FirstTargetRatio, "R)");
        Print("DEBUG [ENHANCED_ORDER]: Buy targets - TP2: ", buyTP2, " (", SecondTargetRatio, "R)");
        Print("DEBUG [ENHANCED_ORDER]: Buy targets - TP3: ", buyTP3, " (", FinalTargetRatio, "R)");
    }
    else
    {
        buyTP3 = NormalizeDouble(buyPrice + (stopDistance * RiskRewardRatio), _Digits);
        Print("DEBUG [ENHANCED_ORDER]: Buy single target: ", buyTP3, " (", RiskRewardRatio, "R)");
    }

    // Calculate multiple target levels for sell
    double sellTP1 = 0, sellTP2 = 0, sellTP3 = 0;
    if (UseMultipleTargets)
    {
        sellTP1 = NormalizeDouble(sellPrice - (sellStopDistance * FirstTargetRatio), _Digits);
        sellTP2 = NormalizeDouble(sellPrice - (sellStopDistance * SecondTargetRatio), _Digits);
        sellTP3 = NormalizeDouble(sellPrice - (sellStopDistance * FinalTargetRatio), _Digits);

        Print("DEBUG [ENHANCED_ORDER]: Sell targets - TP1: ", sellTP1, " (", FirstTargetRatio, "R)");
        Print("DEBUG [ENHANCED_ORDER]: Sell targets - TP2: ", sellTP2, " (", SecondTargetRatio, "R)");
        Print("DEBUG [ENHANCED_ORDER]: Sell targets - TP3: ", sellTP3, " (", FinalTargetRatio, "R)");
    }
    else
    {
        sellTP3 = NormalizeDouble(sellPrice - (sellStopDistance * RiskRewardRatio), _Digits);
        Print("DEBUG [ENHANCED_ORDER]: Sell single target: ", sellTP3, " (", RiskRewardRatio, "R)");
    }

    // Place Buy Stop Order (using final target as TP, will manage others manually)
    Print("DEBUG [ENHANCED_ORDER]: Placing BUY STOP order...");
    bool buyResult = trade.BuyStop(lotSize, buyPrice, _Symbol, buySL, buyTP3, 0, 0, "FCB_Enhanced_Buy");

    if (buyResult)
    {
        Print("DEBUG [ENHANCED_ORDER]: BUY STOP order placed successfully");
    }
    else
    {
        Print("DEBUG [ENHANCED_ORDER]: ERROR - BUY STOP order failed!");
        Print("DEBUG [ENHANCED_ORDER]: Error code: ", trade.ResultRetcode());
        Print("DEBUG [ENHANCED_ORDER]: Error description: ", trade.ResultRetcodeDescription());
    }

    // Place Sell Stop Order (using final target as TP, will manage others manually)
    Print("DEBUG [ENHANCED_ORDER]: Placing SELL STOP order...");
    bool sellResult = trade.SellStop(lotSize, sellPrice, _Symbol, sellSL, sellTP3, 0, 0, "FCB_Enhanced_Sell");

    if (sellResult)
    {
        Print("DEBUG [ENHANCED_ORDER]: SELL STOP order placed successfully");
    }
    else
    {
        Print("DEBUG [ENHANCED_ORDER]: ERROR - SELL STOP order failed!");
        Print("DEBUG [ENHANCED_ORDER]: Error code: ", trade.ResultRetcode());
        Print("DEBUG [ENHANCED_ORDER]: Error description: ", trade.ResultRetcodeDescription());
    }

    Print("=== DEBUG [ENHANCED_ORDER]: Enhanced pending order placement complete ===");
}

//+------------------------------------------------------------------+
//| ENHANCED TRADE MANAGEMENT FUNCTIONS                             |
//+------------------------------------------------------------------+
void ManageActiveTrades()
{
    static datetime lastManagementLog = 0;
    bool shouldLog = (TimeCurrent() - lastManagementLog > 300); // Log every 5 minutes

    if (shouldLog)
    {
        Print("=== DEBUG [ENHANCED_MGMT]: Starting enhanced trade management cycle ===");
        lastManagementLog = TimeCurrent();
    }

    // --- Enhanced OCO (One-Cancels-Other) Logic ---
    bool positionExists = PositionSelect(_Symbol);

    if (shouldLog)
    {
        Print("DEBUG [ENHANCED_MGMT]: Position exists: ", (positionExists ? "YES" : "NO"));
    }

    if (positionExists)
    {
        ulong positionMagic = PositionGetInteger(POSITION_MAGIC);

        if (shouldLog)
        {
            Print("DEBUG [ENHANCED_MGMT]: Position magic: ", positionMagic, " (Expected: ", MagicNumber, ")");
        }

        if(positionMagic == MagicNumber)
        {
            // Initialize position info if not already done
            if (positionInfo.openTime == 0)
            {
                positionInfo.openTime = (datetime)PositionGetInteger(POSITION_TIME);
                positionInfo.remainingLotSize = PositionGetDouble(POSITION_VOLUME);
                Print("DEBUG [ENHANCED_MGMT]: Position info initialized - Open time: ", TimeToString(positionInfo.openTime));
            }

            if (shouldLog)
            {
                Print("DEBUG [ENHANCED_MGMT]: Our position detected - checking for pending orders to cancel");
            }

            int ordersTotal = OrdersTotal();
            int ordersCancelled = 0;

            // A position is open, cancel any remaining pending orders
            for (int i = ordersTotal - 1; i >= 0; i--)
            {
                if (OrderGetTicket(i) > 0 && OrderGetString(ORDER_SYMBOL) == _Symbol && OrderGetInteger(ORDER_MAGIC) == MagicNumber)
                {
                    ulong orderTicket = OrderGetTicket(i);
                    Print("DEBUG [ENHANCED_MGMT]: Active position detected. Deleting opposing pending order #", orderTicket);

                    bool deleteResult = trade.OrderDelete(orderTicket);
                    if (deleteResult)
                    {
                        Print("DEBUG [ENHANCED_MGMT]: Order #", orderTicket, " deleted successfully");
                        ordersCancelled++;
                    }
                    else
                    {
                        Print("DEBUG [ENHANCED_MGMT]: ERROR - Failed to delete order #", orderTicket);
                        Print("DEBUG [ENHANCED_MGMT]: Error code: ", trade.ResultRetcode());
                    }
                }
            }

            if (shouldLog)
            {
                Print("DEBUG [ENHANCED_MGMT]: Orders cancelled: ", ordersCancelled);
            }

            // Enhanced position management
            ManageEnhancedPosition();
        }
    }
    else
    {
        // Reset position info when no position exists
        if (positionInfo.openTime != 0)
        {
            Print("DEBUG [ENHANCED_MGMT]: Position closed - resetting position info");
            ResetPositionInfo();
        }
    }

    if (shouldLog)
    {
        Print("=== DEBUG [ENHANCED_MGMT]: Enhanced trade management cycle complete ===");
    }
}

void ManageEnhancedPosition()
{
    if (!PositionSelect(_Symbol) || PositionGetInteger(POSITION_MAGIC) != MagicNumber)
        return;

    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double currentSL = PositionGetDouble(POSITION_SL);
    double currentTP = PositionGetDouble(POSITION_TP);
    long positionType = PositionGetInteger(POSITION_TYPE);
    double positionVolume = PositionGetDouble(POSITION_VOLUME);
    double currentPrice = (positionType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    static datetime lastDetailedLog = 0;
    bool shouldLogDetails = (TimeCurrent() - lastDetailedLog > 600); // Log details every 10 minutes

    if (shouldLogDetails)
    {
        Print("--- DEBUG [ENHANCED_POS]: Enhanced Position Details ---");
        Print("DEBUG [ENHANCED_POS]: Type: ", (positionType == POSITION_TYPE_BUY ? "BUY" : "SELL"));
        Print("DEBUG [ENHANCED_POS]: Volume: ", positionVolume);
        Print("DEBUG [ENHANCED_POS]: Open Price: ", openPrice);
        Print("DEBUG [ENHANCED_POS]: Current Price: ", currentPrice);
        Print("DEBUG [ENHANCED_POS]: Current SL: ", currentSL);
        Print("DEBUG [ENHANCED_POS]: Current TP: ", currentTP);
        Print("DEBUG [ENHANCED_POS]: Remaining lot size: ", positionInfo.remainingLotSize);
        lastDetailedLog = TimeCurrent();
    }

    // Calculate current profit in R multiples
    double stopDistance = (positionType == POSITION_TYPE_BUY) ? (openPrice - currentSL) : (currentSL - openPrice);
    double currentProfit = (positionType == POSITION_TYPE_BUY) ? (currentPrice - openPrice) : (openPrice - currentPrice);
    double currentRRatio = (stopDistance > 0) ? (currentProfit / stopDistance) : 0;

    if (shouldLogDetails)
    {
        Print("DEBUG [ENHANCED_POS]: Current profit: ", NormalizeDouble(currentProfit / (_Point * 10), 1), " pips");
        Print("DEBUG [ENHANCED_POS]: Current R ratio: ", NormalizeDouble(currentRRatio, 2), "R");
    }

    // Time-based exit
    if (MaxTradeHours > 0)
    {
        long hoursOpen = (TimeCurrent() - positionInfo.openTime) / 3600;
        if (hoursOpen >= MaxTradeHours)
        {
            Print("DEBUG [ENHANCED_POS]: TIME EXIT TRIGGERED - Position open for ", hoursOpen, " hours");
            Print("DEBUG [ENHANCED_POS]: Closing position due to time limit");
            trade.PositionClose(_Symbol);
            return;
        }
    }

    // Multiple profit targets management
    if (UseMultipleTargets)
    {
        ManageMultipleProfitTargets(currentRRatio, positionType);
    }

    // Enhanced breakeven logic
    if (UseDynamicBreakeven && !positionInfo.movedToBreakeven)
    {
        if (currentRRatio >= BreakevenAtRatio)
        {
            Print("DEBUG [ENHANCED_POS]: DYNAMIC BREAKEVEN TRIGGERED at ", NormalizeDouble(currentRRatio, 2), "R");
            Print("DEBUG [ENHANCED_POS]: Moving SL from ", currentSL, " to open price: ", openPrice);

            bool modifyResult = trade.PositionModify(_Symbol, openPrice, currentTP);
            if (modifyResult)
            {
                Print("DEBUG [ENHANCED_POS]: Dynamic breakeven modification successful");
                positionInfo.movedToBreakeven = true;
            }
            else
            {
                Print("DEBUG [ENHANCED_POS]: ERROR - Dynamic breakeven modification failed!");
                Print("DEBUG [ENHANCED_POS]: Error code: ", trade.ResultRetcode());
            }
        }
    }

    // Enhanced trailing stop
    if (TrailingStopPips > 0 && positionInfo.movedToBreakeven)
    {
        ManageEnhancedTrailingStop(positionType, currentPrice, openPrice, currentSL, currentTP);
    }
}

void ManageMultipleProfitTargets(double currentRRatio, long positionType)
{
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    // First target
    if (!positionInfo.firstTargetHit && currentRRatio >= FirstTargetRatio)
    {
        double closeVolume = NormalizeDouble((positionInfo.originalLotSize * FirstTargetPercent / 100.0), 2);
        closeVolume = MathMax(closeVolume, minLot);
        closeVolume = floor(closeVolume / lotStep) * lotStep;

        if (closeVolume <= positionInfo.remainingLotSize)
        {
            Print("DEBUG [MULTI_TARGET]: FIRST TARGET HIT at ", NormalizeDouble(currentRRatio, 2), "R");
            Print("DEBUG [MULTI_TARGET]: Closing ", closeVolume, " lots (", FirstTargetPercent, "%)");

            bool closeResult = trade.PositionClosePartial(_Symbol, closeVolume);
            if (closeResult)
            {
                positionInfo.firstTargetHit = true;
                positionInfo.remainingLotSize -= closeVolume;
                Print("DEBUG [MULTI_TARGET]: First target closure successful. Remaining: ", positionInfo.remainingLotSize);
            }
            else
            {
                Print("DEBUG [MULTI_TARGET]: ERROR - First target closure failed!");
                Print("DEBUG [MULTI_TARGET]: Error code: ", trade.ResultRetcode());
            }
        }
    }

    // Second target
    if (!positionInfo.secondTargetHit && positionInfo.firstTargetHit && currentRRatio >= SecondTargetRatio)
    {
        double closeVolume = NormalizeDouble((positionInfo.originalLotSize * SecondTargetPercent / 100.0), 2);
        closeVolume = MathMax(closeVolume, minLot);
        closeVolume = floor(closeVolume / lotStep) * lotStep;

        if (closeVolume <= positionInfo.remainingLotSize)
        {
            Print("DEBUG [MULTI_TARGET]: SECOND TARGET HIT at ", NormalizeDouble(currentRRatio, 2), "R");
            Print("DEBUG [MULTI_TARGET]: Closing ", closeVolume, " lots (", SecondTargetPercent, "%)");

            bool closeResult = trade.PositionClosePartial(_Symbol, closeVolume);
            if (closeResult)
            {
                positionInfo.secondTargetHit = true;
                positionInfo.remainingLotSize -= closeVolume;
                Print("DEBUG [MULTI_TARGET]: Second target closure successful. Remaining: ", positionInfo.remainingLotSize);
            }
            else
            {
                Print("DEBUG [MULTI_TARGET]: ERROR - Second target closure failed!");
                Print("DEBUG [MULTI_TARGET]: Error code: ", trade.ResultRetcode());
            }
        }
    }
}

void ManageEnhancedTrailingStop(long positionType, double currentPrice, double openPrice, double currentSL, double currentTP)
{
    double newSL = 0;
    bool shouldModify = false;

    static datetime lastTrailingLog = 0;
    bool shouldLog = (TimeCurrent() - lastTrailingLog > 300); // Log every 5 minutes

    if (positionType == POSITION_TYPE_BUY)
    {
        newSL = NormalizeDouble(currentPrice - (TrailingStopPips * 10 * _Point), _Digits);
        shouldModify = (newSL > currentSL) && (newSL > openPrice);

        if (shouldLog)
        {
            Print("DEBUG [ENHANCED_TRAIL]: BUY Trailing - New SL: ", newSL, ", Current SL: ", currentSL);
            Print("DEBUG [ENHANCED_TRAIL]: Should modify: ", shouldModify);
            lastTrailingLog = TimeCurrent();
        }

        if (shouldModify)
        {
            Print("DEBUG [ENHANCED_TRAIL]: ENHANCED TRAILING STOP TRIGGERED for BUY");
            Print("DEBUG [ENHANCED_TRAIL]: Moving SL from ", currentSL, " to ", newSL);

            bool modifyResult = trade.PositionModify(_Symbol, newSL, currentTP);
            if (modifyResult)
            {
                Print("DEBUG [ENHANCED_TRAIL]: Enhanced trailing stop modification successful");
            }
            else
            {
                Print("DEBUG [ENHANCED_TRAIL]: ERROR - Enhanced trailing stop modification failed!");
                Print("DEBUG [ENHANCED_TRAIL]: Error code: ", trade.ResultRetcode());
            }
        }
    }
    else // SELL
    {
        newSL = NormalizeDouble(currentPrice + (TrailingStopPips * 10 * _Point), _Digits);
        shouldModify = (newSL < currentSL) && (newSL < openPrice);

        if (shouldLog)
        {
            Print("DEBUG [ENHANCED_TRAIL]: SELL Trailing - New SL: ", newSL, ", Current SL: ", currentSL);
            Print("DEBUG [ENHANCED_TRAIL]: Should modify: ", shouldModify);
            lastTrailingLog = TimeCurrent();
        }

        if (shouldModify)
        {
            Print("DEBUG [ENHANCED_TRAIL]: ENHANCED TRAILING STOP TRIGGERED for SELL");
            Print("DEBUG [ENHANCED_TRAIL]: Moving SL from ", currentSL, " to ", newSL);

            bool modifyResult = trade.PositionModify(_Symbol, newSL, currentTP);
            if (modifyResult)
            {
                Print("DEBUG [ENHANCED_TRAIL]: Enhanced trailing stop modification successful");
            }
            else
            {
                Print("DEBUG [ENHANCED_TRAIL]: ERROR - Enhanced trailing stop modification failed!");
                Print("DEBUG [ENHANCED_TRAIL]: Error code: ", trade.ResultRetcode());
            }
        }
    }
}
//+------------------------------------------------------------------+
