//+------------------------------------------------------------------+
//|                                        FirstCandleBreakout.mq5 |
//|                      Copyright 2025, Roo - AI Software Engineer |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Roo - AI Software Engineer"
#property link      ""
#property version   "1.00"
#property strict

#include <Trade\Trade.mqh>

//--- Custom Enumerations
enum ENUM_SESSION
{
    SESSION_LONDON,
    SESSION_NEW_YORK
};

//--- EA Input Parameters
//--- Session and Magic Number
input group           "Session Settings"
input ENUM_SESSION    SessionToTrade      = SESSION_LONDON; // Session to trade (London or New York)
input ulong           MagicNumber         = 12345;          // Unique ID for EA's trades

//--- Risk Management
input group           "Risk Management"
input double          RiskPercent         = 1.0;            // Risk percentage of account equity per trade
input double          MaxSpread           = 2.0;            // Maximum allowed spread in pips
input double          RiskRewardRatio     = 1.5;            // Risk-to-Reward ratio for Take Profit

//--- Order Settings
input group           "Order Settings"
input double          OrderOffsetPips     = 1.0;            // Pips to offset pending orders from the candle's high/low
input int             MaxSlippage         = 3;              // Maximum allowed slippage in points

//--- Trade Management
input group           "Trade Management"
input int             BreakevenTriggerPips = 20;             // Pips in profit to move SL to breakeven (0 to disable)
input int             TrailingStopPips     = 15;             // Trailing stop distance in pips (0 to disable)

//--- Global Variables
//--- Session Timing
datetime sessionStartTime;
datetime sessionEndTime;
int      lastTradeDay = 0;

//--- First Candle Data
double   firstCandleHigh = 0;
double   firstCandleLow = 0;
bool     firstCandleIdentified = false;

//--- Trade State
bool     tradePlacedForSession = false;

//--- MQL5 Objects
CTrade   trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("==================================================");
    Print("FirstCandleBreakout EA v1.00 Initializing...");
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(MaxSlippage);
    trade.SetTypeFillingBySymbol(_Symbol);

    // This ensures the session times are set correctly on the very first run
    lastTradeDay = TimeCurrent();
    UpdateSessionTimings();
    Print("EA Initialized. Session tracking is active.");
    Print("==================================================");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("FirstCandleBreakout EA Deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // --- Daily Reset ---
    if (TimeCurrent() / (24 * 3600) != lastTradeDay / (24 * 3600))
    {
        Print("New day detected. Resetting session variables.");
        lastTradeDay = TimeCurrent();
        tradePlacedForSession = false;
        firstCandleIdentified = false;
        UpdateSessionTimings(); // This will now correctly set for the new day
    }

    // --- Session Check ---
    if (!IsTradeWindowActive())
    {
        ManageActiveTrades(); // Still manage trades outside the session window
        return;
    }

    // --- First Candle Identification Logic ---
    if (!firstCandleIdentified)
    {
        if (!IdentifyFirstCandle())
            return; // Wait for the next tick if candle not found yet
    }

    // --- Pre-Trade Logic (Run once per session) ---
    if (firstCandleIdentified && !tradePlacedForSession)
    {
        if (!IsSpreadAcceptable())
        {
            Print("Spread is too high. No trade will be placed for this session.");
            tradePlacedForSession = true; // Abort for this session
            return;
        }

        double lotSize = CalculateLotSize();
        if (lotSize <= 0)
        {
            Print("Lot size calculation failed or resulted in 0. Aborting trade.");
            tradePlacedForSession = true; // Abort for this session
            return;
        }

        PlacePendingOrders(lotSize);
        tradePlacedForSession = true; // Mark as placed to prevent duplicates
    }
    
    // --- Active Trade Management ---
    ManageActiveTrades();
}

//+------------------------------------------------------------------+
//| TIME MANAGEMENT FUNCTIONS                                        |
//+------------------------------------------------------------------+
void UpdateSessionTimings()
{
    long gmtOffset = TimeGMTOffset();
    int sessionStartHourGMT = (SessionToTrade == SESSION_LONDON) ? 8 : 13; // London 8:00 GMT, NY 13:00 GMT
    
    datetime today = TimeCurrent() - (TimeCurrent() % (24 * 3600));
    sessionStartTime = today + sessionStartHourGMT * 3600 - gmtOffset;
    sessionEndTime = sessionStartTime + 9 * 3600; // 9-hour session window

    Print("Session times updated for today: ", TimeToString(sessionStartTime), " to ", TimeToString(sessionEndTime), " (Broker Time)");
}

bool IsTradeWindowActive()
{
    bool isActive = (TimeCurrent() >= sessionStartTime && TimeCurrent() < sessionEndTime);
    // Diagnostic Log
    if(isActive) Print("Debug: IsTradeWindowActive = TRUE. CurrentTime: ", TimeToString(TimeCurrent()), " | SessionStart: ", TimeToString(sessionStartTime));
    return isActive;
}

//+------------------------------------------------------------------+
//| CORE STRATEGY FUNCTIONS                                          |
//+------------------------------------------------------------------+
bool IdentifyFirstCandle()
{
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_M30, 0, 3, rates) < 3)
    {
        Print("Error: Could not copy M30 rates.");
        return false;
    }

    // We look at the most recently closed candle (index 1)
    datetime candleOpenTime = rates[1].time;
    datetime prevCandleOpenTime = rates[2].time;

    if (candleOpenTime >= sessionStartTime && prevCandleOpenTime < sessionStartTime)
    {
        firstCandleHigh = rates[1].high;
        firstCandleLow = rates[1].low;
        firstCandleIdentified = true;
        Print("First M30 candle of the session identified at ", TimeToString(candleOpenTime));
        Print("High: ", NormalizeDouble(firstCandleHigh, _Digits), ", Low: ", NormalizeDouble(firstCandleLow, _Digits));
        return true;
    }
    return false;
}

bool IsSpreadAcceptable()
{
    double currentSpread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * _Point;
    double maxSpreadPips = MaxSpread * 10 * _Point;
    if (currentSpread > maxSpreadPips)
    {
        Print("Spread check failed. Current spread: ", currentSpread / (10 * _Point), " pips. Max allowed: ", MaxSpread, " pips.");
        return false;
    }
    return true;
}

double CalculateLotSize()
{
    double accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    double riskAmount = accountEquity * (RiskPercent / 100.0);
    double stopLossPips = (firstCandleHigh - firstCandleLow) / (_Point * 10);

    if (stopLossPips == 0)
    {
        Print("Error: Stop loss distance is zero. Cannot calculate lot size.");
        return 0;
    }

    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    // Correctly determine pip size based on symbol's digits
    double pointValue = _Point;
    if(_Digits == 3 || _Digits == 5)
    {
        pointValue = _Point * 10;
    }
    
    double valuePerPip = tickValue * (pointValue / tickSize);


    double riskPerLot = stopLossPips * valuePerPip;
    double lotSize = (riskPerLot > 0) ? riskAmount / riskPerLot : 0;

    // Normalize and validate lot size
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lotSize = floor(lotSize / lotStep) * lotStep;

    Print("Lot Size Calculation: Equity=", accountEquity, ", Risk%=", RiskPercent, ", RiskAmt=", riskAmount, ", SL Pips=", stopLossPips, ", LotSize=", lotSize);

    if (lotSize < minLot)
    {
        Print("Calculated lot size ", lotSize, " is less than minimum ", minLot, ". Adjusting to minimum.");
        lotSize = minLot;
    }
    if (lotSize > maxLot)
    {
        Print("Calculated lot size ", lotSize, " is greater than maximum ", maxLot, ". Adjusting to maximum.");
        lotSize = maxLot;
    }
    
    if(accountEquity * (RiskPercent / 100.0) < (lotSize * riskPerLot))
    {
       if(lotSize == minLot)
       {
          Print("Warning: Minimum lot size still exceeds risk percentage.");
       }
    }


    return lotSize;
}

void PlacePendingOrders(double lotSize)
{
    double point = _Point;
    double offset = OrderOffsetPips * 10 * point;

    // --- Buy Stop Order ---
    double buyPrice = NormalizeDouble(firstCandleHigh + offset, _Digits);
    double buySL = NormalizeDouble(firstCandleLow, _Digits);
    double stopDistance = buyPrice - buySL;
    double buyTP = NormalizeDouble(buyPrice + (stopDistance * RiskRewardRatio), _Digits);

    Print("Placing Buy Stop: Price=", buyPrice, " SL=", buySL, " TP=", buyTP, " Lots=", lotSize);
    trade.BuyStop(lotSize, buyPrice, _Symbol, buySL, buyTP, 0, 0, "FirstCandleBreakout_Buy");

    // --- Sell Stop Order ---
    double sellPrice = NormalizeDouble(firstCandleLow - offset, _Digits);
    double sellSL = NormalizeDouble(firstCandleHigh, _Digits);
    stopDistance = sellSL - sellPrice;
    double sellTP = NormalizeDouble(sellPrice - (stopDistance * RiskRewardRatio), _Digits);

    Print("Placing Sell Stop: Price=", sellPrice, " SL=", sellSL, " TP=", sellTP, " Lots=", lotSize);
    trade.SellStop(lotSize, sellPrice, _Symbol, sellSL, sellTP, 0, 0, "FirstCandleBreakout_Sell");
}


//+------------------------------------------------------------------+
//| TRADE MANAGEMENT FUNCTIONS                                       |
//+------------------------------------------------------------------+
void ManageActiveTrades()
{
    // --- OCO (One-Cancels-Other) Logic ---
    if (PositionSelect(_Symbol))
    {
        if(PositionGetInteger(POSITION_MAGIC) == MagicNumber)
        {
            // A position is open, cancel any remaining pending orders
            for (int i = OrdersTotal() - 1; i >= 0; i--)
            {
                if (OrderGetTicket(i) > 0 && OrderGetString(ORDER_SYMBOL) == _Symbol && OrderGetInteger(ORDER_MAGIC) == MagicNumber)
                {
                    Print("Active position detected. Deleting opposing pending order #", OrderGetTicket(i));
                    trade.OrderDelete(OrderGetTicket(i));
                }
            }
        }
    }

    // --- Breakeven and Trailing Stop Logic ---
    if (PositionSelect(_Symbol))
    {
        if(PositionGetInteger(POSITION_MAGIC) != MagicNumber) return;

        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentSL = PositionGetDouble(POSITION_SL);
        long positionType = PositionGetInteger(POSITION_TYPE);

        // Handle Breakeven
        if (BreakevenTriggerPips > 0)
        {
            double profitPips = 0;
            if (positionType == POSITION_TYPE_BUY) profitPips = (SymbolInfoDouble(_Symbol, SYMBOL_BID) - openPrice) / (_Point * 10);
            else profitPips = (openPrice - SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / (_Point * 10);

            if (profitPips >= BreakevenTriggerPips && currentSL != openPrice)
            {
                Print("Breakeven triggered. Moving SL to open price: ", openPrice);
                trade.PositionModify(_Symbol, openPrice, PositionGetDouble(POSITION_TP));
            }
        }

        // Handle Trailing Stop
        if (TrailingStopPips > 0)
        {
            double newSL = 0;
            if (positionType == POSITION_TYPE_BUY)
            {
                newSL = NormalizeDouble(SymbolInfoDouble(_Symbol, SYMBOL_BID) - (TrailingStopPips * 10 * _Point), _Digits);
                if (newSL > currentSL || currentSL == 0)
                {
                     if(newSL > openPrice) // Ensure new SL is at least at breakeven
                     {
                        Print("Trailing stop triggered for BUY. New SL: ", newSL);
                        trade.PositionModify(_Symbol, newSL, PositionGetDouble(POSITION_TP));
                     }
                }
            }
            else // SELL
            {
                newSL = NormalizeDouble(SymbolInfoDouble(_Symbol, SYMBOL_ASK) + (TrailingStopPips * 10 * _Point), _Digits);
                if (newSL < currentSL || currentSL == 0)
                {
                    if(newSL < openPrice) // Ensure new SL is at least at breakeven
                    {
                        Print("Trailing stop triggered for SELL. New SL: ", newSL);
                        trade.PositionModify(_Symbol, newSL, PositionGetDouble(POSITION_TP));
                    }
                }
            }
        }
    }
}
//+------------------------------------------------------------------+
