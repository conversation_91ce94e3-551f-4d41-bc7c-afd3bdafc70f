//+------------------------------------------------------------------+
//|                                        FirstCandleBreakout.mq5 |
//|                      Copyright 2025, Roo - AI Software Engineer |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Roo - AI Software Engineer"
#property link      ""
#property version   "1.00"
#property strict

#include <Trade\Trade.mqh>

//--- Custom Enumerations
enum ENUM_SESSION
{
    SESSION_LONDON,
    SESSION_NEW_YORK
};

//--- EA Input Parameters
//--- Session and Magic Number
input group           "Session Settings"
input ENUM_SESSION    SessionToTrade      = SESSION_LONDON; // Session to trade (London or New York)
input ulong           MagicNumber         = 12345;          // Unique ID for EA's trades

//--- Risk Management
input group           "Risk Management"
input double          RiskPercent         = 1.0;            // Risk percentage of account equity per trade
input double          MaxSpread           = 2.0;            // Maximum allowed spread in pips
input double          RiskRewardRatio     = 1.5;            // Risk-to-Reward ratio for Take Profit

//--- Order Settings
input group           "Order Settings"
input double          OrderOffsetPips     = 1.0;            // Pips to offset pending orders from the candle's high/low
input int             MaxSlippage         = 3;              // Maximum allowed slippage in points

//--- Trade Management
input group           "Trade Management"
input int             BreakevenTriggerPips = 20;             // Pips in profit to move SL to breakeven (0 to disable)
input int             TrailingStopPips     = 15;             // Trailing stop distance in pips (0 to disable)

//--- Global Variables
//--- Session Timing
datetime sessionStartTime;
datetime sessionEndTime;
int      lastTradeDay = 0;

//--- First Candle Data
double   firstCandleHigh = 0;
double   firstCandleLow = 0;
bool     firstCandleIdentified = false;

//--- Trade State
bool     tradePlacedForSession = false;

//--- MQL5 Objects
CTrade   trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("==================================================");
    Print("FirstCandleBreakout EA v1.00 Initializing...");
    Print("DEBUG [INIT]: Symbol: ", _Symbol);
    Print("DEBUG [INIT]: Account Currency: ", AccountInfoString(ACCOUNT_CURRENCY));
    Print("DEBUG [INIT]: Account Equity: ", AccountInfoDouble(ACCOUNT_EQUITY));
    Print("DEBUG [INIT]: Session to Trade: ", (SessionToTrade == SESSION_LONDON ? "LONDON" : "NEW_YORK"));
    Print("DEBUG [INIT]: Magic Number: ", MagicNumber);
    Print("DEBUG [INIT]: Risk Percent: ", RiskPercent, "%");
    Print("DEBUG [INIT]: Max Spread: ", MaxSpread, " pips");
    Print("DEBUG [INIT]: Risk-Reward Ratio: ", RiskRewardRatio);
    Print("DEBUG [INIT]: Order Offset: ", OrderOffsetPips, " pips");
    Print("DEBUG [INIT]: Breakeven Trigger: ", BreakevenTriggerPips, " pips");
    Print("DEBUG [INIT]: Trailing Stop: ", TrailingStopPips, " pips");

    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(MaxSlippage);
    trade.SetTypeFillingBySymbol(_Symbol);
    Print("DEBUG [INIT]: Trade object configured successfully");

    // This ensures the session times are set correctly on the very first run
    lastTradeDay = TimeCurrent();
    Print("DEBUG [INIT]: Initial trade day set to: ", TimeToString(lastTradeDay));
    UpdateSessionTimings();
    Print("EA Initialized. Session tracking is active.");
    Print("==================================================");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("==================================================");
    Print("DEBUG [DEINIT]: FirstCandleBreakout EA Deinitializing...");
    Print("DEBUG [DEINIT]: Reason code: ", reason);

    string reasonText = "";
    switch(reason)
    {
        case REASON_PROGRAM: reasonText = "EA terminated by user"; break;
        case REASON_REMOVE: reasonText = "EA removed from chart"; break;
        case REASON_RECOMPILE: reasonText = "EA recompiled"; break;
        case REASON_CHARTCHANGE: reasonText = "Chart symbol or period changed"; break;
        case REASON_CHARTCLOSE: reasonText = "Chart closed"; break;
        case REASON_PARAMETERS: reasonText = "Input parameters changed"; break;
        case REASON_ACCOUNT: reasonText = "Account changed"; break;
        case REASON_TEMPLATE: reasonText = "Template applied"; break;
        case REASON_INITFAILED: reasonText = "Initialization failed"; break;
        case REASON_CLOSE: reasonText = "Terminal closing"; break;
        default: reasonText = "Unknown reason"; break;
    }

    Print("DEBUG [DEINIT]: Reason: ", reasonText);
    Print("DEBUG [DEINIT]: Final state - firstCandleIdentified: ", firstCandleIdentified);
    Print("DEBUG [DEINIT]: Final state - tradePlacedForSession: ", tradePlacedForSession);

    // Check for any remaining orders or positions
    int pendingOrders = 0;
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderGetTicket(i) > 0 && OrderGetString(ORDER_SYMBOL) == _Symbol && OrderGetInteger(ORDER_MAGIC) == MagicNumber)
        {
            pendingOrders++;
        }
    }

    bool hasPosition = PositionSelect(_Symbol) && PositionGetInteger(POSITION_MAGIC) == MagicNumber;

    Print("DEBUG [DEINIT]: Pending orders remaining: ", pendingOrders);
    Print("DEBUG [DEINIT]: Position open: ", (hasPosition ? "YES" : "NO"));
    Print("DEBUG [DEINIT]: Deinitialization complete");
    Print("==================================================");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static int tickCount = 0;
    tickCount++;

    // Log every 100 ticks to avoid spam but show activity
    if (tickCount % 100 == 0)
    {
        Print("DEBUG [TICK]: Tick #", tickCount, " at ", TimeToString(TimeCurrent()));
    }

    // --- Daily Reset ---
    if (TimeCurrent() / (24 * 3600) != lastTradeDay / (24 * 3600))
    {
        Print("=== DEBUG [DAILY_RESET]: New day detected ===");
        Print("DEBUG [DAILY_RESET]: Previous day: ", TimeToString(lastTradeDay));
        Print("DEBUG [DAILY_RESET]: Current time: ", TimeToString(TimeCurrent()));
        Print("DEBUG [DAILY_RESET]: Resetting session variables...");

        lastTradeDay = TimeCurrent();
        tradePlacedForSession = false;
        firstCandleIdentified = false;

        Print("DEBUG [DAILY_RESET]: tradePlacedForSession = ", tradePlacedForSession);
        Print("DEBUG [DAILY_RESET]: firstCandleIdentified = ", firstCandleIdentified);

        UpdateSessionTimings(); // This will now correctly set for the new day
        Print("=== DEBUG [DAILY_RESET]: Reset complete ===");
    }

    // --- Session Check ---
    bool sessionActive = IsTradeWindowActive();
    if (!sessionActive)
    {
        static datetime lastInactiveLog = 0;
        if (TimeCurrent() - lastInactiveLog > 3600) // Log every hour when inactive
        {
            Print("DEBUG [SESSION]: Outside trading window - managing existing trades only");
            lastInactiveLog = TimeCurrent();
        }
        ManageActiveTrades(); // Still manage trades outside the session window
        return;
    }
    else
    {
        static bool sessionStartLogged = false;
        if (!sessionStartLogged)
        {
            Print("=== DEBUG [SESSION]: Trading window is ACTIVE ===");
            sessionStartLogged = true;
        }
    }

    // --- First Candle Identification Logic ---
    if (!firstCandleIdentified)
    {
        Print("DEBUG [CANDLE]: Attempting to identify first candle...");
        if (!IdentifyFirstCandle())
        {
            Print("DEBUG [CANDLE]: First candle not yet identified, waiting...");
            return; // Wait for the next tick if candle not found yet
        }
        Print("DEBUG [CANDLE]: First candle successfully identified!");
    }

    // --- Pre-Trade Logic (Run once per session) ---
    if (firstCandleIdentified && !tradePlacedForSession)
    {
        Print("=== DEBUG [TRADE_SETUP]: Starting trade setup process ===");
        Print("DEBUG [TRADE_SETUP]: First candle identified: ", firstCandleIdentified);
        Print("DEBUG [TRADE_SETUP]: Trade placed for session: ", tradePlacedForSession);

        if (!IsSpreadAcceptable())
        {
            Print("DEBUG [TRADE_SETUP]: Spread check FAILED - aborting trade setup");
            tradePlacedForSession = true; // Abort for this session
            return;
        }
        Print("DEBUG [TRADE_SETUP]: Spread check PASSED");

        double lotSize = CalculateLotSize();
        Print("DEBUG [TRADE_SETUP]: Calculated lot size: ", lotSize);

        if (lotSize <= 0)
        {
            Print("DEBUG [TRADE_SETUP]: Lot size calculation FAILED - aborting trade setup");
            tradePlacedForSession = true; // Abort for this session
            return;
        }

        Print("DEBUG [TRADE_SETUP]: Proceeding to place pending orders...");
        PlacePendingOrders(lotSize);
        tradePlacedForSession = true; // Mark as placed to prevent duplicates
        Print("=== DEBUG [TRADE_SETUP]: Trade setup process complete ===");
    }

    // --- Active Trade Management ---
    ManageActiveTrades();
}

//+------------------------------------------------------------------+
//| TIME MANAGEMENT FUNCTIONS                                        |
//+------------------------------------------------------------------+
void UpdateSessionTimings()
{
    Print("=== DEBUG [SESSION_TIMING]: Updating session timings ===");

    long gmtOffset = TimeGMTOffset();
    int sessionStartHourGMT = (SessionToTrade == SESSION_LONDON) ? 8 : 13; // London 8:00 GMT, NY 13:00 GMT

    Print("DEBUG [SESSION_TIMING]: Selected session: ", (SessionToTrade == SESSION_LONDON ? "LONDON" : "NEW_YORK"));
    Print("DEBUG [SESSION_TIMING]: Session start hour GMT: ", sessionStartHourGMT, ":00");
    Print("DEBUG [SESSION_TIMING]: GMT Offset: ", gmtOffset, " seconds (", gmtOffset/3600.0, " hours)");

    // Get today's date at 00:00 GMT
    datetime today = TimeCurrent() - (TimeCurrent() % (24 * 3600));
    Print("DEBUG [SESSION_TIMING]: Today's date (00:00 GMT): ", TimeToString(today));

    // Calculate session start time in GMT, then convert to broker time
    datetime sessionStartGMT = today + sessionStartHourGMT * 3600;
    sessionStartTime = sessionStartGMT + gmtOffset; // Add GMT offset to convert to broker time
    sessionEndTime = sessionStartTime + 9 * 3600; // 9-hour session window

    Print("DEBUG [SESSION_TIMING]: Session Start GMT: ", TimeToString(sessionStartGMT));
    Print("DEBUG [SESSION_TIMING]: Session Start Broker Time: ", TimeToString(sessionStartTime));
    Print("DEBUG [SESSION_TIMING]: Session End Broker Time: ", TimeToString(sessionEndTime));
    Print("DEBUG [SESSION_TIMING]: Current Broker Time: ", TimeToString(TimeCurrent()));
    Print("=== DEBUG [SESSION_TIMING]: Session timing update complete ===");
}

bool IsTradeWindowActive()
{
    datetime currentTime = TimeCurrent();
    bool isActive = (currentTime >= sessionStartTime && currentTime < sessionEndTime);

    // Enhanced diagnostic logging
    static datetime lastLogTime = 0;
    static bool lastActiveState = false;

    // Log when state changes or every hour
    if(isActive != lastActiveState || currentTime - lastLogTime > 3600)
    {
        Print("=== DEBUG [TRADE_WINDOW]: Trade Window Status Check ===");
        Print("DEBUG [TRADE_WINDOW]: Current Time: ", TimeToString(currentTime));
        Print("DEBUG [TRADE_WINDOW]: Session Start: ", TimeToString(sessionStartTime));
        Print("DEBUG [TRADE_WINDOW]: Session End: ", TimeToString(sessionEndTime));
        Print("DEBUG [TRADE_WINDOW]: Window Active: ", (isActive ? "TRUE" : "FALSE"));

        if (isActive)
        {
            long timeInSession = currentTime - sessionStartTime;
            Print("DEBUG [TRADE_WINDOW]: Time in session: ", timeInSession/60, " minutes");
        }
        else
        {
            if (currentTime < sessionStartTime)
            {
                long timeToStart = sessionStartTime - currentTime;
                Print("DEBUG [TRADE_WINDOW]: Time until session start: ", timeToStart/60, " minutes");
            }
            else
            {
                long timeAfterEnd = currentTime - sessionEndTime;
                Print("DEBUG [TRADE_WINDOW]: Time after session end: ", timeAfterEnd/60, " minutes");
            }
        }

        lastLogTime = currentTime;
        lastActiveState = isActive;
        Print("=== DEBUG [TRADE_WINDOW]: Status check complete ===");
    }

    return isActive;
}

//+------------------------------------------------------------------+
//| CORE STRATEGY FUNCTIONS                                          |
//+------------------------------------------------------------------+
bool IdentifyFirstCandle()
{
    Print("=== DEBUG [FIRST_CANDLE]: Starting first candle identification ===");

    MqlRates rates[];
    int ratesCopied = CopyRates(_Symbol, PERIOD_M30, 0, 10, rates);

    Print("DEBUG [FIRST_CANDLE]: Requested 10 M30 candles, copied: ", ratesCopied);

    if(ratesCopied < 10)
    {
        Print("DEBUG [FIRST_CANDLE]: ERROR - Could not copy sufficient M30 rates");
        Print("DEBUG [FIRST_CANDLE]: Only ", ratesCopied, " candles available");
        return false;
    }

    Print("DEBUG [FIRST_CANDLE]: Session start time: ", TimeToString(sessionStartTime));
    Print("DEBUG [FIRST_CANDLE]: Current time: ", TimeToString(TimeCurrent()));

    // Look for the first complete candle that starts at or after the session start time
    for(int i = 1; i < 10; i++) // Start from index 1 (most recent closed candle)
    {
        datetime candleOpenTime = rates[i].time;
        datetime candleCloseTime = candleOpenTime + 30 * 60; // 30 minutes later

        Print("DEBUG [FIRST_CANDLE]: Checking candle #", i);
        Print("DEBUG [FIRST_CANDLE]: Candle open time: ", TimeToString(candleOpenTime));
        Print("DEBUG [FIRST_CANDLE]: Candle close time: ", TimeToString(candleCloseTime));
        Print("DEBUG [FIRST_CANDLE]: Candle OHLC: O=", rates[i].open, " H=", rates[i].high,
              " L=", rates[i].low, " C=", rates[i].close);

        bool startsAfterSession = (candleOpenTime >= sessionStartTime);
        bool isComplete = (TimeCurrent() >= candleCloseTime);

        Print("DEBUG [FIRST_CANDLE]: Starts after session: ", startsAfterSession);
        Print("DEBUG [FIRST_CANDLE]: Is complete: ", isComplete);

        // Check if this candle starts at or after session start and is complete
        if (startsAfterSession && isComplete)
        {
            firstCandleHigh = rates[i].high;
            firstCandleLow = rates[i].low;
            firstCandleIdentified = true;

            double candleRangePips = (firstCandleHigh - firstCandleLow) / (_Point * 10);

            Print("=== DEBUG [FIRST_CANDLE]: FIRST CANDLE IDENTIFIED! ===");
            Print("DEBUG [FIRST_CANDLE]: Candle time: ", TimeToString(candleOpenTime));
            Print("DEBUG [FIRST_CANDLE]: High: ", NormalizeDouble(firstCandleHigh, _Digits));
            Print("DEBUG [FIRST_CANDLE]: Low: ", NormalizeDouble(firstCandleLow, _Digits));
            Print("DEBUG [FIRST_CANDLE]: Range: ", NormalizeDouble(candleRangePips, 1), " pips");
            Print("=== DEBUG [FIRST_CANDLE]: Identification complete ===");
            return true;
        }
        else
        {
            Print("DEBUG [FIRST_CANDLE]: Candle #", i, " does not meet criteria");
        }
    }

    Print("DEBUG [FIRST_CANDLE]: No suitable first candle found yet");
    Print("=== DEBUG [FIRST_CANDLE]: Identification attempt complete ===");
    return false;
}

bool IsSpreadAcceptable()
{
    Print("=== DEBUG [SPREAD_CHECK]: Checking spread conditions ===");

    long spreadPoints = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD);
    double currentSpread = spreadPoints * _Point;
    double currentSpreadPips = currentSpread / (10 * _Point);
    double maxSpreadPips = MaxSpread;

    Print("DEBUG [SPREAD_CHECK]: Current spread (points): ", spreadPoints);
    Print("DEBUG [SPREAD_CHECK]: Current spread (pips): ", NormalizeDouble(currentSpreadPips, 1));
    Print("DEBUG [SPREAD_CHECK]: Maximum allowed spread (pips): ", maxSpreadPips);

    bool acceptable = (currentSpreadPips <= maxSpreadPips);

    Print("DEBUG [SPREAD_CHECK]: Spread acceptable: ", (acceptable ? "YES" : "NO"));

    if (!acceptable)
    {
        Print("DEBUG [SPREAD_CHECK]: SPREAD CHECK FAILED!");
        Print("DEBUG [SPREAD_CHECK]: Current: ", NormalizeDouble(currentSpreadPips, 1),
              " pips > Max allowed: ", maxSpreadPips, " pips");
    }

    Print("=== DEBUG [SPREAD_CHECK]: Spread check complete ===");
    return acceptable;
}

double CalculateLotSize()
{
    Print("=== DEBUG [LOT_CALC]: Starting lot size calculation ===");

    double accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    double riskAmount = accountEquity * (RiskPercent / 100.0);
    double stopLossPips = (firstCandleHigh - firstCandleLow) / (_Point * 10);

    Print("DEBUG [LOT_CALC]: Account equity: ", accountEquity);
    Print("DEBUG [LOT_CALC]: Risk percentage: ", RiskPercent, "%");
    Print("DEBUG [LOT_CALC]: Risk amount: ", riskAmount);
    Print("DEBUG [LOT_CALC]: First candle high: ", firstCandleHigh);
    Print("DEBUG [LOT_CALC]: First candle low: ", firstCandleLow);
    Print("DEBUG [LOT_CALC]: Stop loss distance: ", NormalizeDouble(stopLossPips, 1), " pips");

    if (stopLossPips <= 0)
    {
        Print("DEBUG [LOT_CALC]: ERROR - Stop loss distance is zero or negative!");
        return 0;
    }

    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    Print("DEBUG [LOT_CALC]: Symbol digits: ", _Digits);
    Print("DEBUG [LOT_CALC]: Point value: ", _Point);
    Print("DEBUG [LOT_CALC]: Tick value: ", tickValue);
    Print("DEBUG [LOT_CALC]: Tick size: ", tickSize);

    // Correctly determine pip size based on symbol's digits
    double pointValue = _Point;
    if(_Digits == 3 || _Digits == 5)
    {
        pointValue = _Point * 10;
        Print("DEBUG [LOT_CALC]: Using 10x point value for 3/5 digit symbol");
    }

    double valuePerPip = tickValue * (pointValue / tickSize);
    Print("DEBUG [LOT_CALC]: Value per pip: ", valuePerPip);

    double riskPerLot = stopLossPips * valuePerPip;
    double lotSize = (riskPerLot > 0) ? riskAmount / riskPerLot : 0;

    Print("DEBUG [LOT_CALC]: Risk per lot: ", riskPerLot);
    Print("DEBUG [LOT_CALC]: Initial lot size calculation: ", lotSize);

    // Get symbol constraints
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    Print("DEBUG [LOT_CALC]: Symbol constraints - Min: ", minLot, ", Max: ", maxLot, ", Step: ", lotStep);

    // Normalize lot size to step
    lotSize = floor(lotSize / lotStep) * lotStep;
    Print("DEBUG [LOT_CALC]: Lot size after step normalization: ", lotSize);

    // Apply constraints
    if (lotSize < minLot)
    {
        Print("DEBUG [LOT_CALC]: Lot size below minimum, adjusting from ", lotSize, " to ", minLot);
        lotSize = minLot;
    }
    if (lotSize > maxLot)
    {
        Print("DEBUG [LOT_CALC]: Lot size above maximum, adjusting from ", lotSize, " to ", maxLot);
        lotSize = maxLot;
    }

    // Final risk validation
    double actualRisk = lotSize * riskPerLot;
    double actualRiskPercent = (actualRisk / accountEquity) * 100;

    Print("DEBUG [LOT_CALC]: Final lot size: ", lotSize);
    Print("DEBUG [LOT_CALC]: Actual risk amount: ", actualRisk);
    Print("DEBUG [LOT_CALC]: Actual risk percentage: ", NormalizeDouble(actualRiskPercent, 2), "%");

    if(actualRiskPercent > RiskPercent && lotSize == minLot)
    {
        Print("DEBUG [LOT_CALC]: WARNING - Minimum lot size exceeds desired risk percentage!");
    }

    Print("=== DEBUG [LOT_CALC]: Lot size calculation complete ===");
    return lotSize;
}

void PlacePendingOrders(double lotSize)
{
    Print("=== DEBUG [ORDER_PLACEMENT]: Starting pending order placement ===");

    double point = _Point;
    double offset = OrderOffsetPips * 10 * point;

    Print("DEBUG [ORDER_PLACEMENT]: Lot size: ", lotSize);
    Print("DEBUG [ORDER_PLACEMENT]: Point value: ", point);
    Print("DEBUG [ORDER_PLACEMENT]: Offset pips: ", OrderOffsetPips);
    Print("DEBUG [ORDER_PLACEMENT]: Offset price: ", offset);
    Print("DEBUG [ORDER_PLACEMENT]: Risk-reward ratio: ", RiskRewardRatio);

    // --- Buy Stop Order ---
    Print("--- DEBUG [ORDER_PLACEMENT]: Calculating BUY STOP order ---");
    double buyPrice = NormalizeDouble(firstCandleHigh + offset, _Digits);
    double buySL = NormalizeDouble(firstCandleLow, _Digits);
    double stopDistance = buyPrice - buySL;
    double buyTP = NormalizeDouble(buyPrice + (stopDistance * RiskRewardRatio), _Digits);

    Print("DEBUG [ORDER_PLACEMENT]: Buy Stop Price: ", buyPrice);
    Print("DEBUG [ORDER_PLACEMENT]: Buy Stop SL: ", buySL);
    Print("DEBUG [ORDER_PLACEMENT]: Buy Stop Distance: ", NormalizeDouble(stopDistance / (_Point * 10), 1), " pips");
    Print("DEBUG [ORDER_PLACEMENT]: Buy Stop TP: ", buyTP);
    Print("DEBUG [ORDER_PLACEMENT]: Buy TP Distance: ", NormalizeDouble((buyTP - buyPrice) / (_Point * 10), 1), " pips");

    Print("DEBUG [ORDER_PLACEMENT]: Placing BUY STOP order...");
    bool buyResult = trade.BuyStop(lotSize, buyPrice, _Symbol, buySL, buyTP, 0, 0, "FirstCandleBreakout_Buy");

    if (buyResult)
    {
        Print("DEBUG [ORDER_PLACEMENT]: BUY STOP order placed successfully");
    }
    else
    {
        Print("DEBUG [ORDER_PLACEMENT]: ERROR - BUY STOP order failed!");
        Print("DEBUG [ORDER_PLACEMENT]: Error code: ", trade.ResultRetcode());
        Print("DEBUG [ORDER_PLACEMENT]: Error description: ", trade.ResultRetcodeDescription());
    }

    // --- Sell Stop Order ---
    Print("--- DEBUG [ORDER_PLACEMENT]: Calculating SELL STOP order ---");
    double sellPrice = NormalizeDouble(firstCandleLow - offset, _Digits);
    double sellSL = NormalizeDouble(firstCandleHigh, _Digits);
    stopDistance = sellSL - sellPrice;
    double sellTP = NormalizeDouble(sellPrice - (stopDistance * RiskRewardRatio), _Digits);

    Print("DEBUG [ORDER_PLACEMENT]: Sell Stop Price: ", sellPrice);
    Print("DEBUG [ORDER_PLACEMENT]: Sell Stop SL: ", sellSL);
    Print("DEBUG [ORDER_PLACEMENT]: Sell Stop Distance: ", NormalizeDouble(stopDistance / (_Point * 10), 1), " pips");
    Print("DEBUG [ORDER_PLACEMENT]: Sell Stop TP: ", sellTP);
    Print("DEBUG [ORDER_PLACEMENT]: Sell TP Distance: ", NormalizeDouble((sellPrice - sellTP) / (_Point * 10), 1), " pips");

    Print("DEBUG [ORDER_PLACEMENT]: Placing SELL STOP order...");
    bool sellResult = trade.SellStop(lotSize, sellPrice, _Symbol, sellSL, sellTP, 0, 0, "FirstCandleBreakout_Sell");

    if (sellResult)
    {
        Print("DEBUG [ORDER_PLACEMENT]: SELL STOP order placed successfully");
    }
    else
    {
        Print("DEBUG [ORDER_PLACEMENT]: ERROR - SELL STOP order failed!");
        Print("DEBUG [ORDER_PLACEMENT]: Error code: ", trade.ResultRetcode());
        Print("DEBUG [ORDER_PLACEMENT]: Error description: ", trade.ResultRetcodeDescription());
    }

    Print("=== DEBUG [ORDER_PLACEMENT]: Pending order placement complete ===");
}


//+------------------------------------------------------------------+
//| TRADE MANAGEMENT FUNCTIONS                                       |
//+------------------------------------------------------------------+
void ManageActiveTrades()
{
    static datetime lastManagementLog = 0;
    bool shouldLog = (TimeCurrent() - lastManagementLog > 300); // Log every 5 minutes

    if (shouldLog)
    {
        Print("=== DEBUG [TRADE_MGMT]: Starting trade management cycle ===");
        lastManagementLog = TimeCurrent();
    }

    // --- OCO (One-Cancels-Other) Logic ---
    bool positionExists = PositionSelect(_Symbol);

    if (shouldLog)
    {
        Print("DEBUG [TRADE_MGMT]: Position exists: ", (positionExists ? "YES" : "NO"));
    }

    if (positionExists)
    {
        ulong positionMagic = PositionGetInteger(POSITION_MAGIC);

        if (shouldLog)
        {
            Print("DEBUG [TRADE_MGMT]: Position magic: ", positionMagic, " (Expected: ", MagicNumber, ")");
        }

        if(positionMagic == MagicNumber)
        {
            if (shouldLog)
            {
                Print("DEBUG [TRADE_MGMT]: Our position detected - checking for pending orders to cancel");
            }

            int ordersTotal = OrdersTotal();
            int ordersCancelled = 0;

            // A position is open, cancel any remaining pending orders
            for (int i = ordersTotal - 1; i >= 0; i--)
            {
                if (OrderGetTicket(i) > 0 && OrderGetString(ORDER_SYMBOL) == _Symbol && OrderGetInteger(ORDER_MAGIC) == MagicNumber)
                {
                    ulong orderTicket = OrderGetTicket(i);
                    Print("DEBUG [TRADE_MGMT]: Active position detected. Deleting opposing pending order #", orderTicket);

                    bool deleteResult = trade.OrderDelete(orderTicket);
                    if (deleteResult)
                    {
                        Print("DEBUG [TRADE_MGMT]: Order #", orderTicket, " deleted successfully");
                        ordersCancelled++;
                    }
                    else
                    {
                        Print("DEBUG [TRADE_MGMT]: ERROR - Failed to delete order #", orderTicket);
                        Print("DEBUG [TRADE_MGMT]: Error code: ", trade.ResultRetcode());
                    }
                }
            }

            if (shouldLog)
            {
                Print("DEBUG [TRADE_MGMT]: Orders cancelled: ", ordersCancelled);
            }
        }
    }

    // --- Breakeven and Trailing Stop Logic ---
    if (PositionSelect(_Symbol))
    {
        ulong positionMagic = PositionGetInteger(POSITION_MAGIC);
        if(positionMagic != MagicNumber)
        {
            if (shouldLog)
            {
                Print("DEBUG [TRADE_MGMT]: Position exists but wrong magic number: ", positionMagic);
            }
            return;
        }

        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentSL = PositionGetDouble(POSITION_SL);
        double currentTP = PositionGetDouble(POSITION_TP);
        long positionType = PositionGetInteger(POSITION_TYPE);
        double positionVolume = PositionGetDouble(POSITION_VOLUME);
        double currentPrice = (positionType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        if (shouldLog)
        {
            Print("--- DEBUG [TRADE_MGMT]: Position Details ---");
            Print("DEBUG [TRADE_MGMT]: Type: ", (positionType == POSITION_TYPE_BUY ? "BUY" : "SELL"));
            Print("DEBUG [TRADE_MGMT]: Volume: ", positionVolume);
            Print("DEBUG [TRADE_MGMT]: Open Price: ", openPrice);
            Print("DEBUG [TRADE_MGMT]: Current Price: ", currentPrice);
            Print("DEBUG [TRADE_MGMT]: Current SL: ", currentSL);
            Print("DEBUG [TRADE_MGMT]: Current TP: ", currentTP);
        }

        // Handle Breakeven
        if (BreakevenTriggerPips > 0)
        {
            double profitPips = 0;
            if (positionType == POSITION_TYPE_BUY)
                profitPips = (currentPrice - openPrice) / (_Point * 10);
            else
                profitPips = (openPrice - currentPrice) / (_Point * 10);

            if (shouldLog)
            {
                Print("DEBUG [TRADE_MGMT]: Current profit: ", NormalizeDouble(profitPips, 1), " pips");
                Print("DEBUG [TRADE_MGMT]: Breakeven trigger: ", BreakevenTriggerPips, " pips");
            }

            if (profitPips >= BreakevenTriggerPips && currentSL != openPrice)
            {
                Print("DEBUG [TRADE_MGMT]: BREAKEVEN TRIGGERED!");
                Print("DEBUG [TRADE_MGMT]: Moving SL from ", currentSL, " to open price: ", openPrice);

                bool modifyResult = trade.PositionModify(_Symbol, openPrice, currentTP);
                if (modifyResult)
                {
                    Print("DEBUG [TRADE_MGMT]: Breakeven modification successful");
                }
                else
                {
                    Print("DEBUG [TRADE_MGMT]: ERROR - Breakeven modification failed!");
                    Print("DEBUG [TRADE_MGMT]: Error code: ", trade.ResultRetcode());
                }
            }
        }

        // Handle Trailing Stop
        if (TrailingStopPips > 0)
        {
            double newSL = 0;
            bool shouldModify = false;

            if (positionType == POSITION_TYPE_BUY)
            {
                newSL = NormalizeDouble(currentPrice - (TrailingStopPips * 10 * _Point), _Digits);
                shouldModify = (newSL > currentSL || currentSL == 0) && (newSL > openPrice);

                if (shouldLog)
                {
                    Print("DEBUG [TRADE_MGMT]: BUY Trailing - New SL: ", newSL, ", Current SL: ", currentSL);
                    Print("DEBUG [TRADE_MGMT]: Should modify: ", shouldModify);
                }

                if (shouldModify)
                {
                    Print("DEBUG [TRADE_MGMT]: TRAILING STOP TRIGGERED for BUY");
                    Print("DEBUG [TRADE_MGMT]: Moving SL from ", currentSL, " to ", newSL);

                    bool modifyResult = trade.PositionModify(_Symbol, newSL, currentTP);
                    if (modifyResult)
                    {
                        Print("DEBUG [TRADE_MGMT]: Trailing stop modification successful");
                    }
                    else
                    {
                        Print("DEBUG [TRADE_MGMT]: ERROR - Trailing stop modification failed!");
                        Print("DEBUG [TRADE_MGMT]: Error code: ", trade.ResultRetcode());
                    }
                }
            }
            else // SELL
            {
                newSL = NormalizeDouble(currentPrice + (TrailingStopPips * 10 * _Point), _Digits);
                shouldModify = (newSL < currentSL || currentSL == 0) && (newSL < openPrice);

                if (shouldLog)
                {
                    Print("DEBUG [TRADE_MGMT]: SELL Trailing - New SL: ", newSL, ", Current SL: ", currentSL);
                    Print("DEBUG [TRADE_MGMT]: Should modify: ", shouldModify);
                }

                if (shouldModify)
                {
                    Print("DEBUG [TRADE_MGMT]: TRAILING STOP TRIGGERED for SELL");
                    Print("DEBUG [TRADE_MGMT]: Moving SL from ", currentSL, " to ", newSL);

                    bool modifyResult = trade.PositionModify(_Symbol, newSL, currentTP);
                    if (modifyResult)
                    {
                        Print("DEBUG [TRADE_MGMT]: Trailing stop modification successful");
                    }
                    else
                    {
                        Print("DEBUG [TRADE_MGMT]: ERROR - Trailing stop modification failed!");
                        Print("DEBUG [TRADE_MGMT]: Error code: ", trade.ResultRetcode());
                    }
                }
            }
        }
    }

    if (shouldLog)
    {
        Print("=== DEBUG [TRADE_MGMT]: Trade management cycle complete ===");
    }
}
//+------------------------------------------------------------------+
