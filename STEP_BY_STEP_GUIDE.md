# 📋 Complete Step-by-Step Backtesting Guide

## 🎯 Overview
This guide will walk you through the complete process from data import to exporting optimal parameters for your First Candle EA strategy.

## 📁 File Organization
All backtesting files are organized under the `day_trading/backtesting/` folder:

```
day_trading/
├── backtesting/
│   ├── data/           # Raw market data
│   ├── results/        # Backtest results
│   ├── reports/        # HTML/PDF reports
│   ├── exports/        # Optimal parameters & configs
│   ├── cache/          # Temporary files
│   └── logs/           # Process logs
├── FirstCandleBreakout.mq5
├── FirstCandleBreakout_Enhanced.mq5
├── FirstCandleBreakout_Advanced.mq5
└── [Python backtesting files]
```

---

## 🚀 STEP 1: Initial Setup & Installation

### 1.1 Install Dependencies
```bash
# Navigate to your day_trading folder
cd /Users/<USER>/day_trading

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install required packages
pip install -r requirements.txt
```

### 1.2 Test Your Setup
```bash
# Test everything is working
python setup_and_test.py
```

**Expected Output:**
- ✅ All dependencies installed
- ✅ API key working
- ✅ Quick backtest successful
- ✅ Folders created

---

## 📊 STEP 2: Data Import & Preparation

### 2.1 Method A: Alpha Vantage (Recommended - Real Forex Data)

```bash
# Test your API key first
python test_api_key.py
```

**Using Python Script:**
```python
from mt5_data_importer import MT5DataImporter
from config import Config

# Initialize importer
importer = MT5DataImporter()

# Download EURUSD 30-minute data
data = importer.download_alpha_vantage('EURUSD', 'RWYAWAFKFWBRKBVZ', '30min')

# Save to organized folder
filename = Config.get_timestamped_filename('EURUSD_30m', '.csv', Config.DATA_DIR)
importer.save_data(data, filename)
```

**Using Web Interface:**
```bash
# Launch web interface
streamlit run streamlit_app.py
```
1. Go to "📊 Data Import" page
2. Select "Alpha Vantage"
3. Choose symbol: EURUSD
4. Choose interval: 30min
5. Click "Download Data"
6. Click "💾 Save Data to CSV"

### 2.2 Method B: Yahoo Finance (Free but Limited)

```python
# Download daily data and create synthetic 30m
data = importer.download_yahoo_finance('EURUSD=X', '2023-01-01', '2024-01-01', '1d')
synthetic_30m = importer.create_synthetic_intraday(data, '30T')
```

### 2.3 Method C: MT5 CSV Import

1. **Export from MT5 (on Windows/VPS):**
   - Open MT5
   - Go to Tools → History Center
   - Select EURUSD, M30
   - Export to CSV
   - Transfer file to Mac

2. **Import on Mac:**
```python
data = importer.load_mt5_csv('path/to/eurusd_m30.csv', 'EURUSD')
```

---

## 🔬 STEP 3: Single Backtest (Parameter Testing)

### 3.1 Using Web Interface (Easiest)

```bash
streamlit run streamlit_app.py
```

1. **Go to "🔬 Single Backtest" page**
2. **Configure Parameters:**
   - Session: London (8:00 GMT) or New York (13:00 GMT)
   - Risk Percent: 1.0%
   - Risk:Reward Ratio: 2.0
   - Enable/disable filters as needed
3. **Click "🚀 Run Backtest"**
4. **Review Results:**
   - Total Return
   - Win Rate
   - Sharpe Ratio
   - Max Drawdown

### 3.2 Using Python Script

```python
from first_candle_backtester import FirstCandleBacktester

# Initialize backtester
backtester = FirstCandleBacktester(
    symbol='EURUSD',
    start_date='2023-01-01',
    end_date='2024-01-01'
)

# Define test parameters
params = {
    'session_start_hour': 8,        # London session
    'risk_percent': 1.0,
    'risk_reward_ratio': 2.0,
    'use_volume_filter': True,
    'volume_multiplier': 1.2,
    'use_atr_filter': True,
    'min_atr_multiplier': 1.0,
    'max_atr_multiplier': 3.0,
    'use_range_filter': True,
    'range_percentile': 70.0,
    'max_trade_hours': 8
}

# Run backtest
result, cerebro = backtester.run_backtest(params)

# Print results
print(f"Total Return: {result['total_return']:.2f}%")
print(f"Win Rate: {result['win_rate']:.1f}%")
print(f"Sharpe Ratio: {result['sharpe_ratio']:.2f}")
```

**Results are automatically saved to:**
- `backtesting/results/single_backtest_YYYYMMDD_HHMMSS.json`

---

## ⚙️ STEP 4: Parameter Optimization

### 4.1 Define Parameter Ranges

```python
# Edit these ranges in config.py or define custom ranges
param_ranges = {
    'risk_percent': [0.5, 1.0, 1.5, 2.0],
    'risk_reward_ratio': [1.5, 2.0, 2.5, 3.0],
    'volume_multiplier': [1.0, 1.2, 1.5, 1.8],
    'min_atr_multiplier': [0.8, 1.0, 1.2],
    'max_atr_multiplier': [2.5, 3.0, 3.5, 4.0],
    'range_percentile': [60.0, 70.0, 80.0],
    'breakeven_ratio': [1.0, 1.2, 1.5],
    'trailing_pips': [10, 15, 20, 25]
}
```

### 4.2 Run Optimization

```python
# Run parameter optimization
print("Starting parameter optimization...")
opt_results = backtester.optimize_parameters(param_ranges)

# Display top 5 results
print("\nTop 5 Parameter Combinations:")
for i, result in enumerate(opt_results[:5]):
    print(f"{i+1}. Return: {result['total_return']:.2f}%, "
          f"Win Rate: {result['win_rate']:.1f}%, "
          f"Sharpe: {result['sharpe_ratio']:.2f}")
    print(f"   Parameters: {result['parameters']}")
```

**Results are automatically saved to:**
- `backtesting/results/optimization_YYYYMMDD_HHMMSS.json`
- `backtesting/exports/best_parameters_YYYYMMDD_HHMMSS.json`

### 4.3 Expected Output

```
Testing 2048 parameter combinations...
Testing combination 1/2048: {'risk_percent': 0.5, 'risk_reward_ratio': 1.5, ...}
Testing combination 2/2048: {'risk_percent': 0.5, 'risk_reward_ratio': 2.0, ...}
...
Optimization complete. Best result: 15.67%

Top 5 Parameter Combinations:
1. Return: 15.67%, Win Rate: 65.2%, Sharpe: 1.45
   Parameters: {'risk_percent': 1.5, 'risk_reward_ratio': 2.5, 'volume_multiplier': 1.2, ...}
2. Return: 14.23%, Win Rate: 62.8%, Sharpe: 1.38
   Parameters: {'risk_percent': 1.0, 'risk_reward_ratio': 2.0, 'volume_multiplier': 1.5, ...}
...
```

---

## 🚀 STEP 5: Walk-Forward Analysis (Validation)

### 5.1 Understanding Walk-Forward Analysis

Walk-forward analysis validates strategy robustness by:
1. **Optimization Period**: Find best parameters on historical data
2. **Testing Period**: Test those parameters on unseen future data
3. **Rolling Forward**: Repeat process through time

### 5.2 Configure Walk-Forward Parameters

```python
# Walk-forward settings
window_months = 6      # Optimize on 6 months of data
step_months = 1        # Test on 1 month, then move forward
```

### 5.3 Run Walk-Forward Analysis

```python
# Run walk-forward analysis
print("Starting walk-forward analysis...")
wf_results = backtester.walk_forward_analysis(
    param_ranges=param_ranges,
    window_months=6,
    step_months=1
)

# Analyze results
df_results = backtester.analyze_results(wf_results)
```

### 5.4 Expected Process

```
Starting walk-forward analysis...

Optimization period: 2023-01-01 to 2023-07-01
Testing period: 2023-07-01 to 2023-08-01
Best parameters: {'risk_percent': 1.5, 'risk_reward_ratio': 2.0, ...}
Test result: +3.45%

Optimization period: 2023-02-01 to 2023-08-01
Testing period: 2023-08-01 to 2023-09-01
Best parameters: {'risk_percent': 1.0, 'risk_reward_ratio': 2.5, ...}
Test result: +1.23%

...

Walk-forward analysis complete. 12 periods tested.

=== WALK-FORWARD ANALYSIS SUMMARY ===
Total periods tested: 12
Average test return: 2.34%
Median test return: 1.89%
Best test return: 5.67%
Worst test return: -1.23%
Positive periods: 10/12 (83.3%)
Average win rate: 58.7%
Average Sharpe ratio: 1.23
```

**Results are automatically saved to:**
- `backtesting/results/walkforward_YYYYMMDD_HHMMSS.csv`
- `backtesting/reports/walkforward_analysis_YYYYMMDD_HHMMSS.png`

---

## 📊 STEP 6: Results Analysis & Visualization

### 6.1 Automatic Analysis

The system automatically generates:
- **Performance charts** (equity curve, drawdown)
- **Parameter stability analysis**
- **Statistical summaries**
- **Trade-by-trade breakdown**

### 6.2 Key Metrics to Review

1. **Consistency**: Are returns consistent across periods?
2. **Parameter Stability**: Do optimal parameters change drastically?
3. **Drawdown**: Maximum loss periods
4. **Sharpe Ratio**: Risk-adjusted returns
5. **Win Rate**: Percentage of profitable trades

### 6.3 Files Generated

```
backtesting/
├── reports/
│   ├── walkforward_analysis_20241216_143022.png
│   ├── parameter_stability_20241216_143022.png
│   └── performance_summary_20241216_143022.html
├── results/
│   ├── walkforward_20241216_143022.csv
│   └── detailed_trades_20241216_143022.csv
└── exports/
    ├── optimal_parameters_20241216_143022.json
    └── mt5_config_20241216_143022.txt
```

---

## 📤 STEP 7: Export Optimal Parameters

### 7.1 Automatic Export

The system automatically exports optimal parameters in multiple formats:

**JSON Format** (`backtesting/exports/optimal_parameters_YYYYMMDD_HHMMSS.json`):
```json
{
    "strategy_name": "FirstCandleBreakout_Optimized",
    "optimization_date": "2024-12-16",
    "data_period": "2023-01-01 to 2024-01-01",
    "best_parameters": {
        "risk_percent": 1.5,
        "risk_reward_ratio": 2.5,
        "volume_multiplier": 1.2,
        "min_atr_multiplier": 1.0,
        "max_atr_multiplier": 3.0,
        "range_percentile": 70.0,
        "breakeven_ratio": 1.2,
        "trailing_pips": 15
    },
    "performance_metrics": {
        "total_return": 15.67,
        "win_rate": 65.2,
        "sharpe_ratio": 1.45,
        "max_drawdown": 8.34,
        "total_trades": 156
    },
    "validation_results": {
        "walkforward_periods": 12,
        "avg_test_return": 2.34,
        "consistency_score": 0.83
    }
}
```

**MT5 Configuration** (`backtesting/exports/mt5_config_YYYYMMDD_HHMMSS.txt`):
```
// Optimized parameters for FirstCandleBreakout EA
// Generated: 2024-12-16 14:30:22
// Validation: 12 periods, 83.3% positive

input double RiskPercent = 1.5;
input double RiskRewardRatio = 2.5;
input double VolumeMultiplier = 1.2;
input double MinATRMultiplier = 1.0;
input double MaxATRMultiplier = 3.0;
input double RangePercentile = 70.0;
input double BreakevenAtRatio = 1.2;
input int TrailingStopPips = 15;
```

### 7.2 Manual Export

```python
# Export specific results
from config import Config
import json

# Save best parameters
best_params = opt_results[0]['parameters']
export_file = Config.get_timestamped_filename('optimal_parameters', '.json', Config.EXPORTS_DIR)

with open(export_file, 'w') as f:
    json.dump({
        'parameters': best_params,
        'performance': opt_results[0],
        'validation': wf_results
    }, f, indent=2)

print(f"Parameters exported to: {export_file}")
```

---

## 🎯 STEP 8: Implementation in MT5

### 8.1 Update Your MT5 EA

1. **Open your MT5 EA file**
2. **Update input parameters** with optimized values
3. **Test on demo account** first
4. **Monitor performance** vs backtest results

### 8.2 Parameter Mapping

| Python Parameter | MT5 EA Parameter |
|-----------------|------------------|
| `risk_percent` | `RiskPercent` |
| `risk_reward_ratio` | `RiskRewardRatio` |
| `volume_multiplier` | `VolumeMultiplier` |
| `min_atr_multiplier` | `MinATRMultiplier` |
| `max_atr_multiplier` | `MaxATRMultiplier` |
| `range_percentile` | `RangePercentile` |
| `breakeven_ratio` | `BreakevenAtRatio` |
| `trailing_pips` | `TrailingStopPips` |

---

## 🔄 STEP 9: Ongoing Optimization

### 9.1 Regular Re-optimization

```bash
# Run monthly re-optimization
python monthly_reoptimization.py
```

### 9.2 Performance Monitoring

```bash
# Compare live results vs backtest
python performance_monitor.py
```

### 9.3 Data Updates

```bash
# Update data monthly
python update_data.py
```

---

## 📋 Quick Reference Commands

```bash
# Complete workflow
python setup_and_test.py                    # Initial setup
python test_api_key.py                      # Test API
streamlit run streamlit_app.py              # Web interface
python run_complete_analysis.py             # Full analysis
```

## 🆘 Troubleshooting

### Common Issues:
1. **API Rate Limits**: Wait 1 minute between requests
2. **Data Gaps**: Normal for weekends/holidays
3. **Memory Issues**: Reduce parameter combinations
4. **Slow Optimization**: Use fewer parameter combinations

### Support Files:
- Check `backtesting/logs/` for detailed error logs
- Review `backtesting/cache/` for temporary data
- All results timestamped for easy tracking

This complete workflow ensures you have a robust, validated trading strategy with optimal parameters ready for live implementation!
