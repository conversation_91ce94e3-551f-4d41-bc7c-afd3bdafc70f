#!/usr/bin/env python3
"""
Fixed Strategy Analysis - Completely revised approach with proper logic
"""

import numpy as np
import pandas as pd
import backtrader as bt
from datetime import datetime, time
import json
import os

# Import from previous files
from fixed_improved_ml_analysis import RobustFeatureEngine, ImbalancedMLPredictor

try:
    from sklearn.ensemble import RandomForestClassifier
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

class FixedFirstCandleStrategy(bt.Strategy):
    """Completely revised first candle strategy with proper logic"""
    
    params = (
        ('ml_confidence_threshold', 0.6),
        ('risk_reward_ratio', 2.0),
        ('session_start_hour', 8),
        ('max_trade_hours', 6),
        ('min_range_pips', 5.0),
        ('max_range_pips', 25.0),  # Add maximum range filter
        ('position_size', 1000),
    )
    
    def __init__(self):
        self.ml_predictions = {}
        self.daily_sessions = {}  # Track daily session data
        self.trades_log = []
        self.current_trade_info = None
        
    def set_ml_predictions(self, predictions_dict):
        self.ml_predictions = predictions_dict
        
    def get_ml_prediction(self):
        """Get ML prediction for current time"""
        current_dt = self.data.datetime.datetime()
        return self.ml_predictions.get(current_dt, 0.5)
    
    def identify_session_range(self):
        """Properly identify first hour session range"""
        current_dt = self.data.datetime.datetime()
        current_date = current_dt.date()
        current_time = current_dt.time()
        current_hour = current_time.hour
        current_minute = current_time.minute
        
        # Initialize daily session if not exists
        if current_date not in self.daily_sessions:
            self.daily_sessions[current_date] = {
                'session_high': None,
                'session_low': None,
                'session_complete': False,
                'breakout_attempted': False
            }
        
        session = self.daily_sessions[current_date]
        
        # Collect first hour data (8:00-9:00 AM)
        if current_hour == self.params.session_start_hour:
            if session['session_high'] is None:
                session['session_high'] = self.data.high[0]
                session['session_low'] = self.data.low[0]
            else:
                session['session_high'] = max(session['session_high'], self.data.high[0])
                session['session_low'] = min(session['session_low'], self.data.low[0])
            
            # Mark session complete at 9:00 AM
            if current_minute == 30:  # Last bar of first hour
                session['session_complete'] = True
        
        return session
    
    def next(self):
        current_dt = self.data.datetime.datetime()
        current_time = current_dt.time()
        current_hour = current_time.hour
        
        # Skip weekends and outside trading hours
        if current_dt.weekday() >= 5 or current_hour < 8 or current_hour > 17:
            return
        
        # Identify session range
        session = self.identify_session_range()
        
        # Only consider trades after session is complete (after 9:00 AM)
        if (current_hour >= self.params.session_start_hour + 1 and 
            session['session_complete'] and 
            not session['breakout_attempted'] and
            not self.position):
            
            self.check_breakout_opportunity(session)
        
        # Manage existing position
        if self.position:
            self.manage_position()
    
    def check_breakout_opportunity(self, session):
        """Check for valid breakout opportunity"""
        
        # Calculate session range
        session_high = session['session_high']
        session_low = session['session_low']
        range_pips = (session_high - session_low) * 10000
        
        # Range filters
        if range_pips < self.params.min_range_pips or range_pips > self.params.max_range_pips:
            session['breakout_attempted'] = True  # Mark as attempted to avoid multiple checks
            self.trades_log.append({
                'action': 'skipped_range',
                'range_pips': range_pips,
                'reason': f'Range {range_pips:.1f} pips outside limits'
            })
            return
        
        # Get ML prediction
        ml_confidence = self.get_ml_prediction()
        
        # ML filter
        if ml_confidence < self.params.ml_confidence_threshold:
            session['breakout_attempted'] = True
            self.trades_log.append({
                'action': 'skipped_ml',
                'ml_confidence': ml_confidence,
                'range_pips': range_pips,
                'reason': f'ML confidence {ml_confidence:.3f} below threshold'
            })
            return
        
        # Check for actual breakout
        current_price = self.data.close[0]
        buy_trigger = session_high + 0.0001  # 1 pip above session high
        sell_trigger = session_low - 0.0001   # 1 pip below session low
        
        # Execute breakout trade
        if current_price >= buy_trigger:
            self.execute_breakout_trade('buy', session, ml_confidence)
        elif current_price <= sell_trigger:
            self.execute_breakout_trade('sell', session, ml_confidence)
        else:
            # No breakout yet, but mark opportunity as checked for this bar
            pass
    
    def execute_breakout_trade(self, direction, session, ml_confidence):
        """Execute breakout trade with proper risk management"""
        
        session_high = session['session_high']
        session_low = session['session_low']
        range_pips = (session_high - session_low) * 10000
        
        if direction == 'buy':
            entry_price = session_high + 0.0001
            stop_loss = session_low - 0.0001
            take_profit = entry_price + (entry_price - stop_loss) * self.params.risk_reward_ratio
            
            # Only enter if price actually broke above
            if self.data.close[0] >= entry_price:
                self.buy(size=self.params.position_size)
                
                self.current_trade_info = {
                    'direction': 'buy',
                    'entry_price': entry_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'entry_time': self.data.datetime.datetime(),
                    'ml_confidence': ml_confidence,
                    'range_pips': range_pips
                }
                
                session['breakout_attempted'] = True
                
                self.trades_log.append({
                    'action': 'buy',
                    'entry_price': entry_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'ml_confidence': ml_confidence,
                    'range_pips': range_pips
                })
        
        elif direction == 'sell':
            entry_price = session_low - 0.0001
            stop_loss = session_high + 0.0001
            take_profit = entry_price - (stop_loss - entry_price) * self.params.risk_reward_ratio
            
            # Only enter if price actually broke below
            if self.data.close[0] <= entry_price:
                self.sell(size=self.params.position_size)
                
                self.current_trade_info = {
                    'direction': 'sell',
                    'entry_price': entry_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'entry_time': self.data.datetime.datetime(),
                    'ml_confidence': ml_confidence,
                    'range_pips': range_pips
                }
                
                session['breakout_attempted'] = True
                
                self.trades_log.append({
                    'action': 'sell',
                    'entry_price': entry_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'ml_confidence': ml_confidence,
                    'range_pips': range_pips
                })
    
    def manage_position(self):
        """Manage open position with proper exit logic"""
        
        if not self.current_trade_info:
            return
        
        # Time-based exit
        current_time = self.data.datetime.datetime()
        entry_time = self.current_trade_info['entry_time']
        hours_in_trade = (current_time - entry_time).total_seconds() / 3600
        
        if hours_in_trade >= self.params.max_trade_hours:
            self.close()
            self.current_trade_info = None
            return
        
        # Price-based exits
        current_price = self.data.close[0]
        direction = self.current_trade_info['direction']
        stop_loss = self.current_trade_info['stop_loss']
        take_profit = self.current_trade_info['take_profit']
        
        if direction == 'buy':
            if current_price <= stop_loss or current_price >= take_profit:
                self.close()
                self.current_trade_info = None
        elif direction == 'sell':
            if current_price >= stop_loss or current_price <= take_profit:
                self.close()
                self.current_trade_info = None

def create_realistic_labels(data):
    """Create more realistic breakout success labels"""
    
    print("🏷️ Creating realistic breakout labels...")
    
    labels = []
    
    for i in range(len(data)):
        current_time = data.index[i].time()
        current_hour = current_time.hour
        
        # Only create labels for 9 AM (after first hour session)
        if current_hour == 9 and current_time.minute == 0:
            
            # Get session data (previous 2 bars = first hour)
            if i >= 2:
                session_data = data.iloc[i-2:i]
                session_high = session_data['High'].max()
                session_low = session_data['Low'].min()
                range_pips = (session_high - session_low) * 10000
                
                if 5 <= range_pips <= 25:  # Valid range
                    
                    # Look forward to check breakout success
                    future_data = data.iloc[i:i+12]  # Next 6 hours
                    
                    if len(future_data) > 0:
                        # Check for successful breakout
                        max_high = future_data['High'].max()
                        min_low = future_data['Low'].min()
                        
                        # Breakout triggers
                        buy_trigger = session_high + 0.0001
                        sell_trigger = session_low - 0.0001
                        
                        # Success criteria (more realistic)
                        buy_success = False
                        sell_success = False
                        
                        if max_high >= buy_trigger:
                            # Check if 2:1 R:R achieved
                            stop_loss = session_low - 0.0001
                            target = buy_trigger + (buy_trigger - stop_loss) * 2.0
                            buy_success = max_high >= target
                        
                        if min_low <= sell_trigger:
                            # Check if 2:1 R:R achieved
                            stop_loss = session_high + 0.0001
                            target = sell_trigger - (stop_loss - sell_trigger) * 2.0
                            sell_success = min_low <= target
                        
                        if buy_success or sell_success:
                            labels.append(1)  # Successful
                        elif max_high >= buy_trigger or min_low <= sell_trigger:
                            labels.append(0)  # Breakout occurred but failed
                        else:
                            labels.append(-1)  # No breakout
                    else:
                        labels.append(-1)
                else:
                    labels.append(-1)  # Invalid range
            else:
                labels.append(-1)
        else:
            labels.append(-1)  # Not a trade time
    
    labels_series = pd.Series(labels, index=data.index)
    
    print(f"Label distribution:")
    print(f"  Successful breakouts: {(labels_series == 1).sum()}")
    print(f"  Failed breakouts: {(labels_series == 0).sum()}")
    print(f"  No breakouts: {(labels_series == -1).sum()}")
    
    if (labels_series == 1).sum() + (labels_series == 0).sum() > 0:
        success_rate = (labels_series == 1).sum() / ((labels_series == 1).sum() + (labels_series == 0).sum())
        print(f"  Success rate: {success_rate*100:.1f}%")
    
    return labels_series

def precompute_ml_predictions_fixed(data, ml_predictor, feature_engine):
    """Pre-compute ML predictions with better timing"""
    
    print("🧠 Pre-computing ML predictions...")
    
    predictions = {}
    
    for i in range(100, len(data)):
        current_time = data.index[i].time()
        
        # Only compute predictions for potential trade times (9 AM onwards)
        if current_time.hour >= 9:
            try:
                # Get window of data
                window_data = data.iloc[i-100:i+1].copy()
                
                # Extract features
                features = feature_engine.extract_features(window_data)
                
                if len(features) > 0:
                    current_features = features.iloc[-1]
                    prediction = ml_predictor.predict_probability(current_features)
                    predictions[data.index[i]] = prediction
                
            except Exception:
                predictions[data.index[i]] = 0.5
    
    print(f"✅ Pre-computed {len(predictions)} ML predictions")
    return predictions

def run_fixed_backtest(data, ml_predictions, params):
    """Run backtest with fixed strategy"""
    
    cerebro = bt.Cerebro()
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)
    
    cerebro.addstrategy(FixedFirstCandleStrategy, **params)
    
    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.0002)
    
    # Add analyzers
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    
    # Run backtest
    results = cerebro.run()
    strategy = results[0]
    
    # Set ML predictions
    strategy.set_ml_predictions(ml_predictions)
    
    final_value = cerebro.broker.getvalue()
    
    # Extract results
    trade_analyzer = strategy.analyzers.trades.get_analysis()
    sharpe_analyzer = strategy.analyzers.sharpe.get_analysis()
    drawdown_analyzer = strategy.analyzers.drawdown.get_analysis()
    
    # Calculate metrics
    total_return = (final_value - 10000) / 10000 * 100
    total_trades = trade_analyzer.get('total', {}).get('total', 0)
    winning_trades = trade_analyzer.get('won', {}).get('total', 0)
    losing_trades = trade_analyzer.get('lost', {}).get('total', 0)
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    max_drawdown = drawdown_analyzer.get('max', {}).get('drawdown', 0)
    sharpe_ratio = sharpe_analyzer.get('sharperatio', 0) or 0
    
    # Additional metrics
    avg_win = trade_analyzer.get('won', {}).get('pnl', {}).get('average', 0)
    avg_loss = trade_analyzer.get('lost', {}).get('pnl', {}).get('average', 0)
    profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 else 0
    
    return {
        'total_return': total_return,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'final_value': final_value,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'parameters': params,
        'trades_log': getattr(strategy, 'trades_log', [])
    }

def main():
    """Main function with completely fixed strategy"""

    print("🔧 FIXED STRATEGY ANALYSIS")
    print("=" * 50)
    print("Completely revised approach with proper logic:")
    print("✅ Proper session range identification")
    print("✅ Realistic breakout detection")
    print("✅ Correct risk management")
    print("✅ Better label creation")
    print("✅ Range filters (5-25 pips)")
    print()

    if not ML_AVAILABLE:
        print("❌ ML libraries not available")
        return False

    # Load data
    cleaned_files = [f for f in os.listdir('.') if 'cleaned.csv' in f]

    if not cleaned_files:
        print("❌ No cleaned CSV files found")
        return False

    print(f"📁 Using: {cleaned_files[0]}")
    data = pd.read_csv(cleaned_files[0], index_col=0, parse_dates=True)

    print(f"✅ Data loaded: {len(data)} bars")
    print(f"   Period: {data.index[0].date()} to {data.index[-1].date()}")

    # Extract features
    print("\n🔧 Extracting features...")
    feature_engine = RobustFeatureEngine()
    features = feature_engine.extract_features(data)

    # Create realistic labels
    labels = create_realistic_labels(data)

    # Align data
    common_index = features.index.intersection(labels.index)
    features_aligned = features.loc[common_index]
    labels_aligned = labels.loc[common_index]

    print(f"\nAligned data: {len(features_aligned)} samples")

    # Train ML model
    print("\n🧠 Training ML model...")
    ml_predictor = ImbalancedMLPredictor()
    success = ml_predictor.train(features_aligned, labels_aligned)

    if not success:
        print("❌ ML training failed")
        return False

    # Pre-compute predictions
    ml_predictions = precompute_ml_predictions_fixed(data, ml_predictor, feature_engine)

    # Test realistic parameter sets
    param_sets = [
        {
            'ml_confidence_threshold': 0.55,
            'risk_reward_ratio': 2.0,
            'min_range_pips': 5.0,
            'max_range_pips': 25.0,
            'max_trade_hours': 6
        },
        {
            'ml_confidence_threshold': 0.6,
            'risk_reward_ratio': 2.5,
            'min_range_pips': 8.0,
            'max_range_pips': 20.0,
            'max_trade_hours': 4
        },
        {
            'ml_confidence_threshold': 0.65,
            'risk_reward_ratio': 3.0,
            'min_range_pips': 10.0,
            'max_range_pips': 18.0,
            'max_trade_hours': 3
        }
    ]

    best_result = None
    best_params = None

    for i, params in enumerate(param_sets):
        print(f"\n📊 Testing fixed parameter set {i+1}/3:")
        print(f"   ML Threshold: {params['ml_confidence_threshold']}")
        print(f"   Risk:Reward: {params['risk_reward_ratio']}")
        print(f"   Range: {params['min_range_pips']}-{params['max_range_pips']} pips")
        print(f"   Max Hours: {params['max_trade_hours']}")

        try:
            result = run_fixed_backtest(data, ml_predictions, params)

            print(f"   Return: {result['total_return']:.2f}%")
            print(f"   Trades: {result['total_trades']}")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Sharpe: {result['sharpe_ratio']:.2f}")
            print(f"   Profit Factor: {result['profit_factor']:.2f}")
            print(f"   Max DD: {result['max_drawdown']:.2f}%")

            # Select best based on positive returns and good Sharpe
            if (result['total_return'] > 0 and
                result['total_trades'] >= 5 and
                result['sharpe_ratio'] > 0 and
                (best_result is None or
                 (result['sharpe_ratio'] > best_result['sharpe_ratio'] and
                  result['total_return'] > best_result['total_return']))):
                best_result = result
                best_params = params

        except Exception as e:
            print(f"   ❌ Error: {e}")
            continue

    # Display results
    if best_result and best_result['total_return'] > 0:
        print("\n🏆 FIXED STRATEGY RESULTS:")
        print("=" * 40)
        print(f"✅ SUCCESS! Found profitable strategy")
        print(f"Total Return: {best_result['total_return']:.2f}%")
        print(f"Win Rate: {best_result['win_rate']:.1f}%")
        print(f"Total Trades: {best_result['total_trades']}")
        print(f"Winning Trades: {best_result['winning_trades']}")
        print(f"Losing Trades: {best_result['losing_trades']}")
        print(f"Profit Factor: {best_result['profit_factor']:.2f}")
        print(f"Sharpe Ratio: {best_result['sharpe_ratio']:.2f}")
        print(f"Max Drawdown: {best_result['max_drawdown']:.2f}%")
        print(f"Average Win: ${best_result['avg_win']:.2f}")
        print(f"Average Loss: ${best_result['avg_loss']:.2f}")

        print(f"\n⚙️ Optimal Parameters:")
        for key, value in best_params.items():
            print(f"   {key}: {value}")

        # Analyze trades
        trades_log = best_result['trades_log']
        if trades_log:
            actual_trades = [t for t in trades_log if t['action'] in ['buy', 'sell']]
            skipped_trades = [t for t in trades_log if 'skipped' in t['action']]

            print(f"\n📈 Trade Analysis:")
            print(f"Actual Trades: {len(actual_trades)}")
            print(f"Skipped Trades: {len(skipped_trades)}")

            if actual_trades:
                avg_confidence = np.mean([t['ml_confidence'] for t in actual_trades])
                avg_range = np.mean([t['range_pips'] for t in actual_trades])
                print(f"Avg ML Confidence: {avg_confidence:.3f}")
                print(f"Avg Range: {avg_range:.1f} pips")

        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        result_file = f'backtesting/results/fixed_strategy_{timestamp}.json'

        os.makedirs('backtesting/results', exist_ok=True)

        with open(result_file, 'w') as f:
            json.dump({
                'strategy': 'Fixed_First_Candle_Breakout',
                'results': best_result,
                'best_parameters': best_params,
                'timestamp': timestamp,
                'data_period': f'{data.index[0].date()} to {data.index[-1].date()}'
            }, f, indent=2, default=str)

        print(f"\n💾 Results saved to: {result_file}")

        # Create MT5 parameters
        mt5_file = f'backtesting/exports/fixed_mt5_params_{timestamp}.txt'
        os.makedirs('backtesting/exports', exist_ok=True)

        with open(mt5_file, 'w') as f:
            f.write('// Fixed First Candle Breakout EA - Optimized Parameters\\n')
            f.write(f'// Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\\n')
            f.write(f'// Total Return: {best_result["total_return"]:.2f}%\\n')
            f.write(f'// Win Rate: {best_result["win_rate"]:.1f}%\\n')
            f.write(f'// Profit Factor: {best_result["profit_factor"]:.2f}\\n\\n')

            f.write('input double MLConfidenceThreshold = ' + str(best_params['ml_confidence_threshold']) + ';\\n')
            f.write('input double RiskRewardRatio = ' + str(best_params['risk_reward_ratio']) + ';\\n')
            f.write('input double MinRangePips = ' + str(best_params['min_range_pips']) + ';\\n')
            f.write('input double MaxRangePips = ' + str(best_params['max_range_pips']) + ';\\n')
            f.write('input int MaxTradeHours = ' + str(best_params['max_trade_hours']) + ';\\n')
            f.write('input int SessionStartHour = 8;\\n')

        print(f"💾 MT5 parameters saved to: {mt5_file}")

        print("\n🎉 STRATEGY FIXED SUCCESSFULLY!")
        print("✅ Positive returns achieved")
        print("✅ Realistic win rate")
        print("✅ Good risk-adjusted returns")
        print("✅ Ready for MT5 implementation")

    else:
        print("\n❌ STRATEGY STILL NEEDS WORK")
        print("Even with fixes, no profitable configuration found")
        print("\n💡 This suggests:")
        print("   - Market conditions not suitable for breakout strategy")
        print("   - Need different approach (trend following, mean reversion)")
        print("   - Try different timeframes (H1, H4)")
        print("   - Consider different currency pairs")

        if best_result:
            print(f"\nBest attempt: {best_result['total_return']:.2f}% return, {best_result['total_trades']} trades")

    return True

if __name__ == "__main__":
    main()
