//+------------------------------------------------------------------+
//|                                 FirstCandleBreakout_Advanced.mq5 |
//|                      Copyright 2025, Roo - AI Software Engineer |
//|                        Advanced with Medium Impact Features      |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Roo - AI Software Engineer"
#property link      ""
#property version   "3.00"
#property strict

#include <Trade\Trade.mqh>

//--- Custom Enumerations
enum ENUM_SESSION
{
    SESSION_LONDON,
    SESSION_NEW_YORK
};

enum ENUM_TREND_DIRECTION
{
    TREND_UP,
    TREND_DOWN,
    TREND_SIDEWAYS
};

enum ENUM_TRAILING_TYPE
{
    TRAILING_FIXED,      // Fixed pip trailing
    TRAILING_ATR,        // ATR-based trailing
    TRAILING_STEP,       // Step trailing
    TRAILING_ACCELERATION // Acceleration trailing
};

//--- EA Input Parameters
//--- Session and Magic Number
input group           "Session Settings"
input ENUM_SESSION    SessionToTrade      = SESSION_LONDON; // Session to trade (London or New York)
input ulong           MagicNumber         = 12345;          // Unique ID for EA's trades

//--- Risk Management
input group           "Risk Management"
input double          RiskPercent         = 1.0;            // Risk percentage of account equity per trade
input double          MaxSpread           = 2.0;            // Maximum allowed spread in pips
input double          RiskRewardRatio     = 2.0;            // Risk-to-Reward ratio for final Take Profit

//--- Enhanced Filters (from previous version)
input group           "Enhanced Filters"
input bool            UseVolumeFilter     = true;           // Enable volume confirmation filter
input double          VolumeMultiplier    = 1.2;            // Minimum volume multiplier vs average
input bool            UseATRFilter        = true;           // Enable ATR volatility filter
input int             ATRPeriod           = 14;             // ATR calculation period
input double          MinATRMultiplier    = 1.0;            // Minimum ATR multiplier vs average
input double          MaxATRMultiplier    = 3.0;            // Maximum ATR multiplier vs average

//--- NEW: Multiple Timeframe Analysis
input group           "Multiple Timeframe Analysis"
input bool            UseMultiTimeframe   = true;           // Enable multiple timeframe analysis
input ENUM_TIMEFRAMES HigherTimeframe     = PERIOD_H1;      // Higher timeframe for trend analysis
input int             TrendPeriod         = 20;             // Period for trend determination (EMA)
input bool            OnlyTrendDirection  = true;           // Only trade in direction of higher TF trend
input bool            UseHTFSupRes        = true;           // Use higher TF support/resistance levels

//--- NEW: Range-Based Filters
input group           "Range-Based Filters"
input bool            UseRangeFilter      = true;           // Enable range-based filtering
input double          MinRangePips        = 5.0;            // Minimum first candle range in pips
input double          MaxRangePips        = 50.0;           // Maximum first candle range in pips
input double          RangePercentile     = 70.0;           // Only trade if range is above this percentile
input int             RangeHistoryBars    = 50;             // Bars to analyze for range percentile

//--- NEW: Session-Specific Optimizations
input group           "Session-Specific Settings"
input bool            UseSessionOptimization = true;       // Enable session-specific parameters
// London Session Parameters
input double          London_RiskReward   = 2.0;            // London session R:R ratio
input double          London_MinRange     = 8.0;            // London minimum range in pips
input double          London_MaxRange     = 40.0;           // London maximum range in pips
input double          London_VolumeMulti  = 1.3;            // London volume multiplier
// New York Session Parameters  
input double          NY_RiskReward       = 2.5;            // New York session R:R ratio
input double          NY_MinRange         = 10.0;           // New York minimum range in pips
input double          NY_MaxRange         = 60.0;           // New York maximum range in pips
input double          NY_VolumeMulti      = 1.5;            // New York volume multiplier

//--- Multiple Profit Targets
input group           "Profit Management"
input bool            UseMultipleTargets  = true;           // Enable multiple profit targets
input double          FirstTargetRatio    = 1.0;            // First target R:R ratio
input double          SecondTargetRatio   = 1.5;            // Second target R:R ratio
input double          FinalTargetRatio    = 2.5;            // Final target R:R ratio
input double          FirstTargetPercent  = 30.0;           // % of position to close at first target
input double          SecondTargetPercent = 40.0;           // % of position to close at second target

//--- Order Settings
input group           "Order Settings"
input double          OrderOffsetPips     = 1.0;            // Pips to offset pending orders from the candle's high/low
input int             MaxSlippage         = 3;              // Maximum allowed slippage in points

//--- NEW: Advanced Trade Management
input group           "Advanced Trade Management"
input bool            UseDynamicBreakeven = true;           // Enable dynamic breakeven logic
input double          BreakevenAtRatio    = 1.2;            // Move to BE when position reaches this R:R ratio
input ENUM_TRAILING_TYPE TrailingType     = TRAILING_ATR;   // Type of trailing stop to use
input int             TrailingStopPips    = 15;             // Fixed trailing stop distance in pips
input double          ATRTrailingMulti    = 2.0;            // ATR multiplier for ATR trailing
input int             StepTrailingPips    = 10;             // Step size for step trailing
input double          AccelerationFactor  = 0.02;           // Acceleration factor for acceleration trailing
input int             MaxTradeHours       = 8;              // Maximum hours to keep trade open (0 to disable)

//--- Global Variables
//--- Session Timing
datetime sessionStartTime;
datetime sessionEndTime;
int      lastTradeDay = 0;

//--- First Candle Data
double   firstCandleHigh = 0;
double   firstCandleLow = 0;
bool     firstCandleIdentified = false;
double   firstCandleVolume = 0;

//--- Trade State
bool     tradePlacedForSession = false;

//--- Enhanced Variables
double   averageVolume = 0;
double   averageATR = 0;
double   currentATR = 0;

//--- NEW: Multiple Timeframe Variables
ENUM_TREND_DIRECTION higherTFTrend = TREND_SIDEWAYS;
double   htfSupport = 0;
double   htfResistance = 0;
bool     htfAnalysisValid = false;

//--- NEW: Range Analysis Variables
double   rangePercentileValue = 0;
double   rangeHistory[];

//--- NEW: Session-Specific Variables
double   currentSessionRR = 2.0;
double   currentSessionMinRange = 5.0;
double   currentSessionMaxRange = 50.0;
double   currentSessionVolumeMulti = 1.2;

//--- Position Management
struct PositionInfo
{
    double originalLotSize;
    double remainingLotSize;
    bool   firstTargetHit;
    bool   secondTargetHit;
    bool   movedToBreakeven;
    datetime openTime;
    double highestProfit;      // For acceleration trailing
    double accelerationValue;  // Current acceleration value
};
PositionInfo positionInfo;

//--- MQL5 Objects
CTrade   trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("==================================================");
    Print("FirstCandleBreakout Advanced EA v3.00 Initializing...");
    Print("DEBUG [INIT]: Symbol: ", _Symbol);
    Print("DEBUG [INIT]: Account Currency: ", AccountInfoString(ACCOUNT_CURRENCY));
    Print("DEBUG [INIT]: Account Equity: ", AccountInfoDouble(ACCOUNT_EQUITY));
    Print("DEBUG [INIT]: Session to Trade: ", (SessionToTrade == SESSION_LONDON ? "LONDON" : "NEW_YORK"));
    Print("DEBUG [INIT]: Magic Number: ", MagicNumber);
    Print("DEBUG [INIT]: Risk Percent: ", RiskPercent, "%");
    Print("DEBUG [INIT]: Max Spread: ", MaxSpread, " pips");
    
    // Enhanced features status
    Print("=== ENHANCED FEATURES ===");
    Print("DEBUG [INIT]: Volume Filter: ", (UseVolumeFilter ? "ENABLED" : "DISABLED"));
    Print("DEBUG [INIT]: ATR Filter: ", (UseATRFilter ? "ENABLED" : "DISABLED"));
    Print("DEBUG [INIT]: Multiple Targets: ", (UseMultipleTargets ? "ENABLED" : "DISABLED"));
    Print("DEBUG [INIT]: Dynamic Breakeven: ", (UseDynamicBreakeven ? "ENABLED" : "DISABLED"));
    
    // NEW: Advanced features status
    Print("=== ADVANCED FEATURES ===");
    Print("DEBUG [INIT]: Multiple Timeframe: ", (UseMultiTimeframe ? "ENABLED" : "DISABLED"));
    Print("DEBUG [INIT]: Higher Timeframe: ", EnumToString(HigherTimeframe));
    Print("DEBUG [INIT]: Range Filter: ", (UseRangeFilter ? "ENABLED" : "DISABLED"));
    Print("DEBUG [INIT]: Session Optimization: ", (UseSessionOptimization ? "ENABLED" : "DISABLED"));
    
    string trailingTypeStr = "";
    switch(TrailingType)
    {
        case TRAILING_FIXED: trailingTypeStr = "FIXED"; break;
        case TRAILING_ATR: trailingTypeStr = "ATR"; break;
        case TRAILING_STEP: trailingTypeStr = "STEP"; break;
        case TRAILING_ACCELERATION: trailingTypeStr = "ACCELERATION"; break;
    }
    Print("DEBUG [INIT]: Trailing Type: ", trailingTypeStr);
    
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(MaxSlippage);
    trade.SetTypeFillingBySymbol(_Symbol);
    Print("DEBUG [INIT]: Trade object configured successfully");

    // Initialize enhanced variables
    ResetPositionInfo();
    CalculateAverageVolume();
    CalculateAverageATR();
    
    // NEW: Initialize advanced features
    if (UseRangeFilter)
    {
        CalculateRangePercentile();
    }
    
    if (UseSessionOptimization)
    {
        UpdateSessionSpecificParameters();
    }

    // This ensures the session times are set correctly on the very first run
    lastTradeDay = TimeCurrent();
    Print("DEBUG [INIT]: Initial trade day set to: ", TimeToString(lastTradeDay));
    UpdateSessionTimings();
    Print("EA Initialized. Advanced session tracking is active.");
    Print("==================================================");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("==================================================");
    Print("DEBUG [DEINIT]: FirstCandleBreakout Advanced EA Deinitializing...");
    Print("DEBUG [DEINIT]: Reason code: ", reason);
    
    string reasonText = "";
    switch(reason)
    {
        case REASON_PROGRAM: reasonText = "EA terminated by user"; break;
        case REASON_REMOVE: reasonText = "EA removed from chart"; break;
        case REASON_RECOMPILE: reasonText = "EA recompiled"; break;
        case REASON_CHARTCHANGE: reasonText = "Chart symbol or period changed"; break;
        case REASON_CHARTCLOSE: reasonText = "Chart closed"; break;
        case REASON_PARAMETERS: reasonText = "Input parameters changed"; break;
        case REASON_ACCOUNT: reasonText = "Account changed"; break;
        case REASON_TEMPLATE: reasonText = "Template applied"; break;
        case REASON_INITFAILED: reasonText = "Initialization failed"; break;
        case REASON_CLOSE: reasonText = "Terminal closing"; break;
        default: reasonText = "Unknown reason"; break;
    }
    
    Print("DEBUG [DEINIT]: Reason: ", reasonText);
    Print("DEBUG [DEINIT]: Final state - firstCandleIdentified: ", firstCandleIdentified);
    Print("DEBUG [DEINIT]: Final state - tradePlacedForSession: ", tradePlacedForSession);
    
    // Check for any remaining orders or positions
    int pendingOrders = 0;
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderGetTicket(i) > 0 && OrderGetString(ORDER_SYMBOL) == _Symbol && OrderGetInteger(ORDER_MAGIC) == MagicNumber)
        {
            pendingOrders++;
        }
    }
    
    bool hasPosition = PositionSelect(_Symbol) && PositionGetInteger(POSITION_MAGIC) == MagicNumber;
    
    Print("DEBUG [DEINIT]: Pending orders remaining: ", pendingOrders);
    Print("DEBUG [DEINIT]: Position open: ", (hasPosition ? "YES" : "NO"));
    Print("DEBUG [DEINIT]: Deinitialization complete");
    Print("==================================================");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static int tickCount = 0;
    tickCount++;
    
    // Log every 100 ticks to avoid spam but show activity
    if (tickCount % 100 == 0)
    {
        Print("DEBUG [TICK]: Tick #", tickCount, " at ", TimeToString(TimeCurrent()));
    }

    // --- Daily Reset ---
    if (TimeCurrent() / (24 * 3600) != lastTradeDay / (24 * 3600))
    {
        Print("=== DEBUG [DAILY_RESET]: New day detected ===");
        Print("DEBUG [DAILY_RESET]: Previous day: ", TimeToString(lastTradeDay));
        Print("DEBUG [DAILY_RESET]: Current time: ", TimeToString(TimeCurrent()));
        Print("DEBUG [DAILY_RESET]: Resetting session variables...");
        
        lastTradeDay = TimeCurrent();
        tradePlacedForSession = false;
        firstCandleIdentified = false;
        ResetPositionInfo();
        
        // Recalculate averages for new day
        CalculateAverageVolume();
        CalculateAverageATR();
        
        // NEW: Recalculate advanced features for new day
        if (UseRangeFilter)
        {
            CalculateRangePercentile();
        }
        
        if (UseSessionOptimization)
        {
            UpdateSessionSpecificParameters();
        }
        
        Print("DEBUG [DAILY_RESET]: tradePlacedForSession = ", tradePlacedForSession);
        Print("DEBUG [DAILY_RESET]: firstCandleIdentified = ", firstCandleIdentified);
        
        UpdateSessionTimings();
        Print("=== DEBUG [DAILY_RESET]: Reset complete ===");
    }

    // --- Session Check ---
    bool sessionActive = IsTradeWindowActive();
    if (!sessionActive)
    {
        static datetime lastInactiveLog = 0;
        if (TimeCurrent() - lastInactiveLog > 3600) // Log every hour when inactive
        {
            Print("DEBUG [SESSION]: Outside trading window - managing existing trades only");
            lastInactiveLog = TimeCurrent();
        }
        ManageActiveTrades(); // Still manage trades outside the session window
        return;
    }
    else
    {
        static bool sessionStartLogged = false;
        if (!sessionStartLogged)
        {
            Print("=== DEBUG [SESSION]: Trading window is ACTIVE ===");
            sessionStartLogged = true;
        }
    }

    // --- First Candle Identification Logic ---
    if (!firstCandleIdentified)
    {
        Print("DEBUG [CANDLE]: Attempting to identify first candle...");
        if (!IdentifyFirstCandle())
        {
            Print("DEBUG [CANDLE]: First candle not yet identified, waiting...");
            return; // Wait for the next tick if candle not found yet
        }
        Print("DEBUG [CANDLE]: First candle successfully identified!");
    }

    // --- Advanced Pre-Trade Logic (Run once per session) ---
    if (firstCandleIdentified && !tradePlacedForSession)
    {
        Print("=== DEBUG [TRADE_SETUP]: Starting advanced trade setup process ===");
        Print("DEBUG [TRADE_SETUP]: First candle identified: ", firstCandleIdentified);
        Print("DEBUG [TRADE_SETUP]: Trade placed for session: ", tradePlacedForSession);
        
        if (!IsSpreadAcceptable())
        {
            Print("DEBUG [TRADE_SETUP]: Spread check FAILED - aborting trade setup");
            tradePlacedForSession = true;
            return;
        }
        Print("DEBUG [TRADE_SETUP]: Spread check PASSED");

        if (!PassesEnhancedFilters())
        {
            Print("DEBUG [TRADE_SETUP]: Enhanced filters FAILED - aborting trade setup");
            tradePlacedForSession = true;
            return;
        }
        Print("DEBUG [TRADE_SETUP]: Enhanced filters PASSED");

        // NEW: Advanced filters
        if (!PassesAdvancedFilters())
        {
            Print("DEBUG [TRADE_SETUP]: Advanced filters FAILED - aborting trade setup");
            tradePlacedForSession = true;
            return;
        }
        Print("DEBUG [TRADE_SETUP]: Advanced filters PASSED");

        double lotSize = CalculateLotSize();
        Print("DEBUG [TRADE_SETUP]: Calculated lot size: ", lotSize);
        
        if (lotSize <= 0)
        {
            Print("DEBUG [TRADE_SETUP]: Lot size calculation FAILED - aborting trade setup");
            tradePlacedForSession = true;
            return;
        }

        Print("DEBUG [TRADE_SETUP]: Proceeding to place advanced pending orders...");
        PlaceAdvancedPendingOrders(lotSize);
        tradePlacedForSession = true;
        Print("=== DEBUG [TRADE_SETUP]: Advanced trade setup process complete ===");
    }
    
    // --- Advanced Active Trade Management ---
    ManageActiveTrades();
}

//+------------------------------------------------------------------+
//| TIME MANAGEMENT FUNCTIONS                                        |
//+------------------------------------------------------------------+
void UpdateSessionTimings()
{
    Print("=== DEBUG [SESSION_TIMING]: Updating session timings ===");

    long gmtOffset = TimeGMTOffset();
    int sessionStartHourGMT = (SessionToTrade == SESSION_LONDON) ? 8 : 13; // London 8:00 GMT, NY 13:00 GMT

    Print("DEBUG [SESSION_TIMING]: Selected session: ", (SessionToTrade == SESSION_LONDON ? "LONDON" : "NEW_YORK"));
    Print("DEBUG [SESSION_TIMING]: Session start hour GMT: ", sessionStartHourGMT, ":00");
    Print("DEBUG [SESSION_TIMING]: GMT Offset: ", gmtOffset, " seconds (", gmtOffset/3600.0, " hours)");

    // Get today's date at 00:00 GMT
    datetime today = TimeCurrent() - (TimeCurrent() % (24 * 3600));
    Print("DEBUG [SESSION_TIMING]: Today's date (00:00 GMT): ", TimeToString(today));

    // Calculate session start time in GMT, then convert to broker time
    datetime sessionStartGMT = today + sessionStartHourGMT * 3600;
    sessionStartTime = sessionStartGMT + gmtOffset; // Add GMT offset to convert to broker time
    sessionEndTime = sessionStartTime + 9 * 3600; // 9-hour session window

    Print("DEBUG [SESSION_TIMING]: Session Start GMT: ", TimeToString(sessionStartGMT));
    Print("DEBUG [SESSION_TIMING]: Session Start Broker Time: ", TimeToString(sessionStartTime));
    Print("DEBUG [SESSION_TIMING]: Session End Broker Time: ", TimeToString(sessionEndTime));
    Print("DEBUG [SESSION_TIMING]: Current Broker Time: ", TimeToString(TimeCurrent()));
    Print("=== DEBUG [SESSION_TIMING]: Session timing update complete ===");
}

bool IsTradeWindowActive()
{
    datetime currentTime = TimeCurrent();
    bool isActive = (currentTime >= sessionStartTime && currentTime < sessionEndTime);

    // Enhanced diagnostic logging
    static datetime lastLogTime = 0;
    static bool lastActiveState = false;

    // Log when state changes or every hour
    if(isActive != lastActiveState || currentTime - lastLogTime > 3600)
    {
        Print("=== DEBUG [TRADE_WINDOW]: Trade Window Status Check ===");
        Print("DEBUG [TRADE_WINDOW]: Current Time: ", TimeToString(currentTime));
        Print("DEBUG [TRADE_WINDOW]: Session Start: ", TimeToString(sessionStartTime));
        Print("DEBUG [TRADE_WINDOW]: Session End: ", TimeToString(sessionEndTime));
        Print("DEBUG [TRADE_WINDOW]: Window Active: ", (isActive ? "TRUE" : "FALSE"));

        if (isActive)
        {
            long timeInSession = currentTime - sessionStartTime;
            Print("DEBUG [TRADE_WINDOW]: Time in session: ", timeInSession/60, " minutes");
        }
        else
        {
            if (currentTime < sessionStartTime)
            {
                long timeToStart = sessionStartTime - currentTime;
                Print("DEBUG [TRADE_WINDOW]: Time until session start: ", timeToStart/60, " minutes");
            }
            else
            {
                long timeAfterEnd = currentTime - sessionEndTime;
                Print("DEBUG [TRADE_WINDOW]: Time after session end: ", timeAfterEnd/60, " minutes");
            }
        }

        lastLogTime = currentTime;
        lastActiveState = isActive;
        Print("=== DEBUG [TRADE_WINDOW]: Status check complete ===");
    }

    return isActive;
}

//+------------------------------------------------------------------+
//| ENHANCED HELPER FUNCTIONS                                        |
//+------------------------------------------------------------------+
void ResetPositionInfo()
{
    positionInfo.originalLotSize = 0;
    positionInfo.remainingLotSize = 0;
    positionInfo.firstTargetHit = false;
    positionInfo.secondTargetHit = false;
    positionInfo.movedToBreakeven = false;
    positionInfo.openTime = 0;
    positionInfo.highestProfit = 0;
    positionInfo.accelerationValue = AccelerationFactor;
}

void CalculateAverageVolume()
{
    Print("=== DEBUG [VOLUME_CALC]: Calculating average volume ===");

    long volumes[];
    int volumesCopied = CopyTickVolume(_Symbol, PERIOD_M30, 1, 20, volumes);

    if (volumesCopied < 20)
    {
        Print("DEBUG [VOLUME_CALC]: WARNING - Only ", volumesCopied, " volume bars available");
        averageVolume = 1000; // Default fallback
        return;
    }

    long totalVolume = 0;
    for (int i = 0; i < volumesCopied; i++)
    {
        totalVolume += volumes[i];
    }

    averageVolume = (double)totalVolume / volumesCopied;
    Print("DEBUG [VOLUME_CALC]: Average volume over ", volumesCopied, " bars: ", averageVolume);
    Print("=== DEBUG [VOLUME_CALC]: Volume calculation complete ===");
}

void CalculateAverageATR()
{
    Print("=== DEBUG [ATR_CALC]: Calculating average ATR ===");

    double atrValues[];
    int atrHandle = iATR(_Symbol, PERIOD_M30, ATRPeriod);

    if (atrHandle == INVALID_HANDLE)
    {
        Print("DEBUG [ATR_CALC]: ERROR - Failed to create ATR indicator");
        averageATR = 0.001; // Default fallback
        return;
    }

    int atrCopied = CopyBuffer(atrHandle, 0, 1, 20, atrValues);

    if (atrCopied < 20)
    {
        Print("DEBUG [ATR_CALC]: WARNING - Only ", atrCopied, " ATR values available");
        averageATR = 0.001; // Default fallback
        IndicatorRelease(atrHandle);
        return;
    }

    double totalATR = 0;
    for (int i = 0; i < atrCopied; i++)
    {
        totalATR += atrValues[i];
    }

    averageATR = totalATR / atrCopied;

    // Get current ATR
    double currentATRArray[];
    if (CopyBuffer(atrHandle, 0, 0, 1, currentATRArray) > 0)
    {
        currentATR = currentATRArray[0];
    }

    Print("DEBUG [ATR_CALC]: Average ATR over ", atrCopied, " bars: ", averageATR);
    Print("DEBUG [ATR_CALC]: Current ATR: ", currentATR);
    Print("=== DEBUG [ATR_CALC]: ATR calculation complete ===");

    IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| NEW: ADVANCED HELPER FUNCTIONS                                   |
//+------------------------------------------------------------------+
void UpdateSessionSpecificParameters()
{
    Print("=== DEBUG [SESSION_OPT]: Updating session-specific parameters ===");

    if (SessionToTrade == SESSION_LONDON)
    {
        currentSessionRR = London_RiskReward;
        currentSessionMinRange = London_MinRange;
        currentSessionMaxRange = London_MaxRange;
        currentSessionVolumeMulti = London_VolumeMulti;
        Print("DEBUG [SESSION_OPT]: Using LONDON parameters");
    }
    else
    {
        currentSessionRR = NY_RiskReward;
        currentSessionMinRange = NY_MinRange;
        currentSessionMaxRange = NY_MaxRange;
        currentSessionVolumeMulti = NY_VolumeMulti;
        Print("DEBUG [SESSION_OPT]: Using NEW YORK parameters");
    }

    Print("DEBUG [SESSION_OPT]: R:R Ratio: ", currentSessionRR);
    Print("DEBUG [SESSION_OPT]: Min Range: ", currentSessionMinRange, " pips");
    Print("DEBUG [SESSION_OPT]: Max Range: ", currentSessionMaxRange, " pips");
    Print("DEBUG [SESSION_OPT]: Volume Multiplier: ", currentSessionVolumeMulti);
    Print("=== DEBUG [SESSION_OPT]: Session parameters updated ===");
}

void CalculateRangePercentile()
{
    Print("=== DEBUG [RANGE_CALC]: Calculating range percentile ===");

    MqlRates rates[];
    int ratesCopied = CopyRates(_Symbol, PERIOD_M30, 1, RangeHistoryBars, rates);

    if (ratesCopied < RangeHistoryBars)
    {
        Print("DEBUG [RANGE_CALC]: WARNING - Only ", ratesCopied, " bars available for range analysis");
        rangePercentileValue = 0.001; // Default fallback
        return;
    }

    // Calculate ranges for all bars
    ArrayResize(rangeHistory, ratesCopied);
    for (int i = 0; i < ratesCopied; i++)
    {
        rangeHistory[i] = (rates[i].high - rates[i].low) / (_Point * 10); // Convert to pips
    }

    // Sort ranges to find percentile
    ArraySort(rangeHistory);

    int percentileIndex = (int)((RangePercentile / 100.0) * (ratesCopied - 1));
    rangePercentileValue = rangeHistory[percentileIndex];

    Print("DEBUG [RANGE_CALC]: Range percentile (", RangePercentile, "%): ", NormalizeDouble(rangePercentileValue, 1), " pips");
    Print("DEBUG [RANGE_CALC]: Based on ", ratesCopied, " historical bars");
    Print("=== DEBUG [RANGE_CALC]: Range percentile calculation complete ===");
}

ENUM_TREND_DIRECTION AnalyzeHigherTimeframeTrend()
{
    Print("=== DEBUG [HTF_TREND]: Analyzing higher timeframe trend ===");

    double emaValues[];
    int emaHandle = iMA(_Symbol, HigherTimeframe, TrendPeriod, 0, MODE_EMA, PRICE_CLOSE);

    if (emaHandle == INVALID_HANDLE)
    {
        Print("DEBUG [HTF_TREND]: ERROR - Failed to create EMA indicator");
        return TREND_SIDEWAYS;
    }

    int emaCopied = CopyBuffer(emaHandle, 0, 0, 3, emaValues);

    if (emaCopied < 3)
    {
        Print("DEBUG [HTF_TREND]: ERROR - Could not copy EMA values");
        IndicatorRelease(emaHandle);
        return TREND_SIDEWAYS;
    }

    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double currentEMA = emaValues[0];
    double prevEMA = emaValues[1];
    double prevPrevEMA = emaValues[2];

    Print("DEBUG [HTF_TREND]: Current Price: ", currentPrice);
    Print("DEBUG [HTF_TREND]: Current EMA: ", currentEMA);
    Print("DEBUG [HTF_TREND]: Previous EMA: ", prevEMA);
    Print("DEBUG [HTF_TREND]: EMA 2 bars ago: ", prevPrevEMA);

    ENUM_TREND_DIRECTION trend = TREND_SIDEWAYS;

    // Determine trend based on EMA slope and price position
    bool emaRising = (currentEMA > prevEMA && prevEMA > prevPrevEMA);
    bool emaFalling = (currentEMA < prevEMA && prevEMA < prevPrevEMA);
    bool priceAboveEMA = (currentPrice > currentEMA);
    bool priceBelowEMA = (currentPrice < currentEMA);

    if (emaRising && priceAboveEMA)
    {
        trend = TREND_UP;
        Print("DEBUG [HTF_TREND]: Trend identified as UP");
    }
    else if (emaFalling && priceBelowEMA)
    {
        trend = TREND_DOWN;
        Print("DEBUG [HTF_TREND]: Trend identified as DOWN");
    }
    else
    {
        trend = TREND_SIDEWAYS;
        Print("DEBUG [HTF_TREND]: Trend identified as SIDEWAYS");
    }

    IndicatorRelease(emaHandle);
    Print("=== DEBUG [HTF_TREND]: Higher timeframe trend analysis complete ===");

    return trend;
}

void CalculateHTFSupportResistance()
{
    Print("=== DEBUG [HTF_SUPRES]: Calculating higher timeframe support/resistance ===");

    MqlRates htfRates[];
    int htfRatesCopied = CopyRates(_Symbol, HigherTimeframe, 1, 20, htfRates);

    if (htfRatesCopied < 20)
    {
        Print("DEBUG [HTF_SUPRES]: WARNING - Only ", htfRatesCopied, " HTF bars available");
        htfSupport = 0;
        htfResistance = 0;
        return;
    }

    // Find recent swing highs and lows
    double recentHighs[];
    double recentLows[];
    ArrayResize(recentHighs, 0);
    ArrayResize(recentLows, 0);

    for (int i = 1; i < htfRatesCopied - 1; i++)
    {
        // Check for swing high
        if (htfRates[i].high > htfRates[i-1].high && htfRates[i].high > htfRates[i+1].high)
        {
            int newSize = ArraySize(recentHighs) + 1;
            ArrayResize(recentHighs, newSize);
            recentHighs[newSize-1] = htfRates[i].high;
        }

        // Check for swing low
        if (htfRates[i].low < htfRates[i-1].low && htfRates[i].low < htfRates[i+1].low)
        {
            int newSize = ArraySize(recentLows) + 1;
            ArrayResize(recentLows, newSize);
            recentLows[newSize-1] = htfRates[i].low;
        }
    }

    // Find nearest support and resistance
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    htfResistance = 0;
    htfSupport = 0;

    // Find nearest resistance above current price
    for (int i = 0; i < ArraySize(recentHighs); i++)
    {
        if (recentHighs[i] > currentPrice)
        {
            if (htfResistance == 0 || recentHighs[i] < htfResistance)
            {
                htfResistance = recentHighs[i];
            }
        }
    }

    // Find nearest support below current price
    for (int i = 0; i < ArraySize(recentLows); i++)
    {
        if (recentLows[i] < currentPrice)
        {
            if (htfSupport == 0 || recentLows[i] > htfSupport)
            {
                htfSupport = recentLows[i];
            }
        }
    }

    Print("DEBUG [HTF_SUPRES]: Current Price: ", currentPrice);
    Print("DEBUG [HTF_SUPRES]: HTF Resistance: ", htfResistance);
    Print("DEBUG [HTF_SUPRES]: HTF Support: ", htfSupport);
    Print("DEBUG [HTF_SUPRES]: Found ", ArraySize(recentHighs), " swing highs and ", ArraySize(recentLows), " swing lows");
    Print("=== DEBUG [HTF_SUPRES]: Support/resistance calculation complete ===");
}

//+------------------------------------------------------------------+
//| ADVANCED CORE STRATEGY FUNCTIONS                                |
//+------------------------------------------------------------------+
bool IdentifyFirstCandle()
{
    Print("=== DEBUG [FIRST_CANDLE]: Starting advanced first candle identification ===");

    MqlRates rates[];
    int ratesCopied = CopyRates(_Symbol, PERIOD_M30, 0, 10, rates);

    Print("DEBUG [FIRST_CANDLE]: Requested 10 M30 candles, copied: ", ratesCopied);

    if(ratesCopied < 10)
    {
        Print("DEBUG [FIRST_CANDLE]: ERROR - Could not copy sufficient M30 rates");
        Print("DEBUG [FIRST_CANDLE]: Only ", ratesCopied, " candles available");
        return false;
    }

    Print("DEBUG [FIRST_CANDLE]: Session start time: ", TimeToString(sessionStartTime));
    Print("DEBUG [FIRST_CANDLE]: Current time: ", TimeToString(TimeCurrent()));

    // Look for the first complete candle that starts at or after the session start time
    for(int i = 1; i < 10; i++) // Start from index 1 (most recent closed candle)
    {
        datetime candleOpenTime = rates[i].time;
        datetime candleCloseTime = candleOpenTime + 30 * 60; // 30 minutes later

        Print("DEBUG [FIRST_CANDLE]: Checking candle #", i);
        Print("DEBUG [FIRST_CANDLE]: Candle open time: ", TimeToString(candleOpenTime));
        Print("DEBUG [FIRST_CANDLE]: Candle close time: ", TimeToString(candleCloseTime));
        Print("DEBUG [FIRST_CANDLE]: Candle OHLC: O=", rates[i].open, " H=", rates[i].high,
              " L=", rates[i].low, " C=", rates[i].close);

        bool startsAfterSession = (candleOpenTime >= sessionStartTime);
        bool isComplete = (TimeCurrent() >= candleCloseTime);

        Print("DEBUG [FIRST_CANDLE]: Starts after session: ", startsAfterSession);
        Print("DEBUG [FIRST_CANDLE]: Is complete: ", isComplete);

        // Check if this candle starts at or after session start and is complete
        if (startsAfterSession && isComplete)
        {
            firstCandleHigh = rates[i].high;
            firstCandleLow = rates[i].low;
            firstCandleIdentified = true;

            // Get volume for this candle
            long volumes[];
            if (CopyTickVolume(_Symbol, PERIOD_M30, i, 1, volumes) > 0)
            {
                firstCandleVolume = (double)volumes[0];
            }
            else
            {
                firstCandleVolume = 0;
                Print("DEBUG [FIRST_CANDLE]: WARNING - Could not get volume data");
            }

            double candleRangePips = (firstCandleHigh - firstCandleLow) / (_Point * 10);

            Print("=== DEBUG [FIRST_CANDLE]: FIRST CANDLE IDENTIFIED! ===");
            Print("DEBUG [FIRST_CANDLE]: Candle time: ", TimeToString(candleOpenTime));
            Print("DEBUG [FIRST_CANDLE]: High: ", NormalizeDouble(firstCandleHigh, _Digits));
            Print("DEBUG [FIRST_CANDLE]: Low: ", NormalizeDouble(firstCandleLow, _Digits));
            Print("DEBUG [FIRST_CANDLE]: Range: ", NormalizeDouble(candleRangePips, 1), " pips");
            Print("DEBUG [FIRST_CANDLE]: Volume: ", firstCandleVolume);
            Print("=== DEBUG [FIRST_CANDLE]: Identification complete ===");
            return true;
        }
        else
        {
            Print("DEBUG [FIRST_CANDLE]: Candle #", i, " does not meet criteria");
        }
    }

    Print("DEBUG [FIRST_CANDLE]: No suitable first candle found yet");
    Print("=== DEBUG [FIRST_CANDLE]: Identification attempt complete ===");
    return false;
}

bool PassesEnhancedFilters()
{
    Print("=== DEBUG [ENHANCED_FILTERS]: Starting enhanced filter checks ===");

    bool allFiltersPassed = true;

    // Use session-specific volume multiplier if enabled
    double volumeMultiplier = UseSessionOptimization ? currentSessionVolumeMulti : VolumeMultiplier;

    // Volume Filter
    if (UseVolumeFilter)
    {
        Print("--- DEBUG [VOLUME_FILTER]: Checking volume filter ---");
        Print("DEBUG [VOLUME_FILTER]: First candle volume: ", firstCandleVolume);
        Print("DEBUG [VOLUME_FILTER]: Average volume: ", averageVolume);
        Print("DEBUG [VOLUME_FILTER]: Required multiplier: ", volumeMultiplier);

        double requiredVolume = averageVolume * volumeMultiplier;
        bool volumePassed = (firstCandleVolume >= requiredVolume);

        Print("DEBUG [VOLUME_FILTER]: Required volume: ", requiredVolume);
        Print("DEBUG [VOLUME_FILTER]: Volume filter: ", (volumePassed ? "PASSED" : "FAILED"));

        if (!volumePassed)
        {
            allFiltersPassed = false;
            Print("DEBUG [VOLUME_FILTER]: Volume too low - rejecting trade");
        }
    }
    else
    {
        Print("DEBUG [ENHANCED_FILTERS]: Volume filter DISABLED");
    }

    // ATR Filter
    if (UseATRFilter)
    {
        Print("--- DEBUG [ATR_FILTER]: Checking ATR filter ---");
        Print("DEBUG [ATR_FILTER]: Current ATR: ", currentATR);
        Print("DEBUG [ATR_FILTER]: Average ATR: ", averageATR);
        Print("DEBUG [ATR_FILTER]: Min multiplier: ", MinATRMultiplier);
        Print("DEBUG [ATR_FILTER]: Max multiplier: ", MaxATRMultiplier);

        double minATR = averageATR * MinATRMultiplier;
        double maxATR = averageATR * MaxATRMultiplier;
        bool atrPassed = (currentATR >= minATR && currentATR <= maxATR);

        Print("DEBUG [ATR_FILTER]: Min required ATR: ", minATR);
        Print("DEBUG [ATR_FILTER]: Max allowed ATR: ", maxATR);
        Print("DEBUG [ATR_FILTER]: ATR filter: ", (atrPassed ? "PASSED" : "FAILED"));

        if (!atrPassed)
        {
            allFiltersPassed = false;
            if (currentATR < minATR)
                Print("DEBUG [ATR_FILTER]: ATR too low - market not volatile enough");
            else
                Print("DEBUG [ATR_FILTER]: ATR too high - market too volatile");
        }
    }
    else
    {
        Print("DEBUG [ENHANCED_FILTERS]: ATR filter DISABLED");
    }

    Print("DEBUG [ENHANCED_FILTERS]: All enhanced filters passed: ", (allFiltersPassed ? "YES" : "NO"));
    Print("=== DEBUG [ENHANCED_FILTERS]: Enhanced filter checks complete ===");

    return allFiltersPassed;
}

bool PassesAdvancedFilters()
{
    Print("=== DEBUG [ADVANCED_FILTERS]: Starting advanced filter checks ===");

    bool allFiltersPassed = true;

    // Multiple Timeframe Analysis
    if (UseMultiTimeframe)
    {
        Print("--- DEBUG [MTF_FILTER]: Checking multiple timeframe filter ---");

        // Analyze higher timeframe trend
        higherTFTrend = AnalyzeHigherTimeframeTrend();

        // Calculate HTF support/resistance if enabled
        if (UseHTFSupRes)
        {
            CalculateHTFSupportResistance();
        }

        htfAnalysisValid = true;

        string trendStr = (higherTFTrend == TREND_UP) ? "UP" : (higherTFTrend == TREND_DOWN) ? "DOWN" : "SIDEWAYS";
        Print("DEBUG [MTF_FILTER]: Higher TF trend: ", trendStr);

        // If only trading in trend direction, check alignment
        if (OnlyTrendDirection && higherTFTrend == TREND_SIDEWAYS)
        {
            allFiltersPassed = false;
            Print("DEBUG [MTF_FILTER]: Higher timeframe trend is SIDEWAYS - rejecting trade");
        }
        else
        {
            Print("DEBUG [MTF_FILTER]: Multiple timeframe filter: PASSED");
        }
    }
    else
    {
        Print("DEBUG [ADVANCED_FILTERS]: Multiple timeframe filter DISABLED");
    }

    // Range-Based Filter
    if (UseRangeFilter)
    {
        Print("--- DEBUG [RANGE_FILTER]: Checking range-based filter ---");

        double firstCandleRangePips = (firstCandleHigh - firstCandleLow) / (_Point * 10);

        // Use session-specific range limits if enabled
        double minRange = UseSessionOptimization ? currentSessionMinRange : MinRangePips;
        double maxRange = UseSessionOptimization ? currentSessionMaxRange : MaxRangePips;

        Print("DEBUG [RANGE_FILTER]: First candle range: ", NormalizeDouble(firstCandleRangePips, 1), " pips");
        Print("DEBUG [RANGE_FILTER]: Min range: ", minRange, " pips");
        Print("DEBUG [RANGE_FILTER]: Max range: ", maxRange, " pips");
        Print("DEBUG [RANGE_FILTER]: Range percentile threshold: ", rangePercentileValue, " pips");

        bool rangeInBounds = (firstCandleRangePips >= minRange && firstCandleRangePips <= maxRange);
        bool rangeAbovePercentile = (firstCandleRangePips >= rangePercentileValue);

        Print("DEBUG [RANGE_FILTER]: Range in bounds: ", (rangeInBounds ? "YES" : "NO"));
        Print("DEBUG [RANGE_FILTER]: Range above percentile: ", (rangeAbovePercentile ? "YES" : "NO"));

        if (!rangeInBounds)
        {
            allFiltersPassed = false;
            if (firstCandleRangePips < minRange)
                Print("DEBUG [RANGE_FILTER]: Range too small - rejecting trade");
            else
                Print("DEBUG [RANGE_FILTER]: Range too large - rejecting trade");
        }
        else if (!rangeAbovePercentile)
        {
            allFiltersPassed = false;
            Print("DEBUG [RANGE_FILTER]: Range below percentile threshold - rejecting trade");
        }
        else
        {
            Print("DEBUG [RANGE_FILTER]: Range filter: PASSED");
        }
    }
    else
    {
        Print("DEBUG [ADVANCED_FILTERS]: Range filter DISABLED");
    }

    Print("DEBUG [ADVANCED_FILTERS]: All advanced filters passed: ", (allFiltersPassed ? "YES" : "NO"));
    Print("=== DEBUG [ADVANCED_FILTERS]: Advanced filter checks complete ===");

    return allFiltersPassed;
}

bool IsSpreadAcceptable()
{
    Print("=== DEBUG [SPREAD_CHECK]: Checking spread conditions ===");

    long spreadPoints = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD);
    double currentSpread = spreadPoints * _Point;
    double currentSpreadPips = currentSpread / (10 * _Point);
    double maxSpreadPips = MaxSpread;

    Print("DEBUG [SPREAD_CHECK]: Current spread (points): ", spreadPoints);
    Print("DEBUG [SPREAD_CHECK]: Current spread (pips): ", NormalizeDouble(currentSpreadPips, 1));
    Print("DEBUG [SPREAD_CHECK]: Maximum allowed spread (pips): ", maxSpreadPips);

    bool acceptable = (currentSpreadPips <= maxSpreadPips);

    Print("DEBUG [SPREAD_CHECK]: Spread acceptable: ", (acceptable ? "YES" : "NO"));

    if (!acceptable)
    {
        Print("DEBUG [SPREAD_CHECK]: SPREAD CHECK FAILED!");
        Print("DEBUG [SPREAD_CHECK]: Current: ", NormalizeDouble(currentSpreadPips, 1),
              " pips > Max allowed: ", maxSpreadPips, " pips");
    }

    Print("=== DEBUG [SPREAD_CHECK]: Spread check complete ===");
    return acceptable;
}

double CalculateLotSize()
{
    Print("=== DEBUG [LOT_CALC]: Starting lot size calculation ===");

    double accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    double riskAmount = accountEquity * (RiskPercent / 100.0);
    double stopLossPips = (firstCandleHigh - firstCandleLow) / (_Point * 10);

    Print("DEBUG [LOT_CALC]: Account equity: ", accountEquity);
    Print("DEBUG [LOT_CALC]: Risk percentage: ", RiskPercent, "%");
    Print("DEBUG [LOT_CALC]: Risk amount: ", riskAmount);
    Print("DEBUG [LOT_CALC]: First candle high: ", firstCandleHigh);
    Print("DEBUG [LOT_CALC]: First candle low: ", firstCandleLow);
    Print("DEBUG [LOT_CALC]: Stop loss distance: ", NormalizeDouble(stopLossPips, 1), " pips");

    if (stopLossPips <= 0)
    {
        Print("DEBUG [LOT_CALC]: ERROR - Stop loss distance is zero or negative!");
        return 0;
    }

    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    Print("DEBUG [LOT_CALC]: Symbol digits: ", _Digits);
    Print("DEBUG [LOT_CALC]: Point value: ", _Point);
    Print("DEBUG [LOT_CALC]: Tick value: ", tickValue);
    Print("DEBUG [LOT_CALC]: Tick size: ", tickSize);

    // Correctly determine pip size based on symbol's digits
    double pointValue = _Point;
    if(_Digits == 3 || _Digits == 5)
    {
        pointValue = _Point * 10;
        Print("DEBUG [LOT_CALC]: Using 10x point value for 3/5 digit symbol");
    }

    double valuePerPip = tickValue * (pointValue / tickSize);
    Print("DEBUG [LOT_CALC]: Value per pip: ", valuePerPip);

    double riskPerLot = stopLossPips * valuePerPip;
    double lotSize = (riskPerLot > 0) ? riskAmount / riskPerLot : 0;

    Print("DEBUG [LOT_CALC]: Risk per lot: ", riskPerLot);
    Print("DEBUG [LOT_CALC]: Initial lot size calculation: ", lotSize);

    // Get symbol constraints
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    Print("DEBUG [LOT_CALC]: Symbol constraints - Min: ", minLot, ", Max: ", maxLot, ", Step: ", lotStep);

    // Normalize lot size to step
    lotSize = floor(lotSize / lotStep) * lotStep;
    Print("DEBUG [LOT_CALC]: Lot size after step normalization: ", lotSize);

    // Apply constraints
    if (lotSize < minLot)
    {
        Print("DEBUG [LOT_CALC]: Lot size below minimum, adjusting from ", lotSize, " to ", minLot);
        lotSize = minLot;
    }
    if (lotSize > maxLot)
    {
        Print("DEBUG [LOT_CALC]: Lot size above maximum, adjusting from ", lotSize, " to ", maxLot);
        lotSize = maxLot;
    }

    // Final risk validation
    double actualRisk = lotSize * riskPerLot;
    double actualRiskPercent = (actualRisk / accountEquity) * 100;

    Print("DEBUG [LOT_CALC]: Final lot size: ", lotSize);
    Print("DEBUG [LOT_CALC]: Actual risk amount: ", actualRisk);
    Print("DEBUG [LOT_CALC]: Actual risk percentage: ", NormalizeDouble(actualRiskPercent, 2), "%");

    if(actualRiskPercent > RiskPercent && lotSize == minLot)
    {
        Print("DEBUG [LOT_CALC]: WARNING - Minimum lot size exceeds desired risk percentage!");
    }

    Print("=== DEBUG [LOT_CALC]: Lot size calculation complete ===");
    return lotSize;
}

void PlaceAdvancedPendingOrders(double lotSize)
{
    Print("=== DEBUG [ADVANCED_ORDER]: Starting advanced pending order placement ===");

    double point = _Point;
    double offset = OrderOffsetPips * 10 * point;

    Print("DEBUG [ADVANCED_ORDER]: Lot size: ", lotSize);
    Print("DEBUG [ADVANCED_ORDER]: Point value: ", point);
    Print("DEBUG [ADVANCED_ORDER]: Offset pips: ", OrderOffsetPips);
    Print("DEBUG [ADVANCED_ORDER]: Offset price: ", offset);

    // Store original lot size for position management
    positionInfo.originalLotSize = lotSize;
    positionInfo.remainingLotSize = lotSize;

    // Use session-specific R:R ratio if enabled
    double finalRR = UseSessionOptimization ? currentSessionRR : RiskRewardRatio;
    Print("DEBUG [ADVANCED_ORDER]: Using R:R ratio: ", finalRR);

    // Calculate target levels
    double buyPrice = NormalizeDouble(firstCandleHigh + offset, _Digits);
    double buySL = NormalizeDouble(firstCandleLow, _Digits);
    double stopDistance = buyPrice - buySL;

    double sellPrice = NormalizeDouble(firstCandleLow - offset, _Digits);
    double sellSL = NormalizeDouble(firstCandleHigh, _Digits);
    double sellStopDistance = sellSL - sellPrice;

    Print("DEBUG [ADVANCED_ORDER]: Buy setup - Price: ", buyPrice, ", SL: ", buySL, ", Distance: ", NormalizeDouble(stopDistance / (_Point * 10), 1), " pips");
    Print("DEBUG [ADVANCED_ORDER]: Sell setup - Price: ", sellPrice, ", SL: ", sellSL, ", Distance: ", NormalizeDouble(sellStopDistance / (_Point * 10), 1), " pips");

    // Check HTF support/resistance confluence if enabled
    bool buyConfluentWithHTF = true;
    bool sellConfluentWithHTF = true;

    if (UseMultiTimeframe && UseHTFSupRes && htfAnalysisValid)
    {
        Print("--- DEBUG [HTF_CONFLUENCE]: Checking HTF confluence ---");

        if (htfResistance > 0)
        {
            double distanceToResistance = (htfResistance - buyPrice) / (_Point * 10);
            Print("DEBUG [HTF_CONFLUENCE]: Buy target distance to HTF resistance: ", NormalizeDouble(distanceToResistance, 1), " pips");

            if (distanceToResistance < 10) // Too close to resistance
            {
                buyConfluentWithHTF = false;
                Print("DEBUG [HTF_CONFLUENCE]: Buy target too close to HTF resistance - may reduce target");
            }
        }

        if (htfSupport > 0)
        {
            double distanceToSupport = (sellPrice - htfSupport) / (_Point * 10);
            Print("DEBUG [HTF_CONFLUENCE]: Sell target distance to HTF support: ", NormalizeDouble(distanceToSupport, 1), " pips");

            if (distanceToSupport < 10) // Too close to support
            {
                sellConfluentWithHTF = false;
                Print("DEBUG [HTF_CONFLUENCE]: Sell target too close to HTF support - may reduce target");
            }
        }
    }

    // Calculate multiple target levels for buy
    double buyTP1 = 0, buyTP2 = 0, buyTP3 = 0;
    if (UseMultipleTargets)
    {
        buyTP1 = NormalizeDouble(buyPrice + (stopDistance * FirstTargetRatio), _Digits);
        buyTP2 = NormalizeDouble(buyPrice + (stopDistance * SecondTargetRatio), _Digits);
        buyTP3 = NormalizeDouble(buyPrice + (stopDistance * finalRR), _Digits);

        // Adjust final target if too close to HTF resistance
        if (!buyConfluentWithHTF && htfResistance > 0 && buyTP3 > htfResistance)
        {
            buyTP3 = NormalizeDouble(htfResistance - (5 * 10 * _Point), _Digits); // 5 pips before resistance
            Print("DEBUG [HTF_CONFLUENCE]: Adjusted buy TP3 due to HTF resistance: ", buyTP3);
        }

        Print("DEBUG [ADVANCED_ORDER]: Buy targets - TP1: ", buyTP1, " (", FirstTargetRatio, "R)");
        Print("DEBUG [ADVANCED_ORDER]: Buy targets - TP2: ", buyTP2, " (", SecondTargetRatio, "R)");
        Print("DEBUG [ADVANCED_ORDER]: Buy targets - TP3: ", buyTP3, " (", finalRR, "R)");
    }
    else
    {
        buyTP3 = NormalizeDouble(buyPrice + (stopDistance * finalRR), _Digits);

        // Adjust if too close to HTF resistance
        if (!buyConfluentWithHTF && htfResistance > 0 && buyTP3 > htfResistance)
        {
            buyTP3 = NormalizeDouble(htfResistance - (5 * 10 * _Point), _Digits);
            Print("DEBUG [HTF_CONFLUENCE]: Adjusted buy single target due to HTF resistance: ", buyTP3);
        }

        Print("DEBUG [ADVANCED_ORDER]: Buy single target: ", buyTP3, " (", finalRR, "R)");
    }

    // Calculate multiple target levels for sell
    double sellTP1 = 0, sellTP2 = 0, sellTP3 = 0;
    if (UseMultipleTargets)
    {
        sellTP1 = NormalizeDouble(sellPrice - (sellStopDistance * FirstTargetRatio), _Digits);
        sellTP2 = NormalizeDouble(sellPrice - (sellStopDistance * SecondTargetRatio), _Digits);
        sellTP3 = NormalizeDouble(sellPrice - (sellStopDistance * finalRR), _Digits);

        // Adjust final target if too close to HTF support
        if (!sellConfluentWithHTF && htfSupport > 0 && sellTP3 < htfSupport)
        {
            sellTP3 = NormalizeDouble(htfSupport + (5 * 10 * _Point), _Digits); // 5 pips above support
            Print("DEBUG [HTF_CONFLUENCE]: Adjusted sell TP3 due to HTF support: ", sellTP3);
        }

        Print("DEBUG [ADVANCED_ORDER]: Sell targets - TP1: ", sellTP1, " (", FirstTargetRatio, "R)");
        Print("DEBUG [ADVANCED_ORDER]: Sell targets - TP2: ", sellTP2, " (", SecondTargetRatio, "R)");
        Print("DEBUG [ADVANCED_ORDER]: Sell targets - TP3: ", sellTP3, " (", finalRR, "R)");
    }
    else
    {
        sellTP3 = NormalizeDouble(sellPrice - (sellStopDistance * finalRR), _Digits);

        // Adjust if too close to HTF support
        if (!sellConfluentWithHTF && htfSupport > 0 && sellTP3 < htfSupport)
        {
            sellTP3 = NormalizeDouble(htfSupport + (5 * 10 * _Point), _Digits);
            Print("DEBUG [HTF_CONFLUENCE]: Adjusted sell single target due to HTF support: ", sellTP3);
        }

        Print("DEBUG [ADVANCED_ORDER]: Sell single target: ", sellTP3, " (", finalRR, "R)");
    }

    // Determine which orders to place based on HTF trend
    bool placeBuyOrder = true;
    bool placeSellOrder = true;

    if (UseMultiTimeframe && OnlyTrendDirection && htfAnalysisValid)
    {
        if (higherTFTrend == TREND_UP)
        {
            placeSellOrder = false;
            Print("DEBUG [ADVANCED_ORDER]: HTF trend is UP - only placing BUY order");
        }
        else if (higherTFTrend == TREND_DOWN)
        {
            placeBuyOrder = false;
            Print("DEBUG [ADVANCED_ORDER]: HTF trend is DOWN - only placing SELL order");
        }
    }

    // Place Buy Stop Order if allowed
    if (placeBuyOrder)
    {
        Print("DEBUG [ADVANCED_ORDER]: Placing BUY STOP order...");
        bool buyResult = trade.BuyStop(lotSize, buyPrice, _Symbol, buySL, buyTP3, 0, 0, "FCB_Advanced_Buy");

        if (buyResult)
        {
            Print("DEBUG [ADVANCED_ORDER]: BUY STOP order placed successfully");
        }
        else
        {
            Print("DEBUG [ADVANCED_ORDER]: ERROR - BUY STOP order failed!");
            Print("DEBUG [ADVANCED_ORDER]: Error code: ", trade.ResultRetcode());
            Print("DEBUG [ADVANCED_ORDER]: Error description: ", trade.ResultRetcodeDescription());
        }
    }

    // Place Sell Stop Order if allowed
    if (placeSellOrder)
    {
        Print("DEBUG [ADVANCED_ORDER]: Placing SELL STOP order...");
        bool sellResult = trade.SellStop(lotSize, sellPrice, _Symbol, sellSL, sellTP3, 0, 0, "FCB_Advanced_Sell");

        if (sellResult)
        {
            Print("DEBUG [ADVANCED_ORDER]: SELL STOP order placed successfully");
        }
        else
        {
            Print("DEBUG [ADVANCED_ORDER]: ERROR - SELL STOP order failed!");
            Print("DEBUG [ADVANCED_ORDER]: Error code: ", trade.ResultRetcode());
            Print("DEBUG [ADVANCED_ORDER]: Error description: ", trade.ResultRetcodeDescription());
        }
    }

    Print("=== DEBUG [ADVANCED_ORDER]: Advanced pending order placement complete ===");
}

//+------------------------------------------------------------------+
//| ADVANCED TRADE MANAGEMENT FUNCTIONS                             |
//+------------------------------------------------------------------+
void ManageActiveTrades()
{
    static datetime lastManagementLog = 0;
    bool shouldLog = (TimeCurrent() - lastManagementLog > 300); // Log every 5 minutes

    if (shouldLog)
    {
        Print("=== DEBUG [ADVANCED_MGMT]: Starting advanced trade management cycle ===");
        lastManagementLog = TimeCurrent();
    }

    // --- Advanced OCO (One-Cancels-Other) Logic ---
    bool positionExists = PositionSelect(_Symbol);

    if (shouldLog)
    {
        Print("DEBUG [ADVANCED_MGMT]: Position exists: ", (positionExists ? "YES" : "NO"));
    }

    if (positionExists)
    {
        ulong positionMagic = PositionGetInteger(POSITION_MAGIC);

        if (shouldLog)
        {
            Print("DEBUG [ADVANCED_MGMT]: Position magic: ", positionMagic, " (Expected: ", MagicNumber, ")");
        }

        if(positionMagic == MagicNumber)
        {
            // Initialize position info if not already done
            if (positionInfo.openTime == 0)
            {
                positionInfo.openTime = (datetime)PositionGetInteger(POSITION_TIME);
                positionInfo.remainingLotSize = PositionGetDouble(POSITION_VOLUME);
                positionInfo.highestProfit = 0;
                positionInfo.accelerationValue = AccelerationFactor;
                Print("DEBUG [ADVANCED_MGMT]: Position info initialized - Open time: ", TimeToString(positionInfo.openTime));
            }

            if (shouldLog)
            {
                Print("DEBUG [ADVANCED_MGMT]: Our position detected - checking for pending orders to cancel");
            }

            int ordersTotal = OrdersTotal();
            int ordersCancelled = 0;

            // A position is open, cancel any remaining pending orders
            for (int i = ordersTotal - 1; i >= 0; i--)
            {
                if (OrderGetTicket(i) > 0 && OrderGetString(ORDER_SYMBOL) == _Symbol && OrderGetInteger(ORDER_MAGIC) == MagicNumber)
                {
                    ulong orderTicket = OrderGetTicket(i);
                    Print("DEBUG [ADVANCED_MGMT]: Active position detected. Deleting opposing pending order #", orderTicket);

                    bool deleteResult = trade.OrderDelete(orderTicket);
                    if (deleteResult)
                    {
                        Print("DEBUG [ADVANCED_MGMT]: Order #", orderTicket, " deleted successfully");
                        ordersCancelled++;
                    }
                    else
                    {
                        Print("DEBUG [ADVANCED_MGMT]: ERROR - Failed to delete order #", orderTicket);
                        Print("DEBUG [ADVANCED_MGMT]: Error code: ", trade.ResultRetcode());
                    }
                }
            }

            if (shouldLog)
            {
                Print("DEBUG [ADVANCED_MGMT]: Orders cancelled: ", ordersCancelled);
            }

            // Advanced position management
            ManageAdvancedPosition();
        }
    }
    else
    {
        // Reset position info when no position exists
        if (positionInfo.openTime != 0)
        {
            Print("DEBUG [ADVANCED_MGMT]: Position closed - resetting position info");
            ResetPositionInfo();
        }
    }

    if (shouldLog)
    {
        Print("=== DEBUG [ADVANCED_MGMT]: Advanced trade management cycle complete ===");
    }
}

void ManageAdvancedPosition()
{
    if (!PositionSelect(_Symbol) || PositionGetInteger(POSITION_MAGIC) != MagicNumber)
        return;

    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double currentSL = PositionGetDouble(POSITION_SL);
    double currentTP = PositionGetDouble(POSITION_TP);
    long positionType = PositionGetInteger(POSITION_TYPE);
    double positionVolume = PositionGetDouble(POSITION_VOLUME);
    double currentPrice = (positionType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    static datetime lastDetailedLog = 0;
    bool shouldLogDetails = (TimeCurrent() - lastDetailedLog > 600); // Log details every 10 minutes

    if (shouldLogDetails)
    {
        Print("--- DEBUG [ADVANCED_POS]: Advanced Position Details ---");
        Print("DEBUG [ADVANCED_POS]: Type: ", (positionType == POSITION_TYPE_BUY ? "BUY" : "SELL"));
        Print("DEBUG [ADVANCED_POS]: Volume: ", positionVolume);
        Print("DEBUG [ADVANCED_POS]: Open Price: ", openPrice);
        Print("DEBUG [ADVANCED_POS]: Current Price: ", currentPrice);
        Print("DEBUG [ADVANCED_POS]: Current SL: ", currentSL);
        Print("DEBUG [ADVANCED_POS]: Current TP: ", currentTP);
        Print("DEBUG [ADVANCED_POS]: Remaining lot size: ", positionInfo.remainingLotSize);
        lastDetailedLog = TimeCurrent();
    }

    // Calculate current profit in R multiples
    double stopDistance = (positionType == POSITION_TYPE_BUY) ? (openPrice - currentSL) : (currentSL - openPrice);
    double currentProfit = (positionType == POSITION_TYPE_BUY) ? (currentPrice - openPrice) : (openPrice - currentPrice);
    double currentRRatio = (stopDistance > 0) ? (currentProfit / stopDistance) : 0;

    // Update highest profit for acceleration trailing
    if (currentProfit > positionInfo.highestProfit)
    {
        positionInfo.highestProfit = currentProfit;
    }

    if (shouldLogDetails)
    {
        Print("DEBUG [ADVANCED_POS]: Current profit: ", NormalizeDouble(currentProfit / (_Point * 10), 1), " pips");
        Print("DEBUG [ADVANCED_POS]: Current R ratio: ", NormalizeDouble(currentRRatio, 2), "R");
        Print("DEBUG [ADVANCED_POS]: Highest profit: ", NormalizeDouble(positionInfo.highestProfit / (_Point * 10), 1), " pips");
    }

    // Time-based exit
    if (MaxTradeHours > 0)
    {
        long hoursOpen = (TimeCurrent() - positionInfo.openTime) / 3600;
        if (hoursOpen >= MaxTradeHours)
        {
            Print("DEBUG [ADVANCED_POS]: TIME EXIT TRIGGERED - Position open for ", hoursOpen, " hours");
            Print("DEBUG [ADVANCED_POS]: Closing position due to time limit");
            trade.PositionClose(_Symbol);
            return;
        }
    }

    // Multiple profit targets management
    if (UseMultipleTargets)
    {
        ManageMultipleProfitTargets(currentRRatio, positionType);
    }

    // Enhanced breakeven logic
    if (UseDynamicBreakeven && !positionInfo.movedToBreakeven)
    {
        if (currentRRatio >= BreakevenAtRatio)
        {
            Print("DEBUG [ADVANCED_POS]: DYNAMIC BREAKEVEN TRIGGERED at ", NormalizeDouble(currentRRatio, 2), "R");
            Print("DEBUG [ADVANCED_POS]: Moving SL from ", currentSL, " to open price: ", openPrice);

            bool modifyResult = trade.PositionModify(_Symbol, openPrice, currentTP);
            if (modifyResult)
            {
                Print("DEBUG [ADVANCED_POS]: Dynamic breakeven modification successful");
                positionInfo.movedToBreakeven = true;
            }
            else
            {
                Print("DEBUG [ADVANCED_POS]: ERROR - Dynamic breakeven modification failed!");
                Print("DEBUG [ADVANCED_POS]: Error code: ", trade.ResultRetcode());
            }
        }
    }

    // Advanced trailing stop management
    if (positionInfo.movedToBreakeven)
    {
        ManageAdvancedTrailingStop(positionType, currentPrice, openPrice, currentSL, currentTP, currentProfit);
    }
}

void ManageMultipleProfitTargets(double currentRRatio, long positionType)
{
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    // First target
    if (!positionInfo.firstTargetHit && currentRRatio >= FirstTargetRatio)
    {
        double closeVolume = NormalizeDouble((positionInfo.originalLotSize * FirstTargetPercent / 100.0), 2);
        closeVolume = MathMax(closeVolume, minLot);
        closeVolume = floor(closeVolume / lotStep) * lotStep;

        if (closeVolume <= positionInfo.remainingLotSize)
        {
            Print("DEBUG [MULTI_TARGET]: FIRST TARGET HIT at ", NormalizeDouble(currentRRatio, 2), "R");
            Print("DEBUG [MULTI_TARGET]: Closing ", closeVolume, " lots (", FirstTargetPercent, "%)");

            bool closeResult = trade.PositionClosePartial(_Symbol, closeVolume);
            if (closeResult)
            {
                positionInfo.firstTargetHit = true;
                positionInfo.remainingLotSize -= closeVolume;
                Print("DEBUG [MULTI_TARGET]: First target closure successful. Remaining: ", positionInfo.remainingLotSize);
            }
            else
            {
                Print("DEBUG [MULTI_TARGET]: ERROR - First target closure failed!");
                Print("DEBUG [MULTI_TARGET]: Error code: ", trade.ResultRetcode());
            }
        }
    }

    // Second target
    if (!positionInfo.secondTargetHit && positionInfo.firstTargetHit && currentRRatio >= SecondTargetRatio)
    {
        double closeVolume = NormalizeDouble((positionInfo.originalLotSize * SecondTargetPercent / 100.0), 2);
        closeVolume = MathMax(closeVolume, minLot);
        closeVolume = floor(closeVolume / lotStep) * lotStep;

        if (closeVolume <= positionInfo.remainingLotSize)
        {
            Print("DEBUG [MULTI_TARGET]: SECOND TARGET HIT at ", NormalizeDouble(currentRRatio, 2), "R");
            Print("DEBUG [MULTI_TARGET]: Closing ", closeVolume, " lots (", SecondTargetPercent, "%)");

            bool closeResult = trade.PositionClosePartial(_Symbol, closeVolume);
            if (closeResult)
            {
                positionInfo.secondTargetHit = true;
                positionInfo.remainingLotSize -= closeVolume;
                Print("DEBUG [MULTI_TARGET]: Second target closure successful. Remaining: ", positionInfo.remainingLotSize);
            }
            else
            {
                Print("DEBUG [MULTI_TARGET]: ERROR - Second target closure failed!");
                Print("DEBUG [MULTI_TARGET]: Error code: ", trade.ResultRetcode());
            }
        }
    }
}

void ManageAdvancedTrailingStop(long positionType, double currentPrice, double openPrice, double currentSL, double currentTP, double currentProfit)
{
    double newSL = 0;
    bool shouldModify = false;

    static datetime lastTrailingLog = 0;
    bool shouldLog = (TimeCurrent() - lastTrailingLog > 300); // Log every 5 minutes

    string trailingTypeStr = "";

    switch(TrailingType)
    {
        case TRAILING_FIXED:
            trailingTypeStr = "FIXED";
            newSL = CalculateFixedTrailing(positionType, currentPrice);
            break;

        case TRAILING_ATR:
            trailingTypeStr = "ATR";
            newSL = CalculateATRTrailing(positionType, currentPrice);
            break;

        case TRAILING_STEP:
            trailingTypeStr = "STEP";
            newSL = CalculateStepTrailing(positionType, currentPrice, currentSL);
            break;

        case TRAILING_ACCELERATION:
            trailingTypeStr = "ACCELERATION";
            newSL = CalculateAccelerationTrailing(positionType, currentPrice, currentProfit);
            break;
    }

    if (positionType == POSITION_TYPE_BUY)
    {
        shouldModify = (newSL > currentSL) && (newSL > openPrice);
    }
    else // SELL
    {
        shouldModify = (newSL < currentSL) && (newSL < openPrice);
    }

    if (shouldLog)
    {
        Print("DEBUG [ADVANCED_TRAIL]: ", trailingTypeStr, " Trailing - New SL: ", newSL, ", Current SL: ", currentSL);
        Print("DEBUG [ADVANCED_TRAIL]: Should modify: ", shouldModify);
        lastTrailingLog = TimeCurrent();
    }

    if (shouldModify)
    {
        Print("DEBUG [ADVANCED_TRAIL]: ", trailingTypeStr, " TRAILING STOP TRIGGERED for ", (positionType == POSITION_TYPE_BUY ? "BUY" : "SELL"));
        Print("DEBUG [ADVANCED_TRAIL]: Moving SL from ", currentSL, " to ", newSL);

        bool modifyResult = trade.PositionModify(_Symbol, newSL, currentTP);
        if (modifyResult)
        {
            Print("DEBUG [ADVANCED_TRAIL]: Advanced trailing stop modification successful");
        }
        else
        {
            Print("DEBUG [ADVANCED_TRAIL]: ERROR - Advanced trailing stop modification failed!");
            Print("DEBUG [ADVANCED_TRAIL]: Error code: ", trade.ResultRetcode());
        }
    }
}

double CalculateFixedTrailing(long positionType, double currentPrice)
{
    if (positionType == POSITION_TYPE_BUY)
    {
        return NormalizeDouble(currentPrice - (TrailingStopPips * 10 * _Point), _Digits);
    }
    else
    {
        return NormalizeDouble(currentPrice + (TrailingStopPips * 10 * _Point), _Digits);
    }
}

double CalculateATRTrailing(long positionType, double currentPrice)
{
    double atrDistance = currentATR * ATRTrailingMulti;

    if (positionType == POSITION_TYPE_BUY)
    {
        return NormalizeDouble(currentPrice - atrDistance, _Digits);
    }
    else
    {
        return NormalizeDouble(currentPrice + atrDistance, _Digits);
    }
}

double CalculateStepTrailing(long positionType, double currentPrice, double currentSL)
{
    double stepDistance = StepTrailingPips * 10 * _Point;

    if (positionType == POSITION_TYPE_BUY)
    {
        double potentialSL = currentPrice - (TrailingStopPips * 10 * _Point);
        // Only move if we can move by at least one step
        if (potentialSL >= currentSL + stepDistance)
        {
            return NormalizeDouble(potentialSL, _Digits);
        }
        return currentSL;
    }
    else
    {
        double potentialSL = currentPrice + (TrailingStopPips * 10 * _Point);
        // Only move if we can move by at least one step
        if (potentialSL <= currentSL - stepDistance)
        {
            return NormalizeDouble(potentialSL, _Digits);
        }
        return currentSL;
    }
}

double CalculateAccelerationTrailing(long positionType, double currentPrice, double currentProfit)
{
    // Increase acceleration factor as profit increases
    if (currentProfit > positionInfo.highestProfit * 0.9) // If we're within 90% of highest profit
    {
        positionInfo.accelerationValue = MathMin(positionInfo.accelerationValue + AccelerationFactor, 0.2); // Max 20%
    }

    double acceleratedDistance = (TrailingStopPips * 10 * _Point) * (1.0 - positionInfo.accelerationValue);

    if (positionType == POSITION_TYPE_BUY)
    {
        return NormalizeDouble(currentPrice - acceleratedDistance, _Digits);
    }
    else
    {
        return NormalizeDouble(currentPrice + acceleratedDistance, _Digits);
    }
}
//+------------------------------------------------------------------+
