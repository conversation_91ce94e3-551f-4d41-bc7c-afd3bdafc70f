# Array Out of Range Bug Fix - ultra.mq5

## 🚨 **BUG IDENTIFIED AND FIXED**

### **Error Location:**
- **File:** ultra.mq5
- **Line:** 354 (and 335)
- **Error:** "array out of range"
- **Function:** `FindLastTrough` and `FindLastPeak`

### **Root Cause:**
Both functions had the same critical bug:

```mql5
// BUGGY CODE:
for(int i = start; i < end - 1; i++)
{
    if(data[i] > data[i-1] && data[i] > data[i+1]) // ❌ BUG HERE
    //           ^^^^^^^^^ 
    // When i = start, this accesses data[start-1] which can be out of bounds!
}
```

### **The Problem:**
1. Loop starts at `start` index
2. Immediately tries to access `data[i-1]` 
3. When `i = start`, it accesses `data[start-1]`
4. If `start = 0`, then `data[-1]` = **ARRAY OUT OF RANGE ERROR**

### **The Fix:**
Added proper bounds checking and ensured safe array access:

```mql5
// FIXED CODE:
// Ensure we have enough data points and valid bounds
if(start < 1 || end <= start + 2) return -1;

for(int i = MathMax(start, 1); i < end - 1; i++)
//            ^^^^^^^^^^^^^^^^^^
// Now ensures i is always >= 1, so data[i-1] is safe
{
    if(data[i] > data[i-1] && data[i] > data[i+1]) // ✅ SAFE NOW
}
```

### **Changes Made:**

**1. Added Input Validation:**
- Check if `start < 1` (need at least index 1 to access `data[i-1]`)
- Check if `end <= start + 2` (need at least 3 points for peak/trough detection)
- Return `-1` if insufficient data

**2. Fixed Loop Bounds:**
- Changed `i = start` to `i = MathMax(start, 1)`
- Ensures `i` is always at least 1
- Guarantees `data[i-1]` is never out of bounds

**3. Applied to Both Functions:**
- `FindLastPeak()` - fixed line 335
- `FindLastTrough()` - fixed line 354

### **Impact:**
- **Before:** Random crashes during divergence strategy execution
- **After:** Safe execution with proper bounds checking
- **Risk:** No trading logic changes, only safety improvements

### **Testing:**
The EA should now run backtests without array errors. The divergence strategy will:
- Safely handle edge cases with insufficient data
- Return `-1` when peak/trough detection isn't possible
- Continue normal operation in all other cases

### **Error Log Context:**
Your error occurred here:
```
2024.06.03 07:30:00   GetTradingSignal: Market is RANGING/WEAK. Deploying Divergence strategy (EMA filter OFF)...
2024.06.03 07:30:00   array out of range in 'Uktra 12.mq5' (354,28)
```

This was during divergence detection when the algorithm tried to find peaks/troughs in the MACD data. The fix ensures this won't happen again.

**✅ Ultra.mq5 is now safe to run backtests!**