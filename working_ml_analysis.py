#!/usr/bin/env python3
"""
Working ML Analysis - Pre-compute ML predictions to avoid backtrader issues
"""

import numpy as np
import pandas as pd
import backtrader as bt
from datetime import datetime
import json
import os

# Import from the original files
from ml_enhanced_mt5_analysis import find_mt5_csv_files, convert_mt5_csv
from fixed_improved_ml_analysis import RobustFeatureEngine, ImbalancedMLPredictor

try:
    from sklearn.ensemble import RandomForestClassifier
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

class PrecomputedMLStrategy(bt.Strategy):
    """Strategy with pre-computed ML predictions"""
    
    params = (
        ('ml_confidence_threshold', 0.65),
        ('risk_reward_ratio', 2.0),
        ('session_start_hour', 8),
        ('max_trade_hours', 8),
        ('min_range_pips', 8.0),
    )
    
    def __init__(self):
        self.ml_predictions = {}  # Will be set externally
        self.session_high = None
        self.session_low = None
        self.session_start = None
        self.trade_start_time = None
        self.trades_log = []
        self.prediction_count = 0
        
    def set_ml_predictions(self, predictions_dict):
        """Set pre-computed ML predictions"""
        self.ml_predictions = predictions_dict
        
    def get_ml_prediction(self):
        """Get pre-computed ML prediction for current datetime"""
        current_dt = self.data.datetime.datetime()
        
        # Find closest prediction
        closest_pred = 0.5  # Default
        min_diff = float('inf')
        
        for pred_dt, pred_value in self.ml_predictions.items():
            diff = abs((current_dt - pred_dt).total_seconds())
            if diff < min_diff:
                min_diff = diff
                closest_pred = pred_value
        
        self.prediction_count += 1
        return closest_pred
    
    def next(self):
        current_time = self.data.datetime.time()
        current_hour = current_time.hour
        
        # Session detection
        if current_hour == self.params.session_start_hour and current_time.minute == 0:
            self.session_start = len(self.data)
            self.session_high = self.data.high[0]
            self.session_low = self.data.low[0]
            
        elif (self.session_start and 
              len(self.data) - self.session_start <= 2 and
              current_hour == self.params.session_start_hour):
            self.session_high = max(self.session_high, self.data.high[0])
            self.session_low = min(self.session_low, self.data.low[0])
            
        # Trade decision
        elif (self.session_start and 
              len(self.data) - self.session_start == 3 and
              not self.position):
            
            range_pips = (self.session_high - self.session_low) * 10000
            
            if range_pips > self.params.min_range_pips:
                # Get pre-computed ML prediction
                ml_confidence = self.get_ml_prediction()
                
                # Only trade if ML confidence is high enough
                if ml_confidence >= self.params.ml_confidence_threshold:
                    self.execute_trade(ml_confidence)
                else:
                    self.trades_log.append({
                        'action': 'skipped',
                        'ml_confidence': ml_confidence,
                        'range_pips': range_pips
                    })
        
        # Position management
        if self.position:
            if hasattr(self, 'trade_start_time') and self.trade_start_time:
                hours_in_trade = (self.data.datetime.datetime() - self.trade_start_time).total_seconds() / 3600
                if hours_in_trade >= self.params.max_trade_hours:
                    self.close()
                    return
            
            # Check stop/target
            if self.position.size > 0:  # Long
                if (self.data.low[0] <= self.stop_price or 
                    self.data.high[0] >= self.target_price):
                    self.close()
            else:  # Short
                if (self.data.high[0] >= self.stop_price or 
                    self.data.low[0] <= self.target_price):
                    self.close()
    
    def execute_trade(self, ml_confidence):
        """Execute trade with ML confidence logging"""
        buy_price = self.session_high + 0.0001
        sell_price = self.session_low - 0.0001
        
        if self.data.close[0] > buy_price:
            self.buy(size=1000)
            self.stop_price = self.session_low - 0.0001
            self.target_price = buy_price + (buy_price - self.stop_price) * self.params.risk_reward_ratio
            self.trade_start_time = self.data.datetime.datetime()
            
            self.trades_log.append({
                'action': 'buy',
                'entry_price': buy_price,
                'ml_confidence': ml_confidence,
                'range_pips': (self.session_high - self.session_low) * 10000
            })
            
        elif self.data.close[0] < sell_price:
            self.sell(size=1000)
            self.stop_price = self.session_high + 0.0001
            self.target_price = sell_price - (self.stop_price - sell_price) * self.params.risk_reward_ratio
            self.trade_start_time = self.data.datetime.datetime()
            
            self.trades_log.append({
                'action': 'sell',
                'entry_price': sell_price,
                'ml_confidence': ml_confidence,
                'range_pips': (self.session_high - self.session_low) * 10000
            })

def precompute_ml_predictions(data, ml_predictor, feature_engine):
    """Pre-compute ML predictions for all data points"""
    
    print("🧠 Pre-computing ML predictions...")
    
    predictions = {}
    window_size = 100
    
    for i in range(window_size, len(data)):
        try:
            # Get window of data for feature extraction
            window_data = data.iloc[i-window_size:i+1].copy()
            
            # Extract features
            features = feature_engine.extract_features(window_data)
            
            if len(features) > 0:
                current_features = features.iloc[-1]
                prediction = ml_predictor.predict_probability(current_features)
                predictions[data.index[i]] = prediction
            
        except Exception as e:
            # Use default prediction on error
            predictions[data.index[i]] = 0.5
    
    print(f"✅ Pre-computed {len(predictions)} ML predictions")
    return predictions

def run_precomputed_backtest(data, ml_predictions, params):
    """Run backtest with pre-computed ML predictions"""
    
    cerebro = bt.Cerebro()
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)
    
    # Add strategy
    cerebro.addstrategy(PrecomputedMLStrategy, **params)
    
    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.0002)
    
    # Add analyzers
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    
    # Run backtest
    results = cerebro.run()
    strategy = results[0]
    
    # Set ML predictions
    strategy.set_ml_predictions(ml_predictions)
    
    # Run again with predictions set
    cerebro = bt.Cerebro()
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)
    
    cerebro.addstrategy(PrecomputedMLStrategy, **params)
    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.0002)
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    
    results = cerebro.run()
    strategy = results[0]
    
    # Manually set predictions (workaround for backtrader)
    strategy.ml_predictions = ml_predictions
    
    final_value = cerebro.broker.getvalue()
    
    # Extract results
    trade_analyzer = strategy.analyzers.trades.get_analysis()
    sharpe_analyzer = strategy.analyzers.sharpe.get_analysis()
    drawdown_analyzer = strategy.analyzers.drawdown.get_analysis()
    
    # Calculate metrics
    total_return = (final_value - 10000) / 10000 * 100
    total_trades = trade_analyzer.get('total', {}).get('total', 0)
    winning_trades = trade_analyzer.get('won', {}).get('total', 0)
    losing_trades = trade_analyzer.get('lost', {}).get('total', 0)
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    max_drawdown = drawdown_analyzer.get('max', {}).get('drawdown', 0)
    sharpe_ratio = sharpe_analyzer.get('sharperatio', 0) or 0
    
    # ML metrics
    prediction_values = list(ml_predictions.values())
    avg_confidence = np.mean(prediction_values) if prediction_values else 0.5
    
    return {
        'total_return': total_return,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'final_value': final_value,
        'parameters': params,
        'avg_ml_confidence': avg_confidence,
        'ml_predictions_count': len(ml_predictions),
        'trades_log': getattr(strategy, 'trades_log', [])
    }

def main():
    """Main function with working ML approach"""
    
    print("🚀 Working ML Analysis")
    print("=" * 40)
    print("Pre-computed ML predictions approach")
    print()
    
    if not ML_AVAILABLE:
        print("❌ ML libraries not available")
        return False
    
    # Load data
    cleaned_files = [f for f in os.listdir('.') if 'cleaned.csv' in f]
    
    if not cleaned_files:
        print("❌ No cleaned CSV files found")
        return False
    
    print(f"📁 Using: {cleaned_files[0]}")
    data = pd.read_csv(cleaned_files[0], index_col=0, parse_dates=True)
    
    print(f"✅ Data loaded: {len(data)} bars")
    
    # Train ML model
    print("🧠 Training ML model...")
    
    feature_engine = RobustFeatureEngine()
    features = feature_engine.extract_features(data)
    
    # Create labels based on actual breakout success
    print("🏷️ Creating breakout labels...")
    labels = []
    
    for i in range(len(features)):
        if i < len(features) - 20:  # Need future data
            # Look for breakout pattern
            current_high = data['High'].iloc[i]
            current_low = data['Low'].iloc[i]
            
            # Check if price breaks out in next few bars
            future_data = data.iloc[i+1:i+11]  # Next 10 bars (5 hours)
            
            if len(future_data) > 0:
                max_future_high = future_data['High'].max()
                min_future_low = future_data['Low'].min()
                
                # Successful breakout if price moves significantly
                upward_breakout = (max_future_high - current_high) / current_high > 0.002  # 0.2%
                downward_breakout = (current_low - min_future_low) / current_low > 0.002
                
                labels.append(1 if (upward_breakout or downward_breakout) else 0)
            else:
                labels.append(0)
        else:
            labels.append(0)
    
    labels = pd.Series(labels, index=features.index)
    
    print(f"Labels: {labels.value_counts().to_dict()}")
    
    # Train ML predictor
    ml_predictor = ImbalancedMLPredictor()
    success = ml_predictor.train(features, labels)
    
    if not success:
        print("❌ ML training failed")
        return False
    
    # Pre-compute ML predictions
    ml_predictions = precompute_ml_predictions(data, ml_predictor, feature_engine)
    
    # Test different parameters
    param_sets = [
        {'ml_confidence_threshold': 0.6, 'risk_reward_ratio': 2.0, 'min_range_pips': 8.0},
        {'ml_confidence_threshold': 0.65, 'risk_reward_ratio': 2.5, 'min_range_pips': 10.0},
        {'ml_confidence_threshold': 0.7, 'risk_reward_ratio': 3.0, 'min_range_pips': 12.0}
    ]
    
    best_result = None
    best_params = None
    
    for i, params in enumerate(param_sets):
        print(f"\\n📊 Testing set {i+1}/3...")
        print(f"   ML Threshold: {params['ml_confidence_threshold']}")
        print(f"   Risk:Reward: {params['risk_reward_ratio']}")
        print(f"   Min Range: {params['min_range_pips']} pips")
        
        try:
            result = run_precomputed_backtest(data, ml_predictions, params)
            
            print(f"   Return: {result['total_return']:.2f}%")
            print(f"   Trades: {result['total_trades']}")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Sharpe: {result['sharpe_ratio']:.2f}")
            print(f"   Max DD: {result['max_drawdown']:.2f}%")
            
            if (result['total_return'] > 0 and 
                result['total_trades'] >= 5 and
                (best_result is None or result['sharpe_ratio'] > best_result['sharpe_ratio'])):
                best_result = result
                best_params = params
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            continue
    
    if best_result is None:
        print("\\n❌ No profitable results found")
        # Show the best available result anyway
        if len(param_sets) > 0:
            result = run_precomputed_backtest(data, ml_predictions, param_sets[0])
            best_result = result
            best_params = param_sets[0]
            print("\\n📊 Showing best available result:")
    
    # Display results
    print("\\n🏆 RESULTS:")
    print("=" * 30)
    print(f"Total Return: {best_result['total_return']:.2f}%")
    print(f"Win Rate: {best_result['win_rate']:.1f}%")
    print(f"Total Trades: {best_result['total_trades']}")
    print(f"Sharpe Ratio: {best_result['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {best_result['max_drawdown']:.2f}%")
    print(f"Avg ML Confidence: {best_result['avg_ml_confidence']:.3f}")
    
    # Analyze trades
    trades_log = best_result['trades_log']
    if trades_log:
        taken_trades = [t for t in trades_log if t['action'] in ['buy', 'sell']]
        skipped_trades = [t for t in trades_log if t['action'] == 'skipped']
        
        print(f"\\n📈 Trade Analysis:")
        print(f"Trades Taken: {len(taken_trades)}")
        print(f"Trades Skipped: {len(skipped_trades)}")
        
        if taken_trades:
            avg_conf_taken = np.mean([t['ml_confidence'] for t in taken_trades])
            print(f"Avg Confidence (Taken): {avg_conf_taken:.3f}")
        
        if skipped_trades:
            avg_conf_skipped = np.mean([t['ml_confidence'] for t in skipped_trades])
            print(f"Avg Confidence (Skipped): {avg_conf_skipped:.3f}")
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    result_file = f'backtesting/results/working_ml_{timestamp}.json'
    
    os.makedirs('backtesting/results', exist_ok=True)
    
    with open(result_file, 'w') as f:
        json.dump({
            'strategy': 'Working_ML_Enhanced',
            'results': best_result,
            'best_parameters': best_params,
            'timestamp': timestamp
        }, f, indent=2, default=str)
    
    print(f"\\n💾 Results saved to: {result_file}")
    
    # Interpretation
    if best_result['total_return'] > 5:
        print("\\n✅ Good results! Strategy shows promise.")
    elif best_result['total_return'] > 0:
        print("\\n⚠️ Modest positive returns. Room for improvement.")
    else:
        print("\\n❌ Negative returns. Strategy needs work.")
    
    return True

if __name__ == "__main__":
    main()
