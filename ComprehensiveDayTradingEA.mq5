//+------------------------------------------------------------------+
//|                                   ComprehensiveDayTradingEA.mq5 |
//|                      Professional Day Trading EA for Small Accounts |
//|                    Combining Volatility, Volume & Risk Management |
//+------------------------------------------------------------------+
#property copyright "Professional Day Trading EA"
#property version   "1.00"
#property strict

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//+------------------------------------------------------------------+
//|                    STRATEGY SETTINGS                             |
//+------------------------------------------------------------------+
input group "=== VOLATILITY & VOLUME STRATEGY ==="
input int      VWMA_Period = 21;              // Volume Weighted MA Period
input int      RSI_Period = 14;               // RSI Period for momentum
input int      MFI_Period = 14;               // Money Flow Index Period
input int      VROC_Period = 10;              // Volume Rate of Change Period
input double   VROC_Threshold = 5.0;          // VROC threshold % (reduced for demo)
input bool     UseVolumeFilter = true;        // Enable/disable volume filter
input double   MinVolumeThreshold = 1.0;      // Minimum volume threshold
input int      ATR_Period = 14;               // ATR Period
input double   VolatilityFilter = 1.2;        // Min volatility vs average

//+------------------------------------------------------------------+
//|                    RISK MANAGEMENT                               |
//+------------------------------------------------------------------+
input group "=== ADVANCED RISK MANAGEMENT ==="
input double   RiskPercentPerTrade = 1.5;     // Risk per trade %
input double   MaxAccountRisk = 3.0;          // Max total account risk %
input double   DailyLossLimit = 4.0;          // Daily loss limit %
input double   WeeklyLossLimit = 8.0;         // Weekly loss limit %
input double   MaxDrawdownPercent = 12.0;     // Max drawdown %
input int      MaxConsecutiveLosses = 3;      // Max consecutive losses
input double   EquityProtectionLevel = 95.0;  // Stop below % of peak equity
input bool     UseAdaptiveRisk = true;        // Adaptive risk scaling
input double   MaxPositionSize = 5.0;         // Max position size % of account

//+------------------------------------------------------------------+
//|                    POSITION MANAGEMENT                           |
//+------------------------------------------------------------------+
input group "=== POSITION MANAGEMENT ==="
input int      MaxConcurrentPositions = 3;    // Max concurrent positions
input double   MinRiskReward = 1.8;           // Minimum R:R ratio
input double   BaseRiskReward = 2.5;          // Base R:R target
input double   MaxRiskReward = 4.0;           // Maximum R:R target
input bool     UseDynamicTP = true;           // Use dynamic take profit
input bool     UsePartialClose = true;        // Enable partial profit taking
input double   PartialClose1_RR = 1.2;        // First partial at R:R
input double   PartialClose1_Percent = 30.0;  // First partial %
input double   PartialClose2_RR = 2.0;        // Second partial at R:R
input double   PartialClose2_Percent = 40.0;  // Second partial %

//+------------------------------------------------------------------+
//|                    BREAKEVEN & TRAILING                          |
//+------------------------------------------------------------------+
input group "=== BREAKEVEN & TRAILING ==="
input bool     UseBreakeven = true;           // Enable breakeven
input double   BreakevenRR = 0.8;             // R:R to move to breakeven
input double   BreakevenBuffer = 0.1;         // Breakeven buffer R:R
input bool     UseTrailingStop = true;        // Enable trailing stop
input double   TrailStartRR = 1.5;            // R:R to start trailing
input double   TrailATRMultiplier = 1.8;      // ATR multiplier for trailing
input bool     UseSwingTrailing = true;       // Use swing-based trailing
input int      SwingLookback = 8;             // Swing lookback periods

//+------------------------------------------------------------------+
//|                    SESSION & TIME FILTERS                        |
//+------------------------------------------------------------------+
input group "=== SESSION & TIME FILTERS ==="
input bool     TradeAsianSession = false;     // Trade Asian session
input bool     TradeLondonSession = true;     // Trade London session
input bool     TradeNYSession = true;         // Trade NY session
input bool     TradeOverlapOnly = true;       // Trade overlap only
input bool     AvoidNews = true;              // Avoid news times
input int      NewsBufferMinutes = 30;        // News buffer minutes
input int      MaxTradeHours = 8;             // Max hours per trade
input int      CooldownMinutes = 30;          // Cooldown between trades

//+------------------------------------------------------------------+
//|                    PAIR OPTIMIZATION                             |
//+------------------------------------------------------------------+
input group "=== PAIR-SPECIFIC SETTINGS ==="
input bool     OptimizeForPair = true;        // Enable pair optimization
input double   EURUSDMultiplier = 1.0;        // EURUSD adjustment
input double   GBPUSDMultiplier = 0.8;        // GBPUSD adjustment
input double   USDJPYMultiplier = 1.1;        // USDJPY adjustment
input double   AUDUSDMultiplier = 0.9;        // AUDUSD adjustment

//+------------------------------------------------------------------+
//|                    DASHBOARD & NOTIFICATIONS                     |
//+------------------------------------------------------------------+
input group "=== MONITORING & ALERTS ==="
input bool     ShowDashboard = true;          // Show dashboard
input bool     EnableAlerts = true;           // Enable alerts
input bool     EnablePushNotifications = false; // Push notifications
input bool     DetailedLogging = true;        // Detailed logging
input int      DashboardCorner = 0;           // Dashboard corner
input int      DashboardX = 10;               // Dashboard X offset
input int      DashboardY = 20;               // Dashboard Y offset

//+------------------------------------------------------------------+
//|                    GENERAL SETTINGS                              |
//+------------------------------------------------------------------+
input group "=== GENERAL SETTINGS ==="
input ulong    MagicNumber = 777888;          // Magic number
input string   TradeComment = "CompDayTrader"; // Trade comment

//+------------------------------------------------------------------+
//|                    GLOBAL VARIABLES                              |
//+------------------------------------------------------------------+
CTrade trade;
CPositionInfo posInfo;

// Indicator handles
int h_RSI, h_MFI, h_Volume, h_ATR;

// Risk management variables
double dailyStartBalance, weeklyStartBalance, peakEquity;
bool dailyLossLimitHit = false, weeklyLossLimitHit = false;
bool drawdownLimitHit = false, equityProtectionHit = false;
int consecutiveLosses = 0;
datetime lastTradeTime = 0;
datetime lastDayCheck = 0, lastWeekCheck = 0;

// Market analysis variables
double currentVWMA_RSI = 50.0;
double currentMFI = 50.0;
double currentVROC = 0.0;
double currentATR = 0.0;
double averageATR = 0.0;
double volatilityRatio = 1.0;

// Position tracking
struct ManagedPosition {
    ulong ticket;
    datetime openTime;
    double entryPrice;
    double originalSL;
    double riskAmount;
    bool breakevenApplied;
    bool trailingActive;
    bool partial1Closed;
    bool partial2Closed;
    double highestProfit;
    ENUM_POSITION_TYPE type;
};
ManagedPosition managedPositions[];

// Dashboard colors
color BullishColor = clrLimeGreen;
color BearishColor = clrRed;
color NeutralColor = clrGray;
color TextColor = clrWhite;
color BackgroundColor = clrBlack;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    // Set trade parameters
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(3);
    trade.SetTypeFillingBySymbol(_Symbol);
    
    // Initialize indicators
    h_RSI = iRSI(_Symbol, PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);
    h_MFI = iMFI(_Symbol, PERIOD_CURRENT, MFI_Period, VOLUME_TICK);
    h_Volume = iVolumes(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
    h_ATR = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
    
    if(h_RSI == INVALID_HANDLE || h_MFI == INVALID_HANDLE || 
       h_Volume == INVALID_HANDLE || h_ATR == INVALID_HANDLE) {
        Print("Error creating indicator handles");
        return INIT_FAILED;
    }
    
    // Initialize risk management
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    weeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    peakEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    // Initialize dashboard
    if(ShowDashboard) CreateDashboard();
    
    Print("=== Comprehensive Day Trading EA Initialized ===");
    Print("Symbol: ", _Symbol);
    Print("Initial Balance: ", dailyStartBalance);
    Print("Risk per trade: ", RiskPercentPerTrade, "%");
    Print("VROC Threshold: ", VROC_Threshold, "% (reduced for demo accounts)");
    Print("Volume Filter: ", UseVolumeFilter ? "ENABLED" : "DISABLED");
    Print("Min Volume Threshold: ", MinVolumeThreshold);
    Print("Detailed Logging: ", DetailedLogging ? "ENABLED" : "DISABLED");
    
    // Test volume data availability
    long testVolume[];
    if(CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, 1, testVolume) > 0) {
        Print("Volume data available - Current volume: ", testVolume[0]);
    } else {
        Print("WARNING: Volume data not available for ", _Symbol);
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Release indicators
    if(h_RSI != INVALID_HANDLE) IndicatorRelease(h_RSI);
    if(h_MFI != INVALID_HANDLE) IndicatorRelease(h_MFI);
    if(h_Volume != INVALID_HANDLE) IndicatorRelease(h_Volume);
    if(h_ATR != INVALID_HANDLE) IndicatorRelease(h_ATR);
    
    if(ShowDashboard) DeleteDashboard();
    
    Print("Comprehensive Day Trading EA Deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Update dashboard
    if(ShowDashboard) UpdateDashboard();
    
    // Manage existing positions
    ManagePositions();
    
    // Check for new bar
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentBarTime > lastBarTime) {
        lastBarTime = currentBarTime;
        
        // Update time-based tracking
        UpdateTimeFrameTracking();
        
        // Check risk limits
        if(!CheckRiskLimits()) return;
        
        // Check trading session
        if(!IsValidTradingSession()) return;
        
        // Update market analysis
        if(!UpdateMarketAnalysis()) return;
        
        // Check for trading signals
        CheckTradingSignals();
        
        // Time-based position management
        CheckTimeBasedManagement();
    }
}

//+------------------------------------------------------------------+
//| Update market analysis                                           |
//+------------------------------------------------------------------+
bool UpdateMarketAnalysis() {
    // Get RSI values
    double rsiBuffer[];
    ArraySetAsSeries(rsiBuffer, true);
    if(CopyBuffer(h_RSI, 0, 1, 1, rsiBuffer) <= 0) return false;
    
    // Get MFI values
    double mfiBuffer[];
    ArraySetAsSeries(mfiBuffer, true);
    if(CopyBuffer(h_MFI, 0, 1, 1, mfiBuffer) <= 0) return false;
    
    // Get ATR values
    double atrBuffer[];
    ArraySetAsSeries(atrBuffer, true);
    if(CopyBuffer(h_ATR, 0, 1, ATR_Period * 3, atrBuffer) <= 0) return false;
    
    // Calculate VWMA-RSI fusion
    currentVWMA_RSI = CalculateVWMA_RSI(1);
    currentMFI = mfiBuffer[0];
    currentVROC = CalculateVROC(1);
    currentATR = atrBuffer[0];
    
    // Calculate average ATR manually
    double atrSum = 0.0;
    int atrCount = MathMin(ATR_Period * 2, ArraySize(atrBuffer)); // Use 2x ATR period for average
    for(int i = 0; i < atrCount; i++) {
        atrSum += atrBuffer[i];
    }
    averageATR = (atrCount > 0) ? atrSum / atrCount : currentATR;
    volatilityRatio = (averageATR > 0) ? currentATR / averageATR : 1.0;
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate Volume Weighted Moving Average with RSI fusion        |
//+------------------------------------------------------------------+
double CalculateVWMA_RSI(int shift) {
    if(Bars(_Symbol, PERIOD_CURRENT) < shift + VWMA_Period + 10) return 50.0;
    
    double vwma = 0.0;
    double volumeSum = 0.0;
    
    // Calculate VWMA with enhanced validation
    for(int i = 0; i < VWMA_Period; i++) {
        double price = iClose(_Symbol, PERIOD_CURRENT, shift + i);
        long volume[];
        if(CopyTickVolume(_Symbol, PERIOD_CURRENT, shift + i, 1, volume) > 0 && volume[0] >= MinVolumeThreshold) {
            vwma += price * volume[0];
            volumeSum += volume[0];
        } else {
            // Use simple price average for periods with insufficient volume
            vwma += price;
            volumeSum += 1.0;
        }
    }
    
    if(volumeSum > 0) vwma = vwma / volumeSum;
    else vwma = iClose(_Symbol, PERIOD_CURRENT, shift);
    
    // Get RSI
    double rsiBuffer[];
    ArraySetAsSeries(rsiBuffer, true);
    double rsi = 50.0;
    if(CopyBuffer(h_RSI, 0, shift, 1, rsiBuffer) > 0) rsi = rsiBuffer[0];
    
    // Calculate fusion
    double currentPrice = iClose(_Symbol, PERIOD_CURRENT, shift);
    double vwmaPosition = 0.0;
    
    if(vwma != 0) vwmaPosition = ((currentPrice - vwma) / vwma) * 1000 + 50;
    
    double fusion = (vwmaPosition * 0.7) + (rsi * 0.3);
    return MathMax(0, MathMin(100, fusion));
}

//+------------------------------------------------------------------+
//| Calculate Volume Rate of Change                                  |
//+------------------------------------------------------------------+
double CalculateVROC(int shift) {
    if(Bars(_Symbol, PERIOD_CURRENT) < shift + VROC_Period + 5) return 0.0;
    
    long currentVol[], previousVol[];
    if(CopyTickVolume(_Symbol, PERIOD_CURRENT, shift, 1, currentVol) <= 0) return 0.0;
    if(CopyTickVolume(_Symbol, PERIOD_CURRENT, shift + VROC_Period, 1, previousVol) <= 0) return 0.0;
    
    // Enhanced validation for small volumes
    if(previousVol[0] == 0 || currentVol[0] < MinVolumeThreshold || previousVol[0] < MinVolumeThreshold) {
        if(DetailedLogging) Print("VROC: Insufficient volume - Current: ", currentVol[0], ", Previous: ", previousVol[0]);
        return 0.0;
    }
    
    double vroc = ((double)(currentVol[0] - previousVol[0]) / previousVol[0]) * 100.0;
    
    if(DetailedLogging && MathAbs(vroc) > VROC_Threshold) {
        Print("VROC Signal: ", vroc, "% (Current: ", currentVol[0], ", Previous: ", previousVol[0], ")");
    } else if(DetailedLogging) {
        Print("VROC below threshold: ", vroc, "% (abs: ", MathAbs(vroc), "%, threshold: ", VROC_Threshold, "%)");
    }
    
    return vroc;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckTradingSignals() {
    // Check position limits
    if(GetCurrentPositions() >= MaxConcurrentPositions) return;
    
    // Check volatility filter
    if(volatilityRatio < VolatilityFilter) return;
    
    // Check cooldown
    if(lastTradeTime > 0 && (TimeCurrent() - lastTradeTime) < (CooldownMinutes * 60)) return;
    
    // Volume condition check
    bool volumeCondition = !UseVolumeFilter || (MathAbs(currentVROC) > VROC_Threshold);
    
    if(DetailedLogging && !volumeCondition) {
        Print("Signal blocked by volume filter - VROC: ", currentVROC, "%, Threshold: ", VROC_Threshold, "%");
    }
    
    // Bullish signal
    if(currentVWMA_RSI > 55 && currentMFI > 55 && volumeCondition) {
        if(IsConfluenceZone(ORDER_TYPE_BUY)) {
            if(DetailedLogging) Print("Bullish signal detected - VWMA_RSI: ", currentVWMA_RSI, ", MFI: ", currentMFI, ", VROC: ", currentVROC);
            ExecuteTrade(ORDER_TYPE_BUY);
        }
    }
    
    // Bearish signal  
    if(currentVWMA_RSI < 45 && currentMFI < 45 && volumeCondition) {
        if(IsConfluenceZone(ORDER_TYPE_SELL)) {
            if(DetailedLogging) Print("Bearish signal detected - VWMA_RSI: ", currentVWMA_RSI, ", MFI: ", currentMFI, ", VROC: ", currentVROC);
            ExecuteTrade(ORDER_TYPE_SELL);
        }
    }
}

//+------------------------------------------------------------------+
//| Check confluence zone                                            |
//+------------------------------------------------------------------+
bool IsConfluenceZone(ENUM_ORDER_TYPE orderType) {
    double currentPrice = (orderType == ORDER_TYPE_BUY) ? 
        SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Calculate VWMA
    double vwma = 0.0;
    double volumeSum = 0.0;
    
    for(int i = 0; i < VWMA_Period; i++) {
        double price = iClose(_Symbol, PERIOD_CURRENT, i + 1);
        long volume[];
        if(CopyTickVolume(_Symbol, PERIOD_CURRENT, i + 1, 1, volume) > 0) {
            vwma += price * volume[0];
            volumeSum += volume[0];
        }
    }
    
    if(volumeSum > 0) vwma = vwma / volumeSum;
    else return false;
    
    // Check proximity to VWMA
    double distance = MathAbs(currentPrice - vwma);
    return (distance <= currentATR * 0.5);
}

//+------------------------------------------------------------------+
//| Execute trade with comprehensive risk management                  |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE orderType) {
    double price = (orderType == ORDER_TYPE_BUY) ? 
        SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Calculate stop loss and take profit
    double sl = CalculateStopLoss(orderType, price);
    double tp = CalculateTakeProfit(orderType, price, sl);
    
    // Validate risk-reward
    double risk = MathAbs(price - sl);
    double reward = MathAbs(tp - price);
    double rr = (risk > 0) ? reward / risk : 0;
    
    if(rr < MinRiskReward) {
        if(DetailedLogging) Print("Trade rejected - R:R too low: ", rr);
        return;
    }
    
    // Calculate position size
    double lotSize = CalculatePositionSize(risk);
    if(lotSize <= 0) return;
    
    // Execute trade
    bool result = trade.PositionOpen(_Symbol, orderType, lotSize, price, sl, tp, TradeComment);
    
    if(result) {
        lastTradeTime = TimeCurrent();
        AddManagedPosition(trade.ResultOrder());
        
        if(EnableAlerts) {
            Alert(StringFormat("%s trade opened at %.5f, R:R: %.2f", 
                  EnumToString(orderType), price, rr));
        }
        
        if(DetailedLogging) {
            Print(StringFormat("%s trade executed: Price=%.5f, SL=%.5f, TP=%.5f, Lots=%.2f, R:R=%.2f",
                  EnumToString(orderType), price, sl, tp, lotSize, rr));
        }
    } else {
        if(DetailedLogging) {
            Print("Trade execution failed: ", trade.ResultRetcodeDescription());
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate dynamic stop loss                                      |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE orderType, double price) {
    double atrMultiplier = 2.0 * GetPairMultiplier();
    double slDistance = currentATR * atrMultiplier;
    
    // Ensure minimum distance
    double minDistance = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
    if(slDistance < minDistance) slDistance = minDistance * 1.2;
    
    double sl = 0;
    if(orderType == ORDER_TYPE_BUY) sl = price - slDistance;
    else sl = price + slDistance;
    
    return NormalizeDouble(sl, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate dynamic take profit                                    |
//+------------------------------------------------------------------+
double CalculateTakeProfit(ENUM_ORDER_TYPE orderType, double price, double sl) {
    double risk = MathAbs(price - sl);
    double targetRR = BaseRiskReward;
    
    if(UseDynamicTP) {
        // Adjust based on volatility
        if(volatilityRatio > 1.5) targetRR = MathMin(targetRR * 1.2, MaxRiskReward);
        if(volatilityRatio < 0.8) targetRR = MathMax(targetRR * 0.8, MinRiskReward);
    }
    
    double reward = risk * targetRR;
    
    double tp = 0;
    if(orderType == ORDER_TYPE_BUY) tp = price + reward;
    else tp = price - reward;
    
    return NormalizeDouble(tp, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate position size with adaptive risk                       |
//+------------------------------------------------------------------+
double CalculatePositionSize(double risk) {
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskPercent = GetAdaptiveRisk();
    double riskAmount = balance * (riskPercent / 100.0);
    
    // Apply pair-specific adjustment
    riskAmount *= GetPairMultiplier();
    
    // Calculate lot size
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double lots = 0;
    if(tickValue > 0 && tickSize > 0) {
        double riskPerLot = (risk / tickSize) * tickValue;
        if(riskPerLot > 0) lots = riskAmount / riskPerLot;
    }
    
    // Apply position size limit
    double maxPositionValue = balance * (MaxPositionSize / 100.0);
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double contractSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);
    double maxLots = maxPositionValue / (currentPrice * contractSize);
    
    if(lots > maxLots) lots = maxLots;
    
    // Normalize
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lots = MathRound(lots / lotStep) * lotStep;
    lots = MathMax(lots, minLot);
    lots = MathMin(lots, maxLot);
    
    return lots;
}

//+------------------------------------------------------------------+
//| Get adaptive risk based on performance                           |
//+------------------------------------------------------------------+
double GetAdaptiveRisk() {
    if(!UseAdaptiveRisk) return RiskPercentPerTrade;
    
    double baseRisk = RiskPercentPerTrade;
    
    // Reduce risk after consecutive losses
    if(consecutiveLosses >= 1) baseRisk *= 0.85;
    if(consecutiveLosses >= 2) baseRisk *= 0.7;
    if(consecutiveLosses >= 3) baseRisk *= 0.5;
    
    return MathMax(baseRisk, 0.3);
}

//+------------------------------------------------------------------+
//| Get pair-specific multiplier                                     |
//+------------------------------------------------------------------+
double GetPairMultiplier() {
    if(!OptimizeForPair) return 1.0;
    
    string symbol = _Symbol;
    
    if(StringFind(symbol, "EURUSD") >= 0) return EURUSDMultiplier;
    if(StringFind(symbol, "GBPUSD") >= 0) return GBPUSDMultiplier;
    if(StringFind(symbol, "USDJPY") >= 0) return USDJPYMultiplier;
    if(StringFind(symbol, "AUDUSD") >= 0) return AUDUSDMultiplier;
    
    return 1.0;
}

//+------------------------------------------------------------------+
//| Position management functions                                    |
//+------------------------------------------------------------------+
void ManagePositions() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) != MagicNumber) continue;
        if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
        
        ManageAdvancedPosition(ticket);
    }
}

void ManageAdvancedPosition(ulong ticket) {
    if(!PositionSelectByTicket(ticket)) return;
    
    int posIndex = FindManagedPosition(ticket);
    if(posIndex < 0) return;
    
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    double currentPrice = (posType == POSITION_TYPE_BUY) ?
        SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    double risk = MathAbs(managedPositions[posIndex].originalSL - openPrice);
    double currentProfit = (posType == POSITION_TYPE_BUY) ?
        (currentPrice - openPrice) : (openPrice - currentPrice);
    
    double currentRR = (risk > 0) ? currentProfit / risk : 0;
    
    // Update profit tracking
    if(currentProfit > managedPositions[posIndex].highestProfit)
        managedPositions[posIndex].highestProfit = currentProfit;
    
    // Breakeven management
    if(UseBreakeven && !managedPositions[posIndex].breakevenApplied && currentRR >= BreakevenRR) {
        ApplyBreakeven(ticket, posIndex, openPrice, posType, risk);
    }
    
    // Partial profit taking
    if(UsePartialClose) {
        HandlePartialClose(ticket, posIndex, currentRR, posType);
    }
    
    // Trailing stop
    if(UseTrailingStop && managedPositions[posIndex].breakevenApplied && currentRR >= TrailStartRR) {
        ApplyTrailingStop(ticket, posIndex, currentPrice, posType);
    }
}

void ApplyBreakeven(ulong ticket, int posIndex, double openPrice, ENUM_POSITION_TYPE posType, double risk) {
    double buffer = BreakevenBuffer * risk;
    double newSL = openPrice + ((posType == POSITION_TYPE_BUY ? 1 : -1) * buffer);
    
    if(trade.PositionModify(ticket, NormalizeDouble(newSL, _Digits), PositionGetDouble(POSITION_TP))) {
        managedPositions[posIndex].breakevenApplied = true;
        if(DetailedLogging) Print("Breakeven applied to position ", ticket);
    }
}

void HandlePartialClose(ulong ticket, int posIndex, double currentRR, ENUM_POSITION_TYPE posType) {
    // First partial close
    if(!managedPositions[posIndex].partial1Closed && currentRR >= PartialClose1_RR) {
        double currentVolume = PositionGetDouble(POSITION_VOLUME);
        double closeVolume = NormalizeDouble(currentVolume * PartialClose1_Percent / 100.0, 2);
        
        if(trade.PositionClosePartial(ticket, closeVolume)) {
            managedPositions[posIndex].partial1Closed = true;
            if(DetailedLogging) Print("First partial close executed for position ", ticket);
        }
    }
    
    // Second partial close
    if(!managedPositions[posIndex].partial2Closed && currentRR >= PartialClose2_RR) {
        double currentVolume = PositionGetDouble(POSITION_VOLUME);
        double closeVolume = NormalizeDouble(currentVolume * PartialClose2_Percent / 100.0, 2);
        
        if(trade.PositionClosePartial(ticket, closeVolume)) {
            managedPositions[posIndex].partial2Closed = true;
            if(DetailedLogging) Print("Second partial close executed for position ", ticket);
        }
    }
}

void ApplyTrailingStop(ulong ticket, int posIndex, double currentPrice, ENUM_POSITION_TYPE posType) {
    double newSL = 0;
    
    if(UseSwingTrailing) {
        newSL = CalculateSwingTrailingSL(posType);
    } else {
        double trailDistance = currentATR * TrailATRMultiplier;
        newSL = currentPrice - ((posType == POSITION_TYPE_BUY ? 1 : -1) * trailDistance);
    }
    
    double currentSL = PositionGetDouble(POSITION_SL);
    bool shouldModify = (posType == POSITION_TYPE_BUY && newSL > currentSL) ||
                       (posType == POSITION_TYPE_SELL && newSL < currentSL);
    
    if(shouldModify) {
        trade.PositionModify(ticket, NormalizeDouble(newSL, _Digits), PositionGetDouble(POSITION_TP));
    }
}

double CalculateSwingTrailingSL(ENUM_POSITION_TYPE posType) {
    double buffer = currentATR * 0.5;
    
    if(posType == POSITION_TYPE_BUY) {
        int lowestBar = iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, SwingLookback, 1);
        double swingLow = iLow(_Symbol, PERIOD_CURRENT, lowestBar);
        return swingLow - buffer;
    } else {
        int highestBar = iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, SwingLookback, 1);
        double swingHigh = iHigh(_Symbol, PERIOD_CURRENT, highestBar);
        return swingHigh + buffer;
    }
}

//+------------------------------------------------------------------+
//| Risk management functions                                        |
//+------------------------------------------------------------------+
void UpdateTimeFrameTracking() {
    MqlDateTime now, day_start, week_start;
    TimeCurrent(now);
    TimeToStruct(lastDayCheck, day_start);
    TimeToStruct(lastWeekCheck, week_start);
    
    // Check for new day
    if(now.day != day_start.day) {
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dailyLossLimitHit = false;
        lastDayCheck = TimeCurrent();
        
        if(DetailedLogging) Print("New trading day - Daily limits reset");
    }
    
    // Check for new week
    if(now.day_of_week < week_start.day_of_week || 
       (now.day_of_week == 1 && week_start.day_of_week > 1)) {
        weeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        weeklyLossLimitHit = false;
        drawdownLimitHit = false;
        equityProtectionHit = false;
        lastWeekCheck = TimeCurrent();
        
        if(DetailedLogging) Print("New trading week - Weekly limits reset");
    }
}

bool CheckRiskLimits() {
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    // Update peak equity
    if(currentEquity > peakEquity) peakEquity = currentEquity;
    
    // Daily loss limit
    double dailyLoss = dailyStartBalance - currentBalance;
    if(dailyLoss > dailyStartBalance * (DailyLossLimit / 100.0)) {
        dailyLossLimitHit = true;
        if(DetailedLogging) Print("Daily loss limit reached");
        return false;
    }
    
    // Weekly loss limit
    double weeklyLoss = weeklyStartBalance - currentBalance;
    if(weeklyLoss > weeklyStartBalance * (WeeklyLossLimit / 100.0)) {
        weeklyLossLimitHit = true;
        if(DetailedLogging) Print("Weekly loss limit reached");
        return false;
    }
    
    // Drawdown limit
    double equityDrawdown = (peakEquity - currentEquity) / peakEquity * 100.0;
    if(equityDrawdown >= MaxDrawdownPercent) {
        drawdownLimitHit = true;
        if(DetailedLogging) Print("Drawdown limit reached: ", equityDrawdown, "%");
        return false;
    }
    
    // Equity protection
    double equityPercent = currentEquity / peakEquity * 100.0;
    if(equityPercent < EquityProtectionLevel) {
        equityProtectionHit = true;
        if(DetailedLogging) Print("Equity protection triggered");
        return false;
    }
    
    return true;
}

bool IsValidTradingSession() {
    MqlDateTime time;
    TimeCurrent(time);
    int hour = time.hour;
    
    bool isAsian = (hour >= 0 && hour < 8);
    bool isLondon = (hour >= 8 && hour < 16);
    bool isNY = (hour >= 13 && hour < 21);
    
    if(TradeOverlapOnly) {
        return (hour >= 13 && hour < 16); // London-NY overlap
    }
    
    if(TradeAsianSession && isAsian) return true;
    if(TradeLondonSession && isLondon) return true;
    if(TradeNYSession && isNY) return true;
    
    return false;
}

void CheckTimeBasedManagement() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetInteger(POSITION_MAGIC) != MagicNumber) continue;
        
        datetime openTime = (datetime)PositionGetInteger(POSITION_TIME);
        int hoursOpen = (int)((TimeCurrent() - openTime) / 3600);
        
        if(hoursOpen >= MaxTradeHours) {
            trade.PositionClose(ticket);
            if(DetailedLogging) Print("Position closed due to time limit: ", ticket);
        }
    }
}

//+------------------------------------------------------------------+
//| Position tracking functions                                      |
//+------------------------------------------------------------------+
void AddManagedPosition(ulong ticket) {
    if(!PositionSelectByTicket(ticket)) return;
    
    ManagedPosition newPos;
    newPos.ticket = ticket;
    newPos.openTime = (datetime)PositionGetInteger(POSITION_TIME);
    newPos.entryPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    newPos.originalSL = PositionGetDouble(POSITION_SL);
    newPos.type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    newPos.breakevenApplied = false;
    newPos.trailingActive = false;
    newPos.partial1Closed = false;
    newPos.partial2Closed = false;
    newPos.highestProfit = 0;
    
    int size = ArraySize(managedPositions);
    ArrayResize(managedPositions, size + 1);
    managedPositions[size] = newPos;
}

int FindManagedPosition(ulong ticket) {
    for(int i = 0; i < ArraySize(managedPositions); i++) {
        if(managedPositions[i].ticket == ticket) return i;
    }
    return -1;
}

void RemoveManagedPosition(ulong ticket) {
    for(int i = ArraySize(managedPositions) - 1; i >= 0; i--) {
        if(managedPositions[i].ticket == ticket) {
            ArrayRemove(managedPositions, i, 1);
            break;
        }
    }
}

int GetCurrentPositions() {
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Trade transaction handler                                        |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result) {
    if(trans.type != TRADE_TRANSACTION_DEAL_ADD) return;
    if(!HistoryDealSelect(trans.deal)) return;
    if(HistoryDealGetInteger(trans.deal, DEAL_MAGIC) != MagicNumber) return;

    long dealEntry = HistoryDealGetInteger(trans.deal, DEAL_ENTRY);
    ulong positionId = HistoryDealGetInteger(trans.deal, DEAL_POSITION_ID);

    if(dealEntry == DEAL_ENTRY_OUT) {
        // Position closed
        double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
        
        // Update consecutive losses
        if(profit < 0) consecutiveLosses++;
        else consecutiveLosses = 0;
        
        // Remove from managed positions
        RemoveManagedPosition(positionId);
        
        if(DetailedLogging) {
            Print("Position closed: ", positionId, ", Profit: ", profit, 
                  ", Consecutive losses: ", consecutiveLosses);
        }
    }
}

//+------------------------------------------------------------------+
//| Dashboard functions                                              |
//+------------------------------------------------------------------+
void CreateDashboard() {
    // Create background
    ObjectCreate(0, "Dashboard_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_CORNER, DashboardCorner);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_XDISTANCE, DashboardX);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_YDISTANCE, DashboardY);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_XSIZE, 280);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_YSIZE, 200);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_BGCOLOR, BackgroundColor);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);

    // Create labels
    string labels[] = {"Title", "Account", "Risk", "DailyPL", "Positions", "Signal", "VWMA", "MFI", "VROC", "ATR", "Status"};
    
    for(int i = 0; i < ArraySize(labels); i++) {
        ObjectCreate(0, "Label_" + labels[i], OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, "Label_" + labels[i], OBJPROP_CORNER, DashboardCorner);
        ObjectSetInteger(0, "Label_" + labels[i], OBJPROP_XDISTANCE, DashboardX + 10);
        ObjectSetInteger(0, "Label_" + labels[i], OBJPROP_YDISTANCE, DashboardY + 15 + (i * 18));
        ObjectSetInteger(0, "Label_" + labels[i], OBJPROP_COLOR, TextColor);
        ObjectSetInteger(0, "Label_" + labels[i], OBJPROP_FONTSIZE, 9);
    }
}

void UpdateDashboard() {
    // Title
    ObjectSetString(0, "Label_Title", OBJPROP_TEXT, "Comprehensive Day Trader v1.0");
    
    // Account info
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    ObjectSetString(0, "Label_Account", OBJPROP_TEXT, 
                   StringFormat("Balance: %.2f | Equity: %.2f", balance, equity));
    
    // Risk status
    double dailyPL = balance - dailyStartBalance;
    double dailyPLPercent = (dailyStartBalance > 0) ? (dailyPL / dailyStartBalance) * 100 : 0;
    ObjectSetString(0, "Label_Risk", OBJPROP_TEXT, 
                   StringFormat("Risk: %.1f%% | Losses: %d", GetAdaptiveRisk(), consecutiveLosses));
    
    // Daily P&L
    ObjectSetString(0, "Label_DailyPL", OBJPROP_TEXT, 
                   StringFormat("Daily P&L: %.2f (%.2f%%)", dailyPL, dailyPLPercent));
    ObjectSetInteger(0, "Label_DailyPL", OBJPROP_COLOR, dailyPL >= 0 ? BullishColor : BearishColor);
    
    // Positions
    int currentPos = GetCurrentPositions();
    ObjectSetString(0, "Label_Positions", OBJPROP_TEXT, 
                   StringFormat("Positions: %d/%d", currentPos, MaxConcurrentPositions));
    
    // Signal strength with volume consideration
    bool volumeOK = !UseVolumeFilter || (MathAbs(currentVROC) > VROC_Threshold);
    double signalStrength = (currentVWMA_RSI + currentMFI + (volumeOK ? 70 : 30)) / 3.0;
    ObjectSetString(0, "Label_Signal", OBJPROP_TEXT, 
                   StringFormat("Signal: %.1f %s", signalStrength, volumeOK ? "✓" : "⚠"));
    ObjectSetInteger(0, "Label_Signal", OBJPROP_COLOR, 
                    signalStrength > 60 && volumeOK ? BullishColor : (signalStrength < 40 ? BearishColor : NeutralColor));
    
    // Indicators
    ObjectSetString(0, "Label_VWMA", OBJPROP_TEXT, StringFormat("VWMA-RSI: %.1f", currentVWMA_RSI));
    ObjectSetString(0, "Label_MFI", OBJPROP_TEXT, StringFormat("MFI: %.1f", currentMFI));
    // Enhanced VROC display with current volume
    long currentVol[];
    string volumeInfo = "";
    if(CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, 1, currentVol) > 0) {
        volumeInfo = StringFormat(" [Vol:%d]", currentVol[0]);
    }
    ObjectSetString(0, "Label_VROC", OBJPROP_TEXT, StringFormat("VROC: %.1f%%%s", currentVROC, volumeInfo));
    ObjectSetString(0, "Label_ATR", OBJPROP_TEXT, StringFormat("ATR: %.5f (%.2f)", currentATR, volatilityRatio));
    
    // Status
    bool tradingAllowed = CheckRiskLimits() && IsValidTradingSession() && (volatilityRatio >= VolatilityFilter);
    ObjectSetString(0, "Label_Status", OBJPROP_TEXT, StringFormat("Status: %s", tradingAllowed ? "ACTIVE" : "PAUSED"));
    ObjectSetInteger(0, "Label_Status", OBJPROP_COLOR, tradingAllowed ? BullishColor : BearishColor);
}

void DeleteDashboard() {
    ObjectsDeleteAll(0, "Dashboard_");
    ObjectsDeleteAll(0, "Label_");
}