//+------------------------------------------------------------------+
//| UltraConservative_1000_EA.mq5                                   |
//| Ultra Conservative MACD + Bollinger Bands EA for $1000 Accounts|
//| Copyright 2025, Advanced Trading Systems                        |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Advanced Trading Systems"
#property link      "https://advancedtrading.com"
#property version   "4.00" // Version updated to reflect new engine

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//+------------------------------------------------------------------+
//| Alert type constants                                            |
//+------------------------------------------------------------------+
#define ALERT_INFO     0
#define ALERT_WARNING  1
#define ALERT_CRITICAL 2

//--- Input parameters
input group "=== ACCOUNT PROTECTION ==="
input double InpMaxRiskPercent = 1.0;        // Maximum Risk Per Trade (%)
input double InpDailyLossLimit = 1.5;        // Daily Loss Limit (%)
input double InpWeeklyLossLimit = 5.0;       // Weekly Loss Limit (%)
input double InpMonthlyLossLimit = 15.0;     // Monthly Loss Limit (%)
input double InpEmergencyStopPercent = 20.0; // Emergency Stop at % Loss
input double InpMinAccountBalance = 800.0;   // Minimum Account Balance

input group "=== MACD SETTINGS ==="
input int InpMACDFast = 12;                  // MACD Fast EMA
input int InpMACDSlow = 26;                  // MACD Slow EMA
input int InpMACDSignal = 9;                 // MACD Signal Period

input group "=== BOLLINGER BANDS ==="
input int InpBBPeriod = 20;                  // Bollinger Bands Period
input double InpBBDeviations = 2.0;          // Bollinger Bands Deviations
input double InpMinBBWidthPips = 15.0;       // Min BB Width to Confirm Volatility (Pips)

input group "=== STRATEGY FILTERS ==="
input int    InpEMAPeriod = 200;                 // Trend-Filtering EMA Period
input int    InpADXPeriod = 14;                  // ADX Period
input double InpADXTrend = 25.0;                 // ADX Trend Threshold
input int    InpRSI_Period = 14;                 // RSI Period for Momentum Filter

input group "=== POSITION MANAGEMENT ==="
input double InpStopLossBuffer = 10.0;       // Stop Loss Buffer (pips)
input double InpTakeProfitRatio = 2.0;       // Take Profit Ratio (R:R)
input double InpTrailingStop = 20.0;         // Trailing Stop (pips)
input double InpBreakEvenLevel = 1.5;        // Break Even at R:R Level

input group "=== TRADING HOURS ==="
input int InpStartHour = 7;                  // Start Trading Hour (GMT)
input int InpEndHour = 20;                   // End Trading Hour (GMT)
input bool InpTradeOnlyOverlap = false;      // Trade Only During Session Overlap

input group "=== PORTFOLIO RISK MANAGEMENT ==="
input int InpMaxConcurrentTrades = 3;        // Maximum Concurrent Positions
input double InpMaxPortfolioHeat = 6.0;      // Maximum Portfolio Risk (%)
input double InpMaxCurrencyExposure = 40.0;  // Max Exposure Per Currency (%)
input double InpCorrelationLimit = 0.7;      // Max Correlation Between Trades

input group "=== PERFORMANCE-BASED RISK ==="
input bool InpUseAdaptiveRisk = true;        // Enable Adaptive Risk Scaling
input double InpMaxRiskMultiplier = 2.0;     // Max Risk Multiplier for Win Streaks
input double InpMinRiskMultiplier = 0.3;     // Min Risk Multiplier for Loss Streaks
input int InpConsecutiveWinBonus = 3;        // Wins to Trigger Risk Increase
input int InpConsecutiveLossReduction = 2;   // Losses to Trigger Risk Reduction

input group "=== ENHANCED POSITION MANAGEMENT ==="
input bool InpUsePartialTP = true;           // Enable Partial Take Profit
input double InpPartialTP1_RR = 1.0;         // First Partial TP R:R Level
input double InpPartialTP1_Percent = 40.0;   // First Partial TP Percentage
input double InpPartialTP2_RR = 2.0;         // Second Partial TP R:R Level
input double InpPartialTP2_Percent = 30.0;   // Second Partial TP Percentage
input bool InpUseATRTrailing = true;         // Use ATR-Based Trailing Stop
input double InpATRTrailingMultiplier = 1.5; // ATR Multiplier for Trailing

input group "=== VOLATILITY ENHANCEMENT ==="
input bool InpUseATRFilter = true;           // Enable ATR Volatility Filter
input int InpATRPeriod = 14;                 // ATR Period
input double InpMinATRMultiplier = 0.8;      // Minimum ATR vs Average
input double InpMaxATRMultiplier = 3.0;      // Maximum ATR vs Average
input bool InpUseVolatilityAdjustment = true; // Volatility-Adjusted Position Sizing

input group "=== NEWS/EVENT MANAGEMENT ==="
input bool InpAvoidNews = true;              // Avoid Trading During News
input int InpNewsBufferMinutes = 30;         // News Avoidance Buffer (Minutes)
input bool InpReduceRiskFriday = true;       // Reduce Risk on Fridays
input double InpFridayRiskReduction = 0.5;   // Friday Risk Reduction Factor

input group "=== ALERTS ==="
input bool InpEnableAlerts = true;           // Enable Alert Notifications
input bool InpEnableEmail = false;           // Enable Email Alerts
input bool InpEnablePush = false;            // Enable Push Notifications

//--- Global variables
CTrade trade;
CPositionInfo positionInfo;
CAccountInfo accountInfo;

int macdHandle, bbHandle, adxHandle, emaHandle, atrHandle, rsiHandle;
double macdMain[], macdSignal[], bbUpper[], bbMiddle[], bbLower[], adxBuffer[], emaBuffer[], atrBuffer[], rsiBuffer[];
string currentStrategy = ""; // Holds the name of the strategy that triggered a signal

// Enhanced risk management variables
int consecutiveWins = 0;
int consecutiveLosses = 0;
double currentRiskMultiplier = 1.0;
datetime lastNewsCheckTime = 0;
double portfolioHeat = 0.0;
string activeCurrencies[10];
double currencyExposure[10];
int activeCurrencyCount = 0;

//--- State flags for advanced logic
bool bullishDivergenceSetup = false;
bool bearishDivergenceSetup = false;
int pendingSignal = 0;
datetime signalBarTime = 0;
string pendingStrategy = "";

double initialBalance;
double dailyStartBalance;
double weeklyStartBalance;
double monthlyStartBalance;

datetime lastTradeTime = 0;
datetime dailyResetTime = 0;
datetime weeklyResetTime = 0;
datetime monthlyResetTime = 0;

bool emergencyStopTriggered = false;
bool dailyLimitReached = false;
bool weeklyLimitReached = false;
bool monthlyLimitReached = false;

double tickSize, tickValue, lotStep, minLot, maxLot;
int magicNumber = 123456;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize trade object
    trade.SetExpertMagicNumber(magicNumber);
    trade.SetDeviationInPoints(50);
    trade.SetTypeFilling(ORDER_FILLING_IOC);
    
    // Get symbol information
    tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    
    // Initialize indicators
    macdHandle = iMACD(_Symbol, PERIOD_CURRENT, InpMACDFast, InpMACDSlow, InpMACDSignal, PRICE_CLOSE);
    bbHandle = iBands(_Symbol, PERIOD_CURRENT, InpBBPeriod, 0, InpBBDeviations, PRICE_CLOSE);
    adxHandle = iADX(_Symbol, PERIOD_CURRENT, InpADXPeriod);
    emaHandle = iMA(_Symbol, PERIOD_CURRENT, InpEMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
    atrHandle = iATR(_Symbol, PERIOD_CURRENT, InpATRPeriod);
    rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, InpRSI_Period, PRICE_CLOSE);
    
    if(macdHandle == INVALID_HANDLE || bbHandle == INVALID_HANDLE || adxHandle == INVALID_HANDLE ||
       emaHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE || rsiHandle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to create one or more indicator handles");
        return INIT_FAILED;
    }
    
    // Initialize balance tracking
    initialBalance = accountInfo.Balance();
    dailyStartBalance = initialBalance;
    weeklyStartBalance = initialBalance;
    monthlyStartBalance = initialBalance;
    
    // Set reset times
    ResetDailyTracking();
    ResetWeeklyTracking();
    ResetMonthlyTracking();
    
    Print("Ultra Conservative EA v4.0 (Final) initialized successfully.");
    Print("Initial Balance: $", initialBalance);
    Print("Emergency Stop Level: $", initialBalance * (100 - InpEmergencyStopPercent) / 100);
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(macdHandle != INVALID_HANDLE) IndicatorRelease(macdHandle);
    if(bbHandle != INVALID_HANDLE) IndicatorRelease(bbHandle);
    if(adxHandle != INVALID_HANDLE) IndicatorRelease(adxHandle);
    if(emaHandle != INVALID_HANDLE) IndicatorRelease(emaHandle);
    if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
    if(rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
    
    Print("Ultra Conservative EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentBarTime <= lastBarTime)
        return;
    
    lastBarTime = currentBarTime;
    
    // Update time-based tracking
    UpdateTimeBasedTracking();
    
    // Emergency protection check
    if(!CheckEmergencyProtection())
        return;
    
    // Check daily/weekly/monthly limits
    if(!CheckRiskLimits())
        return;
    
    // Check trading hours
    if(!IsValidTradingTime())
        return;
    
    // Copy indicator values
    if(!CopyIndicatorBuffers())
        return;
    
    // Manage existing positions (includes dynamic exits and enhanced features)
    ManageOpenPositions();
    EnhancedManageOpenPositions();
    
    // Check for new signals if no position open
    
    // --- Cooldown to prevent double entry on the same signal ---
    if(TimeCurrent() - lastTradeTime < PeriodSeconds())
        return;
        
    if(!positionInfo.Select(_Symbol))
    {
        int signal = GetTradingSignal();
        if(signal != 0)
        {
            // currentStrategy is set globally by GetTradingSignal()
            ExecuteTrade(signal, currentStrategy);
        }
    }
}

//+------------------------------------------------------------------+
//| Copy indicator buffers                                           |
//+------------------------------------------------------------------+
bool CopyIndicatorBuffers()
{
    if(CopyBuffer(macdHandle, 0, 0, 100, macdMain) < 100) return false;
    if(CopyBuffer(macdHandle, 1, 0, 100, macdSignal) < 100) return false;
    if(CopyBuffer(bbHandle, 0, 0, 3, bbUpper) < 3) return false;
    if(CopyBuffer(bbHandle, 1, 0, 3, bbMiddle) < 3) return false;
    if(CopyBuffer(bbHandle, 2, 0, 3, bbLower) < 3) return false;
    if(CopyBuffer(adxHandle, 0, 0, 3, adxBuffer) < 3) return false;
    if(CopyBuffer(emaHandle, 0, 0, 3, emaBuffer) < 3) return false;
    if(CopyBuffer(atrHandle, 0, 0, 50, atrBuffer) < 50) return false;
    if(CopyBuffer(rsiHandle, 0, 0, 3, rsiBuffer) < 3) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Enhanced Risk Management Functions                               |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Calculate current portfolio heat                                 |
//+------------------------------------------------------------------+
void UpdatePortfolioHeat()
{
    portfolioHeat = 0.0;
    activeCurrencyCount = 0;
    ArrayInitialize(currencyExposure, 0.0);
    for(int i = 0; i < ArraySize(activeCurrencies); i++)
        activeCurrencies[i] = "";
    
    double balance = accountInfo.Balance();
    int totalPositions = PositionsTotal();
    
    Print("=== PORTFOLIO HEAT ANALYSIS ===");
    Print("Current Balance: $", balance);
    Print("Total Positions to Analyze: ", totalPositions);
    
    for(int i = 0; i < totalPositions; i++)
    {
        if(!positionInfo.SelectByIndex(i)) continue;
        if(positionInfo.Magic() != magicNumber) continue;
        
        string symbol = positionInfo.Symbol();
        double volume = positionInfo.Volume();
        double openPrice = positionInfo.PriceOpen();
        double stopLoss = positionInfo.StopLoss();
        
        double positionRisk = MathAbs(openPrice - stopLoss);
        positionRisk *= volume;
        positionRisk *= SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
        
        double riskPercent = (positionRisk / balance) * 100.0;
        portfolioHeat += riskPercent;
        
        Print("Position ", i+1, ": ", symbol, " | Volume: ", volume, 
              " | Risk: $", positionRisk, " (", riskPercent, "%)");
        
        // Track currency exposure
        string baseCurrency = StringSubstr(symbol, 0, 3);
        string quoteCurrency = StringSubstr(symbol, 3, 3);
        
        Print("  Currency Exposure: ", baseCurrency, " and ", quoteCurrency, " each get ", riskPercent, "% exposure");
        
        AddCurrencyExposure(baseCurrency, riskPercent);
        AddCurrencyExposure(quoteCurrency, riskPercent);
    }
    
    Print("TOTAL Portfolio Heat: ", portfolioHeat, "% (Limit: ", InpMaxPortfolioHeat, "%)");
    Print("Active Currencies: ", activeCurrencyCount);
    for(int i = 0; i < activeCurrencyCount; i++)
    {
        Print("  ", activeCurrencies[i], ": ", currencyExposure[i], "% exposure");
    }
    Print("=== END PORTFOLIO ANALYSIS ===");
}

//+------------------------------------------------------------------+
//| Add currency exposure tracking                                   |
//+------------------------------------------------------------------+
void AddCurrencyExposure(string currency, double exposure)
{
    for(int i = 0; i < activeCurrencyCount; i++)
    {
        if(activeCurrencies[i] == currency)
        {
            currencyExposure[i] += exposure;
            return;
        }
    }
    
    if(activeCurrencyCount < 10)
    {
        activeCurrencies[activeCurrencyCount] = currency;
        currencyExposure[activeCurrencyCount] = exposure;
        activeCurrencyCount++;
    }
}

//+------------------------------------------------------------------+
//| Check portfolio risk limits                                      |
//+------------------------------------------------------------------+
bool IsPortfolioRiskAcceptable(string newSymbol, double newRisk)
{
    Print("=== PORTFOLIO RISK CHECK FOR NEW TRADE ===");
    Print("New Symbol: ", newSymbol, " | New Trade Risk: ", newRisk, "%");
    
    UpdatePortfolioHeat();
    
    // Check maximum concurrent trades
    int currentPositions = PositionsTotal();
    Print("Concurrent Trades Check: ", currentPositions, "/", InpMaxConcurrentTrades, " positions");
    if(currentPositions >= InpMaxConcurrentTrades)
    {
        Print("❌ TRADE REJECTED: Maximum concurrent trades reached (", currentPositions, "/", InpMaxConcurrentTrades, ")");
        return false;
    }
    Print("✅ Concurrent trades limit OK");
    
    // Check portfolio heat
    double newTotalHeat = portfolioHeat + newRisk;
    Print("Portfolio Heat Check: Current ", portfolioHeat, "% + New ", newRisk, "% = ", newTotalHeat, "% (Limit: ", InpMaxPortfolioHeat, "%)");
    if(newTotalHeat > InpMaxPortfolioHeat)
    {
        Print("❌ TRADE REJECTED: Portfolio heat limit exceeded (", newTotalHeat, "/", InpMaxPortfolioHeat, "%)");
        return false;
    }
    Print("✅ Portfolio heat limit OK");
    
    // Check currency exposure
    string baseCurrency = StringSubstr(newSymbol, 0, 3);
    string quoteCurrency = StringSubstr(newSymbol, 3, 3);
    
    double currentBaseExposure = GetCurrencyExposure(baseCurrency);
    double currentQuoteExposure = GetCurrencyExposure(quoteCurrency);
    double newBaseExposure = currentBaseExposure + newRisk;
    double newQuoteExposure = currentQuoteExposure + newRisk;
    
    Print("Currency Exposure Check:");
    Print("  ", baseCurrency, ": Current ", currentBaseExposure, "% + New ", newRisk, "% = ", newBaseExposure, "% (Limit: ", InpMaxCurrencyExposure, "%)");
    Print("  ", quoteCurrency, ": Current ", currentQuoteExposure, "% + New ", newRisk, "% = ", newQuoteExposure, "% (Limit: ", InpMaxCurrencyExposure, "%)");
    
    if(newBaseExposure > InpMaxCurrencyExposure || newQuoteExposure > InpMaxCurrencyExposure)
    {
        Print("❌ TRADE REJECTED: Currency exposure limit exceeded for ", baseCurrency, " or ", quoteCurrency);
        return false;
    }
    Print("✅ Currency exposure limits OK");
    
    // Check correlation (simplified - assumes EURUSD/GBPUSD high correlation)
    Print("Correlation Check for ", newSymbol, "...");
    if(CheckCorrelationLimit(newSymbol))
    {
        Print("❌ TRADE REJECTED: Correlation limit exceeded for ", newSymbol);
        return false;
    }
    Print("✅ Correlation limit OK");
    
    Print("🎯 PORTFOLIO RISK CHECK PASSED - Trade approved!");
    Print("=== END PORTFOLIO RISK CHECK ===");
    return true;
}

//+------------------------------------------------------------------+
//| Get currency exposure                                            |
//+------------------------------------------------------------------+
double GetCurrencyExposure(string currency)
{
    for(int i = 0; i < activeCurrencyCount; i++)
    {
        if(activeCurrencies[i] == currency)
            return currencyExposure[i];
    }
    return 0.0;
}

//+------------------------------------------------------------------+
//| Check correlation limits (simplified)                           |
//+------------------------------------------------------------------+
bool CheckCorrelationLimit(string newSymbol)
{
    // Simple correlation check for major pairs
    string correlatedPairs[] = {"EURUSD", "GBPUSD", "AUDUSD", "NZDUSD"};
    
    for(int i = 0; i < ArraySize(correlatedPairs); i++)
    {
        if(correlatedPairs[i] == newSymbol) continue;
        
        for(int j = 0; j < PositionsTotal(); j++)
        {
            if(!positionInfo.SelectByIndex(j)) continue;
            if(positionInfo.Magic() != magicNumber) continue;
            
            if(positionInfo.Symbol() == correlatedPairs[i])
            {
                // High correlation pairs found - check if correlation limit exceeded
                return true; // Block for now - can be enhanced with actual correlation calculation
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Calculate adaptive risk multiplier                              |
//+------------------------------------------------------------------+
double CalculateAdaptiveRisk()
{
    Print("=== ADAPTIVE RISK CALCULATION ===");
    
    if(!InpUseAdaptiveRisk) 
    {
        Print("Adaptive risk disabled - using base multiplier: 1.0");
        return 1.0;
    }
    
    Print("Performance History: ", consecutiveWins, " wins, ", consecutiveLosses, " losses");
    Print("Win Bonus Trigger: ", InpConsecutiveWinBonus, " | Loss Reduction Trigger: ", InpConsecutiveLossReduction);
    
    double baseMultiplier = 1.0;
    
    // Increase risk after consecutive wins
    if(consecutiveWins >= InpConsecutiveWinBonus)
    {
        double winBonus = MathMin(InpMaxRiskMultiplier, 1.0 + (consecutiveWins - InpConsecutiveWinBonus + 1) * 0.3);
        currentRiskMultiplier = winBonus;
        Print("🎯 WIN STREAK BONUS: ", consecutiveWins, " wins ≥ ", InpConsecutiveWinBonus, " → Risk multiplier: ", currentRiskMultiplier);
    }
    // Reduce risk after consecutive losses
    else if(consecutiveLosses >= InpConsecutiveLossReduction)
    {
        double lossReduction = MathMax(InpMinRiskMultiplier, 1.0 - (consecutiveLosses - InpConsecutiveLossReduction + 1) * 0.2);
        currentRiskMultiplier = lossReduction;
        Print("⚠️ LOSS STREAK PROTECTION: ", consecutiveLosses, " losses ≥ ", InpConsecutiveLossReduction, " → Risk multiplier: ", currentRiskMultiplier);
    }
    else
    {
        currentRiskMultiplier = 1.0;
        Print("📊 NEUTRAL PERFORMANCE: Risk multiplier: ", currentRiskMultiplier, " (base level)");
    }
    
    // Apply Friday risk reduction
    MqlDateTime dt;
    TimeCurrent(dt);
    string dayName = "";
    switch(dt.day_of_week)
    {
        case 1: dayName = "Monday"; break;
        case 2: dayName = "Tuesday"; break;
        case 3: dayName = "Wednesday"; break;
        case 4: dayName = "Thursday"; break;
        case 5: dayName = "Friday"; break;
        case 6: dayName = "Saturday"; break;
        case 0: dayName = "Sunday"; break;
    }
    
    if(InpReduceRiskFriday && dt.day_of_week == 5)
    {
        double beforeFridayAdjustment = currentRiskMultiplier;
        currentRiskMultiplier *= InpFridayRiskReduction;
        Print("📅 FRIDAY RISK REDUCTION: ", dayName, " detected → Multiplier reduced from ", beforeFridayAdjustment, " to ", currentRiskMultiplier);
    }
    else
    {
        Print("📅 Day of Week: ", dayName, " (No Friday adjustment needed)");
    }
    
    Print("FINAL Risk Multiplier: ", currentRiskMultiplier, " (Range: ", InpMinRiskMultiplier, " - ", InpMaxRiskMultiplier, ")");
    Print("=== END ADAPTIVE RISK CALCULATION ===");
    
    return currentRiskMultiplier;
}

//+------------------------------------------------------------------+
//| Enhanced volatility filter                                      |
//+------------------------------------------------------------------+
bool IsVolatilityAcceptable()
{
    Print("=== ENHANCED VOLATILITY FILTER ===");
    
    // Original BB width check (preserved)
    double bbWidth = (bbUpper[0] - bbLower[0]);
    double bbWidthPips = bbWidth / _Point / 10;
    double minBBWidth = InpMinBBWidthPips * _Point;
    
    Print("🔵 BOLLINGER BANDS FILTER:");
    Print("  BB Width: ", bbWidthPips, " pips (Minimum: ", InpMinBBWidthPips, " pips)");
    Print("  BB Upper: ", bbUpper[0], " | BB Lower: ", bbLower[0]);
    
    if(bbWidth < minBBWidth) 
    {
        Print("❌ VOLATILITY REJECTED: BB width too narrow (", bbWidthPips, " < ", InpMinBBWidthPips, " pips)");
        Print("=== END VOLATILITY FILTER ===");
        return false;
    }
    Print("✅ BB width acceptable");
    
    // Additional ATR volatility filter
    if(InpUseATRFilter)
    {
        Print("🔶 ATR VOLATILITY FILTER:");
        double currentATR = atrBuffer[0];
        double currentATRPips = currentATR / _Point / 10;
        
        // Calculate ATR average
        double atrSum = 0.0;
        int atrPeriod = MathMin(InpATRPeriod * 2, ArraySize(atrBuffer));
        for(int i = 1; i < atrPeriod; i++)
            atrSum += atrBuffer[i];
        double avgATR = atrSum / (atrPeriod - 1);
        double avgATRPips = avgATR / _Point / 10;
        
        Print("  Current ATR: ", currentATRPips, " pips");
        Print("  Average ATR (", atrPeriod-1, " periods): ", avgATRPips, " pips");
        
        if(avgATR > 0)
        {
            double atrRatio = currentATR / avgATR;
            Print("  ATR Ratio: ", atrRatio, " (Range: ", InpMinATRMultiplier, " - ", InpMaxATRMultiplier, ")");
            
            if(atrRatio < InpMinATRMultiplier)
            {
                Print("❌ VOLATILITY REJECTED: ATR too low (", atrRatio, " < ", InpMinATRMultiplier, ")");
                Print("  Market too quiet for trading");
                Print("=== END VOLATILITY FILTER ===");
                return false;
            }
            
            if(atrRatio > InpMaxATRMultiplier)
            {
                Print("❌ VOLATILITY REJECTED: ATR too high (", atrRatio, " > ", InpMaxATRMultiplier, ")");
                Print("  Market too volatile for safe trading");
                Print("=== END VOLATILITY FILTER ===");
                return false;
            }
            
            Print("✅ ATR ratio acceptable");
        }
        else
        {
            Print("⚠️ WARNING: Average ATR is zero - skipping ATR filter");
        }
    }
    else
    {
        Print("🔶 ATR FILTER: Disabled");
    }
    
    Print("🎯 VOLATILITY FILTER PASSED - Market conditions suitable for trading");
    Print("=== END VOLATILITY FILTER ===");
    return true;
}

//+------------------------------------------------------------------+
//| Check if it's safe to trade (news/event filter)                 |
//+------------------------------------------------------------------+
bool IsSafeToTrade()
{
    if(!InpAvoidNews) return true;
    
    MqlDateTime dt;
    TimeCurrent(dt);
    
    // Avoid trading during high-impact news times (simplified)
    // In real implementation, this would integrate with economic calendar
    
    // Avoid trading 30 minutes before/after major session opens
    int hour = dt.hour;
    int minute = dt.min;
    
    // London open (8:00 GMT)
    if(hour == 7 && minute >= 30) return false;
    if(hour == 8 && minute <= 30) return false;
    
    // NY open (13:00 GMT)
    if(hour == 12 && minute >= 30) return false;
    if(hour == 13 && minute <= 30) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check emergency protection                                       |
//+------------------------------------------------------------------+
bool CheckEmergencyProtection()
{
    double currentEquity = accountInfo.Equity();
    double currentBalance = accountInfo.Balance();
    
    if(currentBalance < InpMinAccountBalance)
    {
        if(!emergencyStopTriggered)
        {
            emergencyStopTriggered = true;
            CloseAllPositions();
            SendAlert("EMERGENCY: Account below minimum balance! Trading suspended.", ALERT_CRITICAL);
        }
        return false;
    }
    
    double emergencyLevel = initialBalance * (100 - InpEmergencyStopPercent) / 100;
    if(currentEquity <= emergencyLevel)
    {
        if(!emergencyStopTriggered)
        {
            emergencyStopTriggered = true;
            CloseAllPositions();
            SendAlert("EMERGENCY: " + DoubleToString(InpEmergencyStopPercent, 1) + 
                     "% loss reached! Trading suspended.", ALERT_CRITICAL);
        }
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check risk limits                                                |
//+------------------------------------------------------------------+
bool CheckRiskLimits()
{
    double currentEquity = accountInfo.Equity();
    
    double dailyLoss = dailyStartBalance - currentEquity;
    double dailyLossPercent = (dailyLoss / dailyStartBalance) * 100;
    
    if(dailyLossPercent >= InpDailyLossLimit)
    {
        if(!dailyLimitReached)
        {
            dailyLimitReached = true;
            CloseAllPositions();
            SendAlert("Daily loss limit of " + DoubleToString(InpDailyLossLimit, 1) + 
                     "% reached. Trading suspended until tomorrow.", ALERT_WARNING);
        }
        return false;
    }
    
    double weeklyLoss = weeklyStartBalance - currentEquity;
    double weeklyLossPercent = (weeklyLoss / weeklyStartBalance) * 100;
    
    if(weeklyLossPercent >= InpWeeklyLossLimit)
    {
        if(!weeklyLimitReached)
        {
            weeklyLimitReached = true;
            CloseAllPositions();
            SendAlert("Weekly loss limit of " + DoubleToString(InpWeeklyLossLimit, 1) + 
                     "% reached. Trading suspended until next week.", ALERT_WARNING);
        }
        return false;
    }
    
    double monthlyLoss = monthlyStartBalance - currentEquity;
    double monthlyLossPercent = (monthlyLoss / monthlyStartBalance) * 100;
    
    if(monthlyLossPercent >= InpMonthlyLossLimit)
    {
        if(!monthlyLimitReached)
        {
            monthlyLimitReached = true;
            CloseAllPositions();
            SendAlert("Monthly loss limit of " + DoubleToString(InpMonthlyLossLimit, 1) + 
                     "% reached. Trading suspended until next month.", ALERT_CRITICAL);
        }
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if valid trading time                                      |
//+------------------------------------------------------------------+
bool IsValidTradingTime()
{
    MqlDateTime dt;
    TimeToStruct(TimeGMT(), dt);
    
    if(dt.day_of_week == 0 || dt.day_of_week == 6)
        return false;
    
    if(dt.hour < InpStartHour || dt.hour >= InpEndHour)
        return false;
    
    if(InpTradeOnlyOverlap)
    {
        if(dt.hour < 13 || dt.hour >= 16)
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Helper: Finds the bar index of the last peak in a data array.    |
//+------------------------------------------------------------------+
int FindLastPeak(const double &data[], int start, int end)
{
    // Ensure we have enough data points and valid bounds
    if(start < 1 || end <= start + 2) return -1;
    
    int peakIndex = -1;
    double peakValue = -DBL_MAX;
    for(int i = MathMax(start, 1); i < end - 1; i++)
    {
        if(data[i] > data[i-1] && data[i] > data[i+1] && data[i] > peakValue)
        {
            peakValue = data[i];
            peakIndex = i;
        }
    }
    return peakIndex;
}

//+------------------------------------------------------------------+
//| Helper: Finds the bar index of the last trough in a data array.  |
//+------------------------------------------------------------------+
int FindLastTrough(const double &data[], int start, int end)
{
    // Ensure we have enough data points and valid bounds
    if(start < 1 || end <= start + 2) return -1;
    
    int troughIndex = -1;
    double troughValue = DBL_MAX;
    for(int i = MathMax(start, 1); i < end - 1; i++)
    {
        if(data[i] < data[i-1] && data[i] < data[i+1] && data[i] < troughValue)
        {
            troughValue = data[i];
            troughIndex = i;
        }
    }
    return troughIndex;
}

//+------------------------------------------------------------------+
//| Detects MACD/Price divergence and sets a global flag.            |
//+------------------------------------------------------------------+
void DetectDivergence()
{
    // --- Bearish Divergence Setup ---
    double highs[];
    CopyHigh(_Symbol, PERIOD_CURRENT, 0, 50, highs);
    int lastPricePeak = FindLastPeak(highs, 1, 10);
    if(lastPricePeak != -1)
    {
        int prevPricePeak = FindLastPeak(highs, lastPricePeak + 1, 50);
        if(prevPricePeak != -1)
        {
            int lastMacdPeak = FindLastPeak(macdMain, 1, 10);
            int prevMacdPeak = FindLastPeak(macdMain, lastMacdPeak + 1, 50);
            if(lastMacdPeak != -1 && prevMacdPeak != -1)
            {
                if(highs[lastPricePeak] > highs[prevPricePeak] && macdMain[lastMacdPeak] < macdMain[prevMacdPeak])
                {
                    Print("Divergence Setup: Bearish Divergence detected. Armed to look for SELL trigger.");
                    bearishDivergenceSetup = true;
                    bullishDivergenceSetup = false;
                }
            }
        }
    }

    // --- Bullish Divergence Setup ---
    double lows[];
    CopyLow(_Symbol, PERIOD_CURRENT, 0, 50, lows);
    int lastPriceTrough = FindLastTrough(lows, 1, 10);
    if(lastPriceTrough != -1)
    {
        int prevPriceTrough = FindLastTrough(lows, lastPriceTrough + 1, 50);
        if(prevPriceTrough != -1)
        {
            int lastMacdTrough = FindLastTrough(macdMain, 1, 10);
            int prevMacdTrough = FindLastTrough(macdMain, lastMacdTrough + 1, 50);
            if(lastMacdTrough != -1 && prevMacdTrough != -1)
            {
                if(lows[lastPriceTrough] < lows[prevPriceTrough] && macdMain[lastMacdTrough] > macdMain[prevMacdTrough])
                {
                    Print("Divergence Setup: Bullish Divergence detected. Armed to look for BUY trigger.");
                    bullishDivergenceSetup = true;
                    bearishDivergenceSetup = false;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Detects a "slingshot" trend continuation signal using crossover. |
//+------------------------------------------------------------------+
int DetectSlingshot()
{
    // Enhanced with RSI Momentum Filter
    bool isUptrend = macdMain[0] > 0;
    bool isCrossover = macdMain[0] > macdSignal[0] && macdMain[1] <= macdSignal[1];
    bool rsiConfirmsBuy = rsiBuffer[0] > 50;
    
    if(isUptrend && isCrossover && rsiConfirmsBuy)
    {
        Print("Slingshot Signal: BUY confirmed by RSI > 50 (", rsiBuffer[0], ")");
        return 1;
    }

    bool isDowntrend = macdMain[0] < 0;
    bool isCrossunder = macdMain[0] < macdSignal[0] && macdMain[1] >= macdSignal[1];
    bool rsiConfirmsSell = rsiBuffer[0] < 50;

    if(isDowntrend && isCrossunder && rsiConfirmsSell)
    {
        Print("Slingshot Signal: SELL confirmed by RSI < 50 (", rsiBuffer[0], ")");
        return -1;
    }

    return 0;
}

//+------------------------------------------------------------------+
//| Get trading signal (Final Engine with Confirmation Candle)       |
//+------------------------------------------------------------------+
int GetTradingSignal()
{
    currentStrategy = "";
    int signal = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);

    // --- Part 1: Check for a confirmed signal from the PREVIOUS bar ---
    if(pendingSignal != 0 && currentBarTime > signalBarTime)
    {
        MqlRates confirmationCandle[1];
        CopyRates(_Symbol, PERIOD_CURRENT, 1, 1, confirmationCandle);
        
        bool isBullishCandle = confirmationCandle[0].close > confirmationCandle[0].open;
        bool isBearishCandle = confirmationCandle[0].close < confirmationCandle[0].open;

        if(pendingSignal == 1 && isBullishCandle)
        {
            Print("GetTradingSignal: CONFIRMED BUY. Reason: Pending signal confirmed by bullish candle.");
            currentStrategy = pendingStrategy;
            signal = 1;
        }
        else if(pendingSignal == -1 && isBearishCandle)
        {
            Print("GetTradingSignal: CONFIRMED SELL. Reason: Pending signal confirmed by bearish candle.");
            currentStrategy = pendingStrategy;
            signal = -1;
        }
        else
        {
            Print("GetTradingSignal: PENDING SIGNAL CANCELED. Reason: Confirmation candle was not in the trade direction.");
        }
        
        // Reset pending signal regardless of outcome
        pendingSignal = 0;
        signalBarTime = 0;
        pendingStrategy = "";
        
        if(signal != 0) return signal;
    }

    // --- Part 2: Look for a NEW signal on the current bar ---
    // Reset any old pending signals that didn't get confirmed
    pendingSignal = 0; 
    
    // --- Enhanced Volatility Filter ---
    if(!IsVolatilityAcceptable()) return 0;
    
    // --- Safety and Risk Filters ---
    if(!IsSafeToTrade()) return 0;

    // --- Market Regime Filter (ADX) ---
    double adxValue = adxBuffer[0];

    if(adxValue >= InpADXTrend) // Trending Market
    {
        int slingshotSignal = DetectSlingshot();
        if(slingshotSignal != 0)
        {
            double emaValue = emaBuffer[0];
            double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
            if(slingshotSignal == 1 && currentPrice > emaValue)
            {
                pendingSignal = 1;
                pendingStrategy = "Slingshot";
            }
            else if(slingshotSignal == -1 && currentPrice < emaValue)
            {
                pendingSignal = -1;
                pendingStrategy = "Slingshot";
            }
        }
    }
    else // Ranging or Weak Trend Market
    {
        DetectDivergence(); // This just sets the flags
        if(bullishDivergenceSetup)
        {
            if(macdMain[0] > macdSignal[0] && macdMain[1] <= macdSignal[1])
            {
                pendingSignal = 1;
                pendingStrategy = "Divergence";
                bullishDivergenceSetup = false;
            }
        }
        else if(bearishDivergenceSetup)
        {
            if(macdMain[0] < macdSignal[0] && macdMain[1] >= macdSignal[1])
            {
                pendingSignal = -1;
                pendingStrategy = "Divergence";
                bearishDivergenceSetup = false;
            }
        }
    }

    if(pendingSignal != 0)
    {
        Print("GetTradingSignal: NEW SIGNAL FOUND (", pendingStrategy, " ", (pendingSignal == 1 ? "Buy" : "Sell"), "). Awaiting confirmation on next candle.");
        signalBarTime = currentBarTime;
    }

    return 0; // Always return 0 on the signal bar, wait for confirmation
}

//+------------------------------------------------------------------+
//| Calculate position size                                          |
//+------------------------------------------------------------------+
double CalculatePositionSize(double stopLossDistance)
{
    Print("=== ENHANCED POSITION SIZING CALCULATION ===");
    
    double balance = accountInfo.Balance();
    double equity = accountInfo.Equity();
    double baseAmount = MathMin(balance, equity);
    
    Print("Account Info: Balance $", balance, " | Equity $", equity, " | Base Amount $", baseAmount);
    Print("Stop Loss Distance: ", stopLossDistance / _Point / 10, " pips");
    
    // Apply adaptive risk multiplier
    double adaptiveRisk = CalculateAdaptiveRisk();
    double adjustedRiskPercent = InpMaxRiskPercent * adaptiveRisk;
    
    Print("🎯 BASE RISK CALCULATION:");
    Print("  Input Risk: ", InpMaxRiskPercent, "% × Adaptive Multiplier: ", adaptiveRisk, " = ", adjustedRiskPercent, "%");
    
    // Apply volatility adjustment
    double volatilityAdjustment = 1.0;
    if(InpUseVolatilityAdjustment)
    {
        Print("🔶 VOLATILITY ADJUSTMENT:");
        double currentATR = atrBuffer[0];
        double atrSum = 0.0;
        int atrPeriod = MathMin(InpATRPeriod, ArraySize(atrBuffer));
        for(int i = 1; i < atrPeriod; i++)
            atrSum += atrBuffer[i];
        double avgATR = atrSum / (atrPeriod - 1);
        
        Print("  Current ATR: ", currentATR / _Point / 10, " pips | Average ATR: ", avgATR / _Point / 10, " pips");
        
        if(avgATR > 0)
        {
            double volatilityRatio = currentATR / avgATR;
            Print("  Volatility Ratio: ", volatilityRatio);
            
            // Reduce position size in high volatility, increase in low volatility
            volatilityAdjustment = MathMax(0.5, MathMin(1.5, 1.0 / volatilityRatio));
            double beforeVolAdj = adjustedRiskPercent;
            adjustedRiskPercent *= volatilityAdjustment;
            
            Print("  Volatility Adjustment Factor: ", volatilityAdjustment);
            Print("  Risk Before Vol Adj: ", beforeVolAdj, "% → After: ", adjustedRiskPercent, "%");
            
            if(volatilityAdjustment < 1.0)
                Print("  ⚠️ HIGH VOLATILITY: Reducing position size for safety");
            else if(volatilityAdjustment > 1.0)
                Print("  📈 LOW VOLATILITY: Increasing position size for opportunity");
            else
                Print("  📊 NORMAL VOLATILITY: No adjustment needed");
        }
        else
        {
            Print("  ⚠️ WARNING: Average ATR is zero - skipping volatility adjustment");
        }
    }
    else
    {
        Print("🔶 VOLATILITY ADJUSTMENT: Disabled");
    }
    
    double riskAmount = baseAmount * adjustedRiskPercent / 100.0;
    Print("💰 RISK AMOUNT: $", baseAmount, " × ", adjustedRiskPercent, "% = $", riskAmount);
    
    double positionSize = riskAmount / (stopLossDistance * tickValue / tickSize);
    Print("📊 RAW CALCULATION: $", riskAmount, " ÷ (", stopLossDistance, " × ", tickValue, " ÷ ", tickSize, ") = ", positionSize, " lots");
    
    double beforeNormalization = positionSize;
    positionSize = MathFloor(positionSize / lotStep) * lotStep;
    Print("🔧 LOT NORMALIZATION: ", beforeNormalization, " → ", positionSize, " (step: ", lotStep, ")");
    
    double beforeBounds = positionSize;
    positionSize = MathMax(minLot, MathMin(positionSize, maxLot));
    if(positionSize != beforeBounds)
        Print("⚖️ BROKER LIMITS: ", beforeBounds, " → ", positionSize, " (range: ", minLot, " - ", maxLot, ")");
    
    // Enhanced small account protection (remove rigid 0.06 limit)
    if(balance <= 1500)
    {
        Print("🛡️ SMALL ACCOUNT PROTECTION (Balance ≤ $1500):");
        double maxLotForSmallAccount = balance * 0.004; // 0.4% of balance as max lot
        double beforeSmallAcctLimit = positionSize;
        positionSize = MathMin(positionSize, maxLotForSmallAccount);
        Print("  Max Lot for Small Account: ", maxLotForSmallAccount, " (0.4% of balance)");
        Print("  Before: ", beforeSmallAcctLimit, " → After: ", positionSize);
        
        if(positionSize < beforeSmallAcctLimit)
            Print("  ⚠️ Position size reduced for small account protection");
    }
    
    Print("🎯 FINAL POSITION SIZE: ", positionSize, " lots");
    Print("📈 FINAL RISK: $", riskAmount, " (", adjustedRiskPercent, "% of account)");
    Print("=== END POSITION SIZING CALCULATION ===");
    
    return positionSize;
}

//+------------------------------------------------------------------+
//| Execute trade                                                    |
//+------------------------------------------------------------------+
void ExecuteTrade(int signal, string strategyName)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    double entryPrice, stopLoss, takeProfit;
    double stopLossDistance;
    
    if(signal == 1) // Buy
    {
        entryPrice = ask;
        
        // Enhanced stop loss - BB + ATR safety net
        double bbStopLoss = bbLower[0] - (InpStopLossBuffer * _Point);
        double atrStopLoss = entryPrice - (atrBuffer[0] * 2.0);
        double maxStopDistance = entryPrice * 0.03; // 3% max stop distance
        
        // Use most conservative (closest to entry but not too close)
        stopLoss = MathMax(bbStopLoss, MathMax(atrStopLoss, entryPrice - maxStopDistance));
        stopLossDistance = entryPrice - stopLoss;
        takeProfit = entryPrice + (stopLossDistance * InpTakeProfitRatio);
    }
    else if(signal == -1) // Sell
    {
        entryPrice = bid;
        
        // Enhanced stop loss - BB + ATR safety net
        double bbStopLoss = bbUpper[0] + (InpStopLossBuffer * _Point);
        double atrStopLoss = entryPrice + (atrBuffer[0] * 2.0);
        double maxStopDistance = entryPrice * 0.03; // 3% max stop distance
        
        // Use most conservative (closest to entry but not too close)
        stopLoss = MathMin(bbStopLoss, MathMin(atrStopLoss, entryPrice + maxStopDistance));
        stopLossDistance = stopLoss - entryPrice;
        takeProfit = entryPrice - (stopLossDistance * InpTakeProfitRatio);
    }
    else return;
    
    // Calculate preliminary position size
    double lotSize = CalculatePositionSize(stopLossDistance);
    
    // Check portfolio risk limits
    double tradeRisk = (stopLossDistance * lotSize * tickValue / tickSize / accountInfo.Balance()) * 100.0;
    if(!IsPortfolioRiskAcceptable(_Symbol, tradeRisk))
    {
        Print("Trade rejected: Portfolio risk limits exceeded");
        return;
    }
    
    stopLoss = NormalizeDouble(stopLoss, _Digits);
    takeProfit = NormalizeDouble(takeProfit, _Digits);
    
    double minStopDistance = 10 * _Point;
    if(stopLossDistance < minStopDistance)
    {
        Print("ExecuteTrade: Stop loss too close. Minimum distance: ", minStopDistance / _Point, " pips");
        return;
    }
    
    string comment = strategyName + "_" + (signal == 1 ? "Buy" : "Sell");
    
    bool result = false;
    if(signal == 1)
        result = trade.Buy(lotSize, _Symbol, entryPrice, stopLoss, takeProfit, comment);
    else
        result = trade.Sell(lotSize, _Symbol, entryPrice, stopLoss, takeProfit, comment);
    
    if(result)
    {
        lastTradeTime = TimeCurrent();
        string alertMsg = StringFormat("%s order executed: %.2f lots at %.5f, SL: %.5f, TP: %.5f",
                         (signal == 1 ? "BUY" : "SELL"), lotSize, entryPrice, stopLoss, takeProfit);
        SendAlert(alertMsg, ALERT_INFO);
        
        Print("Trade executed successfully: ", alertMsg);
    }
    else
    {
        Print("Trade execution failed. Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Checks for and executes dynamic trade exits.                     |
//+------------------------------------------------------------------+
void CheckForDynamicExit()
{
    string positionStrategy = StringSubstr(positionInfo.Comment(), 0, StringFind(positionInfo.Comment(), "_"));
    
    // Exit 1: If it was a Slingshot trade and the trend has ended
    if(positionStrategy == "Slingshot" && adxBuffer[0] < InpADXTrend)
    {
        Print("Dynamic Exit: Closing Slingshot trade. Reason: Trend has ended (ADX dropped below ", InpADXTrend, ").");
        trade.PositionClose(positionInfo.Ticket());
        return;
    }
    
    // Exit 2: If a divergence forms against our open position
    DetectDivergence(); // This will set the global flags
    if(positionInfo.PositionType() == POSITION_TYPE_BUY && bearishDivergenceSetup)
    {
        Print("Dynamic Exit: Closing BUY trade. Reason: Bearish Divergence has formed.");
        trade.PositionClose(positionInfo.Ticket());
        return;
    }
    if(positionInfo.PositionType() == POSITION_TYPE_SELL && bullishDivergenceSetup)
    {
        Print("Dynamic Exit: Closing SELL trade. Reason: Bullish Divergence has formed.");
        trade.PositionClose(positionInfo.Ticket());
        return;
    }
}

//+------------------------------------------------------------------+
//| Manage open positions                                            |
//+------------------------------------------------------------------+
void ManageOpenPositions()
{
    if(!positionInfo.Select(_Symbol))
        return;
        
    // First, check for dynamic exit conditions
    CheckForDynamicExit();
    
    // If position was closed by dynamic exit, don't continue
    if(!positionInfo.Select(_Symbol))
        return;
    
    double currentPrice = (positionInfo.PositionType() == POSITION_TYPE_BUY) ? 
                         SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    double openPrice = positionInfo.PriceOpen();
    double currentSL = positionInfo.StopLoss();
    
    double initialRisk = MathAbs(openPrice - currentSL);
    if(initialRisk == 0) return;
    
    double currentProfit = (positionInfo.PositionType() == POSITION_TYPE_BUY) ? 
                          currentPrice - openPrice : openPrice - currentPrice;
    double rMultiple = currentProfit / initialRisk;
    
    if(rMultiple >= InpBreakEvenLevel && !IsAtBreakEven())
    {
        MoveToBreakEven();
    }
    
    if(rMultiple > 1.0)
    {
        ApplyTrailingStop();
    }
}

//+------------------------------------------------------------------+
//| Check if position is at break-even                              |
//+------------------------------------------------------------------+
bool IsAtBreakEven()
{
    if(!positionInfo.Select(_Symbol))
        return false;
    
    double openPrice = positionInfo.PriceOpen();
    double currentSL = positionInfo.StopLoss();
    double spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * _Point;
    
    if(positionInfo.PositionType() == POSITION_TYPE_BUY)
        return (currentSL >= openPrice + spread);
    else
        return (currentSL <= openPrice - spread);
}

//+------------------------------------------------------------------+
//| Move position to break-even                                     |
//+------------------------------------------------------------------+
void MoveToBreakEven()
{
    if(!positionInfo.Select(_Symbol))
        return;
    
    double openPrice = positionInfo.PriceOpen();
    double spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * _Point;
    double newSL;
    
    if(positionInfo.PositionType() == POSITION_TYPE_BUY)
        newSL = openPrice + spread;
    else
        newSL = openPrice - spread;
    
    newSL = NormalizeDouble(newSL, _Digits);
    
    if(trade.PositionModify(_Symbol, newSL, positionInfo.TakeProfit()))
    {
        SendAlert("Position moved to break-even at " + DoubleToString(newSL, _Digits), ALERT_INFO);
    }
}

//+------------------------------------------------------------------+
//| Apply trailing stop                                             |
//+------------------------------------------------------------------+
void ApplyTrailingStop()
{
    if(!positionInfo.Select(_Symbol))
        return;
    
    double currentPrice = (positionInfo.PositionType() == POSITION_TYPE_BUY) ? 
                         SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    double trailingDistance = InpTrailingStop * _Point;
    double newSL = positionInfo.StopLoss();
    
    if(positionInfo.PositionType() == POSITION_TYPE_BUY)
    {
        double proposedSL = currentPrice - trailingDistance;
        if(proposedSL > positionInfo.StopLoss())
            newSL = proposedSL;
    }
    else
    {
        double proposedSL = currentPrice + trailingDistance;
        if(proposedSL < positionInfo.StopLoss())
            newSL = proposedSL;
    }
    
    newSL = NormalizeDouble(newSL, _Digits);
    
    if(newSL != positionInfo.StopLoss())
    {
        if(trade.PositionModify(_Symbol, newSL, positionInfo.TakeProfit()))
        {
            Print("Trailing stop updated to: ", newSL);
        }
    }
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(positionInfo.SelectByIndex(i) && positionInfo.Symbol() == _Symbol)
        {
            trade.PositionClose(positionInfo.Ticket());
        }
    }
}

//+------------------------------------------------------------------+
//| Update time-based tracking                                      |
//+------------------------------------------------------------------+
void UpdateTimeBasedTracking()
{
    MqlDateTime dt;
    TimeToStruct(TimeGMT(), dt);
    
    if(dt.hour == 0 && TimeCurrent() >= dailyResetTime + 3600)
    {
        ResetDailyTracking();
    }
    
    if(dt.day_of_week == 1 && dt.hour == 0 && TimeCurrent() >= weeklyResetTime + 86400)
    {
        ResetWeeklyTracking();
    }
    
    if(dt.day == 1 && dt.hour == 0 && TimeCurrent() >= monthlyResetTime + 86400)
    {
        ResetMonthlyTracking();
    }
}

//+------------------------------------------------------------------+
//| Reset daily tracking                                            |
//+------------------------------------------------------------------+
void ResetDailyTracking()
{
    dailyStartBalance = accountInfo.Equity();
    dailyLimitReached = false;
    dailyResetTime = TimeCurrent();
    Print("Daily tracking reset. Starting balance: $", dailyStartBalance);
}

//+------------------------------------------------------------------+
//| Reset weekly tracking                                            |
//+------------------------------------------------------------------+
void ResetWeeklyTracking()
{
    weeklyStartBalance = accountInfo.Equity();
    weeklyLimitReached = false;
    weeklyResetTime = TimeCurrent();
    Print("Weekly tracking reset. Starting balance: $", weeklyStartBalance);
}

//+------------------------------------------------------------------+
//| Reset monthly tracking                                          |
//+------------------------------------------------------------------+
void ResetMonthlyTracking()
{
    monthlyStartBalance = accountInfo.Equity();
    monthlyLimitReached = false;
    monthlyResetTime = TimeCurrent();
    Print("Monthly tracking reset. Starting balance: $", monthlyStartBalance);
}

//+------------------------------------------------------------------+
//| Send alert notification                                         |
//+------------------------------------------------------------------+
void SendAlert(string message, int alertType)
{
    string fullMessage = TimeToString(TimeCurrent()) + " - " + _Symbol + ": " + message;
    
    Print(fullMessage);
    
    if(InpEnableAlerts)
    {
        Alert(fullMessage);
    }
    
    if(InpEnableEmail && alertType >= ALERT_WARNING)
    {
        SendMail("Ultra Conservative EA Alert", fullMessage);
    }
    
    if(InpEnablePush && alertType >= ALERT_WARNING)
    {
        SendNotification(fullMessage);
    }
}

//+------------------------------------------------------------------+
//| Enhanced Position Management Functions                           |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Trade transaction event handler                                  |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result)
{
    if(trans.type != TRADE_TRANSACTION_DEAL_ADD) return;
    if(!HistoryDealSelect(trans.deal)) return;
    if(HistoryDealGetInteger(trans.deal, DEAL_MAGIC) != magicNumber) return;

    long dealEntry = HistoryDealGetInteger(trans.deal, DEAL_ENTRY);
    double dealProfit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);

    if(dealEntry == DEAL_ENTRY_OUT) // Position closed
    {
        UpdatePerformanceTracking(dealProfit);
        Print("Position closed with profit: ", dealProfit, 
              " | Consecutive wins: ", consecutiveWins, 
              " | Consecutive losses: ", consecutiveLosses);
    }
}

//+------------------------------------------------------------------+
//| Update performance tracking for adaptive risk                    |
//+------------------------------------------------------------------+
void UpdatePerformanceTracking(double profit)
{
    if(profit > 0)
    {
        consecutiveWins++;
        consecutiveLosses = 0;
    }
    else
    {
        consecutiveLosses++;
        consecutiveWins = 0;
    }
    
    // Log adaptive risk changes
    if(InpUseAdaptiveRisk)
    {
        double newRiskMultiplier = CalculateAdaptiveRisk();
        if(MathAbs(newRiskMultiplier - currentRiskMultiplier) > 0.1)
        {
            Print("Adaptive Risk Update: Risk multiplier changed from ", currentRiskMultiplier, 
                  " to ", newRiskMultiplier, " (Wins: ", consecutiveWins, ", Losses: ", consecutiveLosses, ")");
        }
    }
}

//+------------------------------------------------------------------+
//| Enhanced position management with partial TP and ATR trailing   |
//+------------------------------------------------------------------+
void EnhancedManageOpenPositions()
{
    if(!positionInfo.Select(_Symbol)) return;
    
    Print("=== ENHANCED POSITION MANAGEMENT ===");
    Print("Position Symbol: ", positionInfo.Symbol(), " | Ticket: ", positionInfo.Ticket());
    Print("Position Type: ", (positionInfo.PositionType() == POSITION_TYPE_BUY ? "BUY" : "SELL"));
    Print("Position Volume: ", positionInfo.Volume(), " lots");
    
    double currentPrice = (positionInfo.PositionType() == POSITION_TYPE_BUY) ? 
                         SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    double openPrice = positionInfo.PriceOpen();
    double currentSL = positionInfo.StopLoss();
    double originalSL = currentSL; // Store original for R calculations
    
    Print("Price Levels: Open ", openPrice, " | Current ", currentPrice, " | Stop Loss ", currentSL);
    
    // Calculate risk and profit
    double initialRisk = MathAbs(openPrice - originalSL);
    if(initialRisk == 0) 
    {
        Print("⚠️ WARNING: Initial risk is zero - skipping enhanced management");
        Print("=== END ENHANCED POSITION MANAGEMENT ===");
        return;
    }
    
    double currentProfit = (positionInfo.PositionType() == POSITION_TYPE_BUY) ? 
                          currentPrice - openPrice : openPrice - currentPrice;
    double rMultiple = currentProfit / initialRisk;
    
    Print("Performance Metrics:");
    Print("  Initial Risk: ", initialRisk / _Point / 10, " pips");
    Print("  Current Profit: ", currentProfit / _Point / 10, " pips");
    Print("  R-Multiple: ", rMultiple, "R");
    
    string comment = positionInfo.Comment();
    Print("Position Comment: ", comment);
    
    // Partial Take Profit Management
    if(InpUsePartialTP)
    {
        Print("🎯 PARTIAL TAKE PROFIT ANALYSIS:");
        
        // First partial TP
        bool partial1Executed = (StringFind(comment, "Partial1") != -1);
        Print("  First Partial TP: Target ", InpPartialTP1_RR, "R (", InpPartialTP1_Percent, "%) | Current ", rMultiple, "R | Executed: ", (partial1Executed ? "Yes" : "No"));
        
        if(rMultiple >= InpPartialTP1_RR && !partial1Executed)
        {
            Print("  ✅ EXECUTING FIRST PARTIAL TP: ", InpPartialTP1_Percent, "% at ", rMultiple, "R");
            ExecutePartialClose(InpPartialTP1_Percent, "Partial1");
        }
        
        // Second partial TP
        bool partial2Executed = (StringFind(comment, "Partial2") != -1);
        Print("  Second Partial TP: Target ", InpPartialTP2_RR, "R (", InpPartialTP2_Percent, "%) | Current ", rMultiple, "R | Executed: ", (partial2Executed ? "Yes" : "No"));
        
        if(rMultiple >= InpPartialTP2_RR && !partial2Executed)
        {
            Print("  ✅ EXECUTING SECOND PARTIAL TP: ", InpPartialTP2_Percent, "% at ", rMultiple, "R");
            ExecutePartialClose(InpPartialTP2_Percent, "Partial2");
        }
    }
    else
    {
        Print("🎯 PARTIAL TAKE PROFIT: Disabled");
    }
    
    // Enhanced Breakeven (earlier trigger)
    double breakevenTrigger = InpBreakEvenLevel * 0.8; // Move to BE earlier
    bool isAtBreakeven = IsAtBreakEven();
    Print("🛡️ BREAKEVEN ANALYSIS:");
    Print("  Enhanced Trigger: ", breakevenTrigger, "R (80% of ", InpBreakEvenLevel, "R)");
    Print("  Current Position: ", rMultiple, "R | At Breakeven: ", (isAtBreakeven ? "Yes" : "No"));
    
    if(rMultiple >= breakevenTrigger && !isAtBreakeven)
    {
        Print("  ✅ MOVING TO BREAKEVEN: Position reached ", rMultiple, "R ≥ ", breakevenTrigger, "R trigger");
        MoveToBreakEven();
    }
    else if(rMultiple < breakevenTrigger)
    {
        Print("  ⏳ WAITING FOR BREAKEVEN: Need ", breakevenTrigger - rMultiple, "R more");
    }
    
    // ATR-based Trailing Stop
    Print("📈 ATR TRAILING STOP ANALYSIS:");
    Print("  ATR Trailing Enabled: ", (InpUseATRTrailing ? "Yes" : "No"));
    Print("  R-Multiple: ", rMultiple, "R (Need >1.0R to activate)");
    
    if(InpUseATRTrailing && rMultiple > 1.0)
    {
        Print("  ✅ APPLYING ATR TRAILING: Position is ", rMultiple, "R in profit");
        ApplyATRTrailingStop(currentPrice, positionInfo.PositionType());
    }
    else if(InpUseATRTrailing)
    {
        Print("  ⏳ ATR TRAILING WAITING: Need position to reach 1.0R first");
    }
    else
    {
        Print("  🔶 ATR TRAILING: Disabled");
    }
    
    Print("=== END ENHANCED POSITION MANAGEMENT ===");
}

//+------------------------------------------------------------------+
//| Execute partial position close                                   |
//+------------------------------------------------------------------+
void ExecutePartialClose(double percentage, string partialType)
{
    Print("=== EXECUTING PARTIAL CLOSE ===");
    
    double currentVolume = positionInfo.Volume();
    double closeVolume = NormalizeDouble(currentVolume * percentage / 100.0, 2);
    double minVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    
    Print("Partial Close Details:");
    Print("  Target Percentage: ", percentage, "%");
    Print("  Current Volume: ", currentVolume, " lots");
    Print("  Close Volume: ", closeVolume, " lots");
    Print("  Minimum Volume: ", minVolume, " lots");
    Print("  Partial Type: ", partialType);
    
    if(closeVolume >= minVolume)
    {
        Print("✅ Volume check passed - executing partial close...");
        
        if(trade.PositionClosePartial(positionInfo.Ticket(), closeVolume))
        {
            string newComment = positionInfo.Comment() + "_" + partialType;
            Print("🎯 PARTIAL CLOSE SUCCESS!");
            Print("  Closed: ", percentage, "% (", closeVolume, " lots)");
            Print("  Remaining: ", currentVolume - closeVolume, " lots");
            Print("  Updated Comment: ", newComment);
            Print("  Profit Realized: $", positionInfo.Profit());
        }
        else
        {
            Print("❌ PARTIAL CLOSE FAILED!");
            Print("  Error Code: ", trade.ResultRetcode());
            Print("  Error Description: ", trade.ResultRetcodeDescription());
        }
    }
    else
    {
        Print("❌ PARTIAL CLOSE REJECTED: Volume too small");
        Print("  Required: ≥", minVolume, " lots | Calculated: ", closeVolume, " lots");
        Print("  Suggestion: Increase position size or reduce partial percentage");
    }
    
    Print("=== END PARTIAL CLOSE EXECUTION ===");
}

//+------------------------------------------------------------------+
//| Apply ATR-based trailing stop                                    |
//+------------------------------------------------------------------+
void ApplyATRTrailingStop(double currentPrice, ENUM_POSITION_TYPE posType)
{
    Print("=== ATR TRAILING STOP APPLICATION ===");
    
    double currentATR = atrBuffer[0];
    double trailDistance = currentATR * InpATRTrailingMultiplier;
    double currentSL = positionInfo.StopLoss();
    double currentTP = positionInfo.TakeProfit();
    
    Print("ATR Trailing Parameters:");
    Print("  Position Type: ", (posType == POSITION_TYPE_BUY ? "BUY" : "SELL"));
    Print("  Current Price: ", currentPrice);
    Print("  Current ATR: ", currentATR / _Point / 10, " pips");
    Print("  ATR Multiplier: ", InpATRTrailingMultiplier);
    Print("  Trail Distance: ", trailDistance / _Point / 10, " pips");
    Print("  Current Stop Loss: ", currentSL);
    
    double newSL = 0;
    bool shouldUpdate = false;
    string reason = "";
    
    if(posType == POSITION_TYPE_BUY)
    {
        newSL = currentPrice - trailDistance;
        shouldUpdate = (newSL > currentSL);
        reason = shouldUpdate ? "New SL higher than current" : "New SL not higher than current";
        
        Print("BUY Position Analysis:");
        Print("  Calculated New SL: ", newSL, " (Current Price - ATR Distance)");
        Print("  Current SL: ", currentSL);
        Print("  Should Update: ", (shouldUpdate ? "Yes" : "No"), " (", reason, ")");
    }
    else // SELL
    {
        newSL = currentPrice + trailDistance;
        shouldUpdate = (newSL < currentSL);
        reason = shouldUpdate ? "New SL lower than current" : "New SL not lower than current";
        
        Print("SELL Position Analysis:");
        Print("  Calculated New SL: ", newSL, " (Current Price + ATR Distance)");
        Print("  Current SL: ", currentSL);
        Print("  Should Update: ", (shouldUpdate ? "Yes" : "No"), " (", reason, ")");
    }
    
    if(shouldUpdate)
    {
        double beforeNormalization = newSL;
        newSL = NormalizeDouble(newSL, _Digits);
        
        Print("✅ UPDATING TRAILING STOP:");
        Print("  Before Normalization: ", beforeNormalization);
        Print("  After Normalization: ", newSL);
        Print("  Trail Movement: ", MathAbs(newSL - currentSL) / _Point / 10, " pips");
        
        if(trade.PositionModify(positionInfo.Ticket(), newSL, currentTP))
        {
            Print("🎯 ATR TRAILING STOP UPDATED SUCCESSFULLY!");
            Print("  New Stop Loss: ", newSL);
            Print("  ATR Distance: ", trailDistance / _Point / 10, " pips");
            Print("  Protection Level: ", MathAbs(currentPrice - newSL) / _Point / 10, " pips from current price");
        }
        else
        {
            Print("❌ ATR TRAILING STOP UPDATE FAILED!");
            Print("  Error Code: ", trade.ResultRetcode());
            Print("  Error Description: ", trade.ResultRetcodeDescription());
            Print("  Attempted SL: ", newSL);
            Print("  Current TP: ", currentTP);
        }
    }
    else
    {
        Print("⏳ NO ATR TRAILING UPDATE: ", reason);
        Print("  Current protection adequate at ", MathAbs(currentPrice - currentSL) / _Point / 10, " pips");
    }
    
    Print("=== END ATR TRAILING STOP APPLICATION ===");
}
