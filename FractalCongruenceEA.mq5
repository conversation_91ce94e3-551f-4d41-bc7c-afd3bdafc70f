//+------------------------------------------------------------------+
//|                                          FractalCongruenceEA.mq5 |
//|                                    Copyright 2025, Your Name     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      ""
#property version   "1.10"
#property description "A robust EA based on Multi-Timeframe Fractal Congruence. Corrected version."

#include <Trade\Trade.mqh>
CTrade trade;

//--- EA Settings
input string          sEASettings         = "--- EA Settings ---";
input long            MagicNumber         = 12345; // Magic number for this EA

//--- Signal Indicator Settings
input string          sSignalSettings     = "--- Signal Settings ---";
input ENUM_TIMEFRAMES ConfirmationTF1       = PERIOD_H1;    // Confirmation Timeframe 1
input ENUM_TIMEFRAMES ConfirmationTF2       = PERIOD_H4;    // Confirmation Timeframe 2

//--- Position Sizing & Risk Management
input string          sRiskManagement     = "--- Risk Management ---";
input double          RiskPercent         = 1.0;              // Risk per trade as % of account balance
input int             MaxConcurrentTrades = 2;        // Maximum number of open trades

//--- Stop-Loss & Take-Profit
input string          sSLTPSettings       = "--- SL & TP Settings ---";
input double          StopLossATR_Multiplier = 2.0;   // Stop Loss based on ATR multiplier
input int             ATR_Period          = 14;                // ATR Period for SL calculation
input double          RiskRewardRatio     = 2.0;          // Risk to Reward Ratio for initial TP

//--- Break-Even Settings
input string          sBreakEvenSettings  = "--- Break-Even Settings ---";
input bool            UseBreakEven        = true;            // Use Break-Even
input int             BreakEvenPips       = 20;             // Pips in profit to trigger break-even

//--- Trailing Stop Settings
input string          sTrailingStopSettings= "--- Trailing Stop Settings ---";
input bool            UseTrailingStop     = true;         // Use Trailing Stop
input int             TrailingStopPips    = 15;          // Trailing stop distance in pips

//--- Daily Drawdown & Profit Target
input string          sDailyLockSettings  = "--- Daily Lock Settings ---";
input bool            UseDailyDrawdownLock= true;    // Use Daily Drawdown Lock
input double          MaxDailyDrawdownPercent = 3.0;  // Max daily drawdown in %
input bool            UseDailyProfitLock  = true;      // Use Daily Profit Lock
input double          MaxDailyProfitPercent = 4.0;    // Daily profit target in %

//--- Time-Based Exit
input string          sTimeBasedExit      = "--- Time-Based Exit ---";
input bool            EnableFridayClose   = true;       // Close all trades on Friday
input string          FridayCloseTime     = "20:00";      // Time to close trades on Friday

//--- News Filter
input string          sNewsFilter         = "--- News Filter ---";
input bool            UseNewsFilter       = true;           // Enable News Filter
input int             MinutesBeforeNews   = 30;         // Minutes to stop trading before news
input int             MinutesAfterNews    = 30;          // Minutes to resume trading after news

//--- Global variables
bool   isTradingAllowed     = true;
double dailyStartingBalance = 0;
int    atr_handle           = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetMarginMode();
   dailyStartingBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   //--- Initialize ATR indicator handle
   atr_handle = iATR(_Symbol, _Period, ATR_Period);
   if(atr_handle == INVALID_HANDLE)
     {
      Print("Error creating ATR indicator handle - error: ", GetLastError());
      return(INIT_FAILED);
     }
     
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   //--- Release indicator handle
   if(atr_handle != INVALID_HANDLE)
      IndicatorRelease(atr_handle);
  }
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
   //--- Master risk controls
   CheckDailyLocks();
   if(!isTradingAllowed) return;

   //--- All trade management logic that needs to run on every tick
   ManageOpenTrades();
  }
//+------------------------------------------------------------------+
//| New Bar function                                                 |
//+------------------------------------------------------------------+
void OnNewBar()
{
    //--- Check for new signals only once per bar
    if(IsNewBar())
    {
        if(!isTradingAllowed || IsNewsTime()) return;
        
        //--- Check for Friday close on the new bar
        CheckFridayClose();
        if(!isTradingAllowed) return; // Re-check in case Friday close was triggered

        string signal = GetFractalSignal();
        if(signal != "")
        {
            Print("Fractal Signal Found: ", signal);
            if(PositionsTotal() < MaxConcurrentTrades)
            {
                ExecuteTrade(signal);
            }
            else
            {
                Print("Signal ignored: Max concurrent trades limit reached (", PositionsTotal(), "/", MaxConcurrentTrades, ").");
            }
        }
    }
}
//+------------------------------------------------------------------+
//| Check for new bar                                                |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, _Period, 0);

    if(lastBarTime != currentBarTime)
    {
        lastBarTime = currentBarTime;
        Print("New Bar Detected at ", TimeToString(currentBarTime));
        return true;
    }
    return false;
}
//+------------------------------------------------------------------+
//| Signal Generation Functions                                      |
//+------------------------------------------------------------------+
string GetFractalSignal()
{
    //--- Get current chart data
    double high[], low[];
    datetime time[];
    if(CopyHigh(_Symbol, _Period, 1, 5, high) < 5 ||
       CopyLow(_Symbol, _Period, 1, 5, low) < 5 ||
       CopyTime(_Symbol, _Period, 1, 5, time) < 5)
       {
        return "";
       }

    //--- Bullish Signal Check
    if(IsBullishFractal(3, high, low))
    {
        Print(TimeToString(time[3]), ": M15 Bullish Fractal found.");
        bool tf1_ok = CheckHigherTimeframeFractal(ConfirmationTF1, true, time[3]);
        Print("   - H1 Congruence Check: ", tf1_ok ? "OK" : "FAIL");
        if(tf1_ok)
        {
            bool tf2_ok = CheckHigherTimeframeFractal(ConfirmationTF2, true, time[3]);
            Print("     - H4 Congruence Check: ", tf2_ok ? "OK" : "FAIL");
            if(tf2_ok)
            {
                return "BUY";
            }
        }
    }

    //--- Bearish Signal Check
    if(IsBearishFractal(3, high, low))
    {
        Print(TimeToString(time[3]), ": M15 Bearish Fractal found.");
        bool tf1_ok = CheckHigherTimeframeFractal(ConfirmationTF1, false, time[3]);
        Print("   - H1 Congruence Check: ", tf1_ok ? "OK" : "FAIL");
        if(tf1_ok)
        {
            bool tf2_ok = CheckHigherTimeframeFractal(ConfirmationTF2, false, time[3]);
            Print("     - H4 Congruence Check: ", tf2_ok ? "OK" : "FAIL");
            if(tf2_ok)
            {
                return "SELL";
            }
        }
    }

    return "";
}
//+------------------------------------------------------------------+
bool IsBullishFractal(int index, const double &high[], const double &low[])
{
    // A bullish fractal is a series of 5 bars where the middle bar has the lowest low
    if(low[index-2] < low[index-4] &&
       low[index-2] < low[index-3] &&
       low[index-2] < low[index-1] &&
       low[index-2] < low[index])
    {
        return(true);
    }
    return(false);
}
//+------------------------------------------------------------------+
bool IsBearishFractal(int index, const double &high[], const double &low[])
{
    // A bearish fractal is a series of 5 bars where the middle bar has the highest high
    if(high[index-2] > high[index-4] &&
       high[index-2] > high[index-3] &&
       high[index-2] > high[index-1] &&
       high[index-2] > high[index])
    {
        return(true);
    }
    return(false);
}
//+------------------------------------------------------------------+
bool CheckHigherTimeframeFractal(ENUM_TIMEFRAMES tf, bool isBullish, datetime barTime)
{
    // We need enough bars to find a fractal, copy a few extra for safety
    int bars_to_copy = 15;
    double ht_high[], ht_low[];
    datetime ht_time[];

    if(CopyHigh(_Symbol, tf, 0, bars_to_copy, ht_high) < bars_to_copy ||
       CopyLow(_Symbol, tf, 0, bars_to_copy, ht_low) < bars_to_copy ||
       CopyTime(_Symbol, tf, 0, bars_to_copy, ht_time) < bars_to_copy)
    {
        Print("Error copying higher timeframe data for ", EnumToString(tf));
        return false;
    }
    
    // Find the corresponding bar on the higher timeframe that contains the lower timeframe bar
    int ht_bar_index = -1;
    for(int i = 0; i < bars_to_copy; i++)
    {
        if(ht_time[i] <= barTime && (i == 0 || ht_time[i-1] > barTime))
        {
            ht_bar_index = i;
            break;
        }
    }

    if(ht_bar_index == -1)
        return false;

    // Now, check for a fractal in the recent past on the higher timeframe
    for(int i = ht_bar_index; i < ht_bar_index + 5 && i < bars_to_copy; i++)
    {
        if(i < 4) continue; // Not enough bars to form a fractal before this point

        if(isBullish)
        {
            if(IsBullishFractal(i, ht_high, ht_low)) return true;
        }
        else
        {
            if(IsBearishFractal(i, ht_high, ht_low)) return true;
        }
    }

    return false;
}
//+------------------------------------------------------------------+
//| Trade Execution Functions                                        |
//+------------------------------------------------------------------+
void ExecuteTrade(string signalType)
{
    Print("Attempting to execute ", signalType, " trade.");
    double price = SymbolInfoDouble(_Symbol, signalType == "BUY" ? SYMBOL_ASK : SYMBOL_BID);
    
    double sl = CalculateStopLoss(signalType);
    if(sl == 0)
    {
        Print("   - Trade Aborted: Stop loss calculation failed.");
        return;
    }
    Print("   - Calculated Stop Loss: ", sl);
    
    double tp = CalculateTakeProfit(signalType, price, sl);
    Print("   - Calculated Take Profit: ", tp);

    double lots = CalculateLotSize(sl);
    Print("   - Calculated Lot Size: ", lots);

    if(lots > 0)
    {
        Print("   - Executing ", signalType, " trade with ", lots, " lots.");
        if(signalType == "BUY")
        {
            trade.Buy(lots, _Symbol, price, sl, tp, "Buy triggered by Fractal EA");
        }
        else if(signalType == "SELL")
        {
            trade.Sell(lots, _Symbol, price, sl, tp, "Sell triggered by Fractal EA");
        }
    }
    else
    {
        Print("   - Trade Aborted: Lot size is zero. This is a safety feature to protect small accounts. Check ATR volatility vs. Risk %.");
    }
}
//+------------------------------------------------------------------+
double CalculateStopLoss(string signalType)
{
    double atr_buffer[];
    if(CopyBuffer(atr_handle, 0, 1, 1, atr_buffer) <= 0)
    {
        Print("Error copying ATR buffer");
        return 0;
    }
    double atr_value = atr_buffer[0];

    if(signalType == "BUY")
    {
        return SymbolInfoDouble(_Symbol, SYMBOL_BID) - (atr_value * StopLossATR_Multiplier);
    }
    else // SELL
    {
        return SymbolInfoDouble(_Symbol, SYMBOL_ASK) + (atr_value * StopLossATR_Multiplier);
    }
}
//+------------------------------------------------------------------+
double CalculateTakeProfit(string signalType, double entry_price, double stop_loss)
{
    double sl_distance = MathAbs(entry_price - stop_loss);
    if(signalType == "BUY")
    {
        return entry_price + (sl_distance * RiskRewardRatio);
    }
    else // SELL
    {
        return entry_price - (sl_distance * RiskRewardRatio);
    }
}
//+------------------------------------------------------------------+
double CalculateLotSize(double stop_loss)
{
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * (RiskPercent / 100.0);
    
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl_distance = MathAbs(entry_price - stop_loss);
    if(sl_distance == 0) return 0;

    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double value_per_point = tick_value / tick_size;
    double lots = risk_amount / (sl_distance * value_per_point);

    //--- Normalize and check against min/max lot sizes
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lots = MathFloor(lots / lot_step) * lot_step;

    if(lots < min_lot) lots = 0; // If calculated lot is too small, don't trade
    if(lots > max_lot) lots = max_lot;

    return lots;
}
//+------------------------------------------------------------------+
//| Trade Management Functions                                       |
//+------------------------------------------------------------------+
void ManageOpenTrades()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket) && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
        {
            if(UseBreakEven) ManageBreakEven(ticket);
            if(UseTrailingStop) ManageTrailingStop(ticket);
            ManageDynamicTP(ticket);
        }
    }
}
//+------------------------------------------------------------------+
void ManageBreakEven(ulong ticket)
{
    double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
    double sl = PositionGetDouble(POSITION_SL);
    long type = PositionGetInteger(POSITION_TYPE);

    if(sl == entry_price) return; // Already at break-even

    double pips_in_profit = 0;
    if(type == POSITION_TYPE_BUY)
    {
        pips_in_profit = (current_price - entry_price) / (_Point * 10);
    }
    else // SELL
    {
        pips_in_profit = (entry_price - current_price) / (_Point * 10);
    }

    if(pips_in_profit >= BreakEvenPips)
    {
        trade.PositionModify(ticket, entry_price, PositionGetDouble(POSITION_TP));
    }
}
//+------------------------------------------------------------------+
void ManageTrailingStop(ulong ticket)
{
    double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
    double sl = PositionGetDouble(POSITION_SL);
    double tp = PositionGetDouble(POSITION_TP);
    long type = PositionGetInteger(POSITION_TYPE);

    double new_sl = 0;
    double trailing_stop_dist = TrailingStopPips * _Point * 10;

    if(type == POSITION_TYPE_BUY)
    {
        new_sl = current_price - trailing_stop_dist;
        if(new_sl > sl && new_sl > entry_price)
        {
            trade.PositionModify(ticket, new_sl, tp);
        }
    }
    else // SELL
    {
        new_sl = current_price + trailing_stop_dist;
        if(new_sl < sl && new_sl < entry_price)
        {
            trade.PositionModify(ticket, new_sl, tp);
        }
    }
}
//+------------------------------------------------------------------+
void ManageDynamicTP(ulong ticket)
{
    long type = PositionGetInteger(POSITION_TYPE);
    string signal = GetFractalSignal();

    if(type == POSITION_TYPE_BUY && signal == "SELL")
    {
        trade.PositionClose(ticket);
    }
    else if(type == POSITION_TYPE_SELL && signal == "BUY")
    {
        trade.PositionClose(ticket);
    }
}
//+------------------------------------------------------------------+
//| Master Risk Control Functions                                    |
//+------------------------------------------------------------------+
void CheckDailyLocks()
{
    if(!UseDailyDrawdownLock && !UseDailyProfitLock) return;

    //--- Reset daily lock at the start of a new day
    static datetime today = 0;
    datetime current_day = iTime(_Symbol, PERIOD_D1, 0);
    if(today != current_day)
    {
        today = current_day;
        isTradingAllowed = true;
        dailyStartingBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    }

    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double daily_pnl = equity - dailyStartingBalance;
    double daily_drawdown = dailyStartingBalance * (MaxDailyDrawdownPercent / 100.0);
    double daily_profit = dailyStartingBalance * (MaxDailyProfitPercent / 100.0);

    if(isTradingAllowed && UseDailyDrawdownLock && daily_pnl < -daily_drawdown)
    {
        isTradingAllowed = false;
        Print("Max daily drawdown reached. Closing all trades and disabling trading for today.");
        //--- Close all positions
        for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
            ulong ticket = PositionGetTicket(i);
            if(PositionSelectByTicket(ticket) && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                trade.PositionClose(ticket);
            }
        }
    }

    if(isTradingAllowed && UseDailyProfitLock && daily_pnl > daily_profit)
    {
        isTradingAllowed = false;
        Print("Max daily profit reached. Closing all trades and disabling trading for today.");
        //--- Close all positions
        for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
            ulong ticket = PositionGetTicket(i);
            if(PositionSelectByTicket(ticket) && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                trade.PositionClose(ticket);
            }
        }
    }
}
//+------------------------------------------------------------------+
void CheckFridayClose()
{
    if(!EnableFridayClose) return;

    MqlDateTime time;
    TimeCurrent(time);

    if(time.day_of_week == FRIDAY && TimeToString(TimeCurrent(), TIME_MINUTES) == FridayCloseTime)
    {
        Print("Friday close time reached. Closing all open positions.");
        for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
            ulong ticket = PositionGetTicket(i);
            if(PositionSelectByTicket(ticket) && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                trade.PositionClose(ticket);
            }
        }
    }
}
//+------------------------------------------------------------------+
bool IsNewsTime()
{
    if(!UseNewsFilter) return false;
    
    //--- This is a placeholder. A full implementation requires a news data source.
    return false;
}
//+------------------------------------------------------------------+