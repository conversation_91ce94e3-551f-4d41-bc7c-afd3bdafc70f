#!/usr/bin/env python3
"""
Streamlit Web Interface for First Candle EA Backtesting
Run with: streamlit run streamlit_app.py
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
from datetime import datetime, timedelta
import io

# Import our custom modules
from first_candle_backtester import FirstCandleBacktester, FirstCandleStrategy
from mt5_data_importer import MT5DataImporter

# Page configuration
st.set_page_config(
    page_title="First Candle EA Backtester",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

def main():
    st.markdown('<h1 class="main-header">📈 First Candle EA Backtesting System</h1>', unsafe_allow_html=True)
    st.markdown("**Mac-compatible backtesting and optimization for your First Candle breakout strategy**")
    
    # Sidebar for navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox("Choose a page", [
        "🏠 Home",
        "📊 Data Import", 
        "🔬 Single Backtest",
        "⚙️ Parameter Optimization",
        "🚀 Walk-Forward Analysis",
        "📈 Results Analysis"
    ])
    
    if page == "🏠 Home":
        show_home_page()
    elif page == "📊 Data Import":
        show_data_import_page()
    elif page == "🔬 Single Backtest":
        show_single_backtest_page()
    elif page == "⚙️ Parameter Optimization":
        show_optimization_page()
    elif page == "🚀 Walk-Forward Analysis":
        show_walkforward_page()
    elif page == "📈 Results Analysis":
        show_results_page()

def show_home_page():
    st.header("Welcome to the First Candle EA Backtesting System")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🎯 Features")
        st.markdown("""
        - **Mac Compatible**: No MT5 Python library required
        - **Multiple Data Sources**: Yahoo Finance, Alpha Vantage, CSV import
        - **Advanced Backtesting**: Replicates your MT5 EA logic
        - **Parameter Optimization**: Find optimal settings
        - **Walk-Forward Analysis**: Validate strategy robustness
        - **Interactive Visualizations**: Comprehensive reporting
        """)
        
        st.subheader("🚀 Quick Start")
        st.markdown("""
        1. **Import Data**: Use the Data Import page to load your forex data
        2. **Single Backtest**: Test with default parameters
        3. **Optimize**: Find the best parameter combinations
        4. **Validate**: Run walk-forward analysis
        5. **Deploy**: Use optimized parameters for live trading
        """)
    
    with col2:
        st.subheader("📊 Strategy Overview")
        st.markdown("""
        **First Candle Breakout Strategy:**
        - Identifies first 30-minute candle of trading session
        - Places pending orders above/below candle range
        - Uses multiple filters for trade quality
        - Implements advanced risk management
        """)
        
        st.subheader("🔧 Available Filters")
        st.markdown("""
        - **Volume Confirmation**: Above-average volume requirement
        - **ATR Volatility**: Optimal market volatility conditions
        - **Range Analysis**: Historical range percentile filtering
        - **Multiple Timeframe**: Higher timeframe trend alignment
        - **Session Optimization**: London vs New York specific settings
        """)
    
    # System status
    st.header("📋 System Status")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Python Version", f"{st.session_state.get('python_version', '3.x')}")
    with col2:
        st.metric("Backtrader", "✅ Available")
    with col3:
        st.metric("Data Sources", "3 Available")
    with col4:
        st.metric("Status", "🟢 Ready")

def show_data_import_page():
    st.header("📊 Data Import")
    
    # Initialize data importer
    if 'data_importer' not in st.session_state:
        st.session_state.data_importer = MT5DataImporter()
    
    importer = st.session_state.data_importer
    
    # Data source selection
    data_source = st.selectbox("Select Data Source", [
        "Yahoo Finance (Free)",
        "Alpha Vantage (API Key Required)", 
        "Upload MT5 CSV",
        "Load Saved Data"
    ])
    
    if data_source == "Yahoo Finance (Free)":
        st.subheader("Yahoo Finance Data Download")
        
        col1, col2 = st.columns(2)
        with col1:
            symbol = st.text_input("Symbol", value="EURUSD=X", help="Use format: EURUSD=X for forex")
            start_date = st.date_input("Start Date", value=datetime(2023, 1, 1))
        with col2:
            interval = st.selectbox("Interval", ["1d", "1h", "30m", "15m", "5m"])
            end_date = st.date_input("End Date", value=datetime.now())
        
        if st.button("Download Data"):
            with st.spinner("Downloading data..."):
                data = importer.download_yahoo_finance(
                    symbol=symbol,
                    start_date=start_date.strftime('%Y-%m-%d'),
                    end_date=end_date.strftime('%Y-%m-%d'),
                    interval=interval
                )
                
                if not data.empty:
                    st.success(f"Downloaded {len(data)} bars successfully!")
                    st.session_state.current_data = data
                    st.session_state.current_symbol = symbol
                    
                    # Show data preview
                    st.subheader("Data Preview")
                    st.dataframe(data.head(10))
                    
                    # Create synthetic 30m data if needed
                    if interval == "1d":
                        if st.button("Create Synthetic 30-minute Data"):
                            synthetic_data = importer.create_synthetic_intraday(data, '30T')
                            st.session_state.current_data = synthetic_data
                            st.success("Synthetic 30-minute data created!")
                else:
                    st.error("Failed to download data. Please check the symbol and try again.")
    
    elif data_source == "Alpha Vantage (API Key Required)":
        st.subheader("Alpha Vantage Data Download")
        st.info("Get your free API key from: https://www.alphavantage.co/support/#api-key")
        
        col1, col2 = st.columns(2)
        with col1:
            api_key = st.text_input("API Key", type="password")
            symbol = st.text_input("Symbol", value="EURUSD", help="6-character format: EURUSD")
        with col2:
            interval = st.selectbox("Interval", ["30min", "15min", "5min", "1min"])
        
        if st.button("Download Data") and api_key:
            with st.spinner("Downloading data..."):
                data = importer.download_alpha_vantage(symbol, api_key, interval)
                
                if not data.empty:
                    st.success(f"Downloaded {len(data)} bars successfully!")
                    st.session_state.current_data = data
                    st.session_state.current_symbol = symbol
                    st.dataframe(data.head(10))
                else:
                    st.error("Failed to download data. Please check your API key and symbol.")
    
    elif data_source == "Upload MT5 CSV":
        st.subheader("Upload MT5 Exported CSV")
        
        uploaded_file = st.file_uploader("Choose CSV file", type="csv")
        symbol = st.text_input("Symbol Name", value="EURUSD")
        
        if uploaded_file is not None:
            # Save uploaded file temporarily
            with open("temp_upload.csv", "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            data = importer.load_mt5_csv("temp_upload.csv", symbol)
            
            if not data.empty:
                st.success(f"Loaded {len(data)} bars successfully!")
                st.session_state.current_data = data
                st.session_state.current_symbol = symbol
                st.dataframe(data.head(10))
            else:
                st.error("Failed to load CSV. Please check the format.")
    
    # Show cached data
    if importer.list_cached_symbols():
        st.subheader("📁 Cached Data")
        cached_symbols = importer.list_cached_symbols()
        
        for symbol in cached_symbols:
            data = importer.get_cached_data(symbol)
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.write(f"**{symbol}**")
            with col2:
                st.write(f"{len(data)} bars")
            with col3:
                st.write(f"{data.index[0].date()} to {data.index[-1].date()}")
            with col4:
                if st.button(f"Use {symbol}", key=f"use_{symbol}"):
                    st.session_state.current_data = data
                    st.session_state.current_symbol = symbol
                    st.success(f"Selected {symbol} for backtesting")

def show_single_backtest_page():
    st.header("🔬 Single Backtest")
    
    # Check if data is available
    if 'current_data' not in st.session_state:
        st.warning("Please import data first using the Data Import page.")
        return
    
    data = st.session_state.current_data
    symbol = st.session_state.get('current_symbol', 'Unknown')
    
    st.info(f"Using data for {symbol}: {len(data)} bars from {data.index[0].date()} to {data.index[-1].date()}")
    
    # Parameter configuration
    st.subheader("⚙️ Strategy Parameters")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("**Session Settings**")
        session_start = st.selectbox("Session", ["London (8:00 GMT)", "New York (13:00 GMT)"])
        session_hour = 8 if "London" in session_start else 13
        
        st.markdown("**Risk Management**")
        risk_percent = st.slider("Risk Percent", 0.1, 5.0, 1.0, 0.1)
        risk_reward = st.slider("Risk:Reward Ratio", 1.0, 5.0, 2.0, 0.1)
    
    with col2:
        st.markdown("**Enhanced Filters**")
        use_volume = st.checkbox("Volume Filter", value=True)
        volume_multi = st.slider("Volume Multiplier", 1.0, 2.0, 1.2, 0.1)
        
        use_atr = st.checkbox("ATR Filter", value=True)
        min_atr = st.slider("Min ATR Multiplier", 0.5, 2.0, 1.0, 0.1)
        max_atr = st.slider("Max ATR Multiplier", 2.0, 5.0, 3.0, 0.1)
    
    with col3:
        st.markdown("**Advanced Features**")
        use_mtf = st.checkbox("Multiple Timeframe", value=True)
        use_range = st.checkbox("Range Filter", value=True)
        range_percentile = st.slider("Range Percentile", 50.0, 90.0, 70.0, 5.0)
        
        use_multiple_targets = st.checkbox("Multiple Targets", value=True)
        max_hours = st.slider("Max Trade Hours", 1, 24, 8)
    
    # Run backtest button
    if st.button("🚀 Run Backtest", type="primary"):
        
        # Prepare parameters
        params = {
            'session_start_hour': session_hour,
            'risk_percent': risk_percent,
            'risk_reward_ratio': risk_reward,
            'use_volume_filter': use_volume,
            'volume_multiplier': volume_multi,
            'use_atr_filter': use_atr,
            'min_atr_multiplier': min_atr,
            'max_atr_multiplier': max_atr,
            'use_mtf': use_mtf,
            'use_range_filter': use_range,
            'range_percentile': range_percentile,
            'use_multiple_targets': use_multiple_targets,
            'max_trade_hours': max_hours
        }
        
        # Run backtest
        with st.spinner("Running backtest..."):
            backtester = FirstCandleBacktester(
                symbol=symbol,
                start_date=data.index[0].strftime('%Y-%m-%d'),
                end_date=data.index[-1].strftime('%Y-%m-%d')
            )
            
            # Use current data instead of downloading
            backtester.data_30m = data
            backtester.data_1h = data  # Simplified
            
            try:
                result, cerebro = backtester.run_backtest(params)
                
                if result:
                    st.session_state.last_backtest_result = result
                    
                    # Display results
                    st.success("Backtest completed successfully!")
                    
                    # Key metrics
                    st.subheader("📊 Key Results")
                    col1, col2, col3, col4 = st.columns(4)
                    
                    with col1:
                        st.metric("Total Return", f"{result['total_return']:.2f}%")
                    with col2:
                        st.metric("Total Trades", result['total_trades'])
                    with col3:
                        st.metric("Win Rate", f"{result['win_rate']:.1f}%")
                    with col4:
                        st.metric("Sharpe Ratio", f"{result['sharpe_ratio']:.2f}")
                    
                    # Additional metrics
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("Winning Trades", result['winning_trades'])
                    with col2:
                        st.metric("Losing Trades", result['losing_trades'])
                    with col3:
                        st.metric("Avg Win", f"{result['avg_win']:.2f}")
                    with col4:
                        st.metric("Max Drawdown", f"{result['max_drawdown']:.2f}%")
                    
                    # Plot equity curve (simplified)
                    if result['trades_log']:
                        st.subheader("📈 Trade Analysis")
                        trades_df = pd.DataFrame(result['trades_log'])
                        
                        # Trades over time
                        fig = go.Figure()
                        fig.add_trace(go.Scatter(
                            x=trades_df['entry_time'],
                            y=trades_df['pnl_pips'],
                            mode='markers',
                            marker=dict(
                                color=trades_df['pnl_pips'],
                                colorscale='RdYlGn',
                                size=8
                            ),
                            name='Trade PnL (pips)'
                        ))
                        fig.update_layout(title="Trade Results Over Time", xaxis_title="Date", yaxis_title="PnL (pips)")
                        st.plotly_chart(fig, use_container_width=True)
                
                else:
                    st.error("Backtest failed. Please check your parameters and data.")
                    
            except Exception as e:
                st.error(f"Error running backtest: {str(e)}")

def show_optimization_page():
    st.header("⚙️ Parameter Optimization")
    st.info("Find the best parameter combinations for your strategy")
    
    # Implementation would go here
    st.write("Parameter optimization interface coming soon...")

def show_walkforward_page():
    st.header("🚀 Walk-Forward Analysis")
    st.info("Validate strategy robustness over time")
    
    # Implementation would go here
    st.write("Walk-forward analysis interface coming soon...")

def show_results_page():
    st.header("📈 Results Analysis")
    
    if 'last_backtest_result' in st.session_state:
        result = st.session_state.last_backtest_result
        
        st.subheader("Detailed Performance Analysis")
        
        # Create comprehensive analysis
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Performance Metrics**")
            metrics_df = pd.DataFrame({
                'Metric': ['Total Return', 'Win Rate', 'Sharpe Ratio', 'Max Drawdown', 'Total Trades'],
                'Value': [f"{result['total_return']:.2f}%", f"{result['win_rate']:.1f}%", 
                         f"{result['sharpe_ratio']:.2f}", f"{result['max_drawdown']:.2f}%", 
                         result['total_trades']]
            })
            st.dataframe(metrics_df, hide_index=True)
        
        with col2:
            st.markdown("**Trade Statistics**")
            if result['trades_log']:
                trades_df = pd.DataFrame(result['trades_log'])
                
                st.write(f"Average trade duration: {trades_df['duration_hours'].mean():.1f} hours")
                st.write(f"Best trade: {trades_df['pnl_pips'].max():.1f} pips")
                st.write(f"Worst trade: {trades_df['pnl_pips'].min():.1f} pips")
                st.write(f"Average win: {trades_df[trades_df['pnl_pips'] > 0]['pnl_pips'].mean():.1f} pips")
                st.write(f"Average loss: {trades_df[trades_df['pnl_pips'] < 0]['pnl_pips'].mean():.1f} pips")
    else:
        st.info("No backtest results available. Please run a backtest first.")

if __name__ == "__main__":
    main()
