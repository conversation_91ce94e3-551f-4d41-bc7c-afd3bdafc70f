#!/usr/bin/env python3
"""
Complete Backtesting Workflow Script
Automates the entire process from data import to parameter export
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any

# Import our modules
from first_candle_backtester import FirstCandleBacktester
from mt5_data_importer import MT5DataImporter
from config import Config, get_alpha_vantage_key, get_default_params, get_optimization_ranges

class CompleteAnalysisWorkflow:
    """
    Complete workflow for backtesting analysis
    """
    
    def __init__(self, symbol: str = 'EURUSD', start_date: str = '2023-01-01', end_date: str = '2024-01-01'):
        self.symbol = symbol
        self.start_date = start_date
        self.end_date = end_date
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Create directories
        Config.create_directories()
        
        # Initialize components
        self.importer = MT5DataImporter()
        self.backtester = None
        self.data = None
        
        # Results storage
        self.single_backtest_result = None
        self.optimization_results = None
        self.walkforward_results = None
        
        print(f"🚀 Starting Complete Analysis Workflow")
        print(f"Symbol: {symbol}")
        print(f"Period: {start_date} to {end_date}")
        print(f"Timestamp: {self.timestamp}")
        print("=" * 60)
    
    def step1_import_data(self, use_alpha_vantage: bool = True, interval: str = '30min') -> bool:
        """Step 1: Import and prepare data"""
        
        print("\n📊 STEP 1: Data Import & Preparation")
        print("-" * 40)
        
        try:
            if use_alpha_vantage:
                print(f"Downloading {self.symbol} {interval} data from Alpha Vantage...")
                api_key = get_alpha_vantage_key()
                
                self.data = self.importer.download_alpha_vantage(self.symbol, api_key, interval)
                
                if self.data.empty:
                    print("❌ Alpha Vantage download failed, trying Yahoo Finance...")
                    use_alpha_vantage = False
            
            if not use_alpha_vantage:
                print(f"Downloading {self.symbol} data from Yahoo Finance...")
                yahoo_symbol = f"{self.symbol}=X" if len(self.symbol) == 6 else self.symbol
                
                daily_data = self.importer.download_yahoo_finance(
                    yahoo_symbol, self.start_date, self.end_date, '1d'
                )
                
                if not daily_data.empty:
                    print("Creating synthetic 30-minute data...")
                    self.data = self.importer.create_synthetic_intraday(daily_data, '30T')
                else:
                    print("❌ Yahoo Finance download failed")
                    return False
            
            if not self.data.empty:
                # Save data
                data_filename = Config.get_timestamped_filename(
                    f'{self.symbol}_{interval}_data', '.csv', Config.DATA_DIR
                )
                self.importer.save_data(self.data, data_filename)
                
                print(f"✅ Data imported successfully!")
                print(f"   Bars: {len(self.data)}")
                print(f"   Period: {self.data.index[0]} to {self.data.index[-1]}")
                print(f"   Saved to: {data_filename}")
                
                # Initialize backtester
                self.backtester = FirstCandleBacktester(
                    symbol=self.symbol,
                    start_date=self.data.index[0].strftime('%Y-%m-%d'),
                    end_date=self.data.index[-1].strftime('%Y-%m-%d')
                )
                
                # Use imported data
                self.backtester.data_30m = self.data
                self.backtester.data_1h = self.importer.resample_data(self.data, '1H')
                
                return True
            else:
                print("❌ No data imported")
                return False
                
        except Exception as e:
            print(f"❌ Error in data import: {e}")
            return False
    
    def step2_single_backtest(self, custom_params: Dict = None) -> bool:
        """Step 2: Run single backtest with default or custom parameters"""
        
        print("\n🔬 STEP 2: Single Backtest")
        print("-" * 40)
        
        if not self.backtester:
            print("❌ Backtester not initialized. Run step1_import_data first.")
            return False
        
        try:
            # Use custom parameters or defaults
            params = custom_params if custom_params else get_default_params()
            
            print("Running single backtest with parameters:")
            for key, value in params.items():
                print(f"  {key}: {value}")
            
            # Run backtest
            self.single_backtest_result, _ = self.backtester.run_backtest(params)
            
            if self.single_backtest_result:
                # Save results
                result_filename = Config.get_timestamped_filename(
                    'single_backtest_result', '.json', Config.RESULTS_DIR
                )
                
                with open(result_filename, 'w') as f:
                    json.dump({
                        'parameters': params,
                        'results': self.single_backtest_result,
                        'symbol': self.symbol,
                        'period': f"{self.start_date} to {self.end_date}",
                        'timestamp': self.timestamp
                    }, f, indent=2)
                
                print(f"✅ Single backtest completed!")
                print(f"   Total Return: {self.single_backtest_result['total_return']:.2f}%")
                print(f"   Win Rate: {self.single_backtest_result['win_rate']:.1f}%")
                print(f"   Sharpe Ratio: {self.single_backtest_result['sharpe_ratio']:.2f}")
                print(f"   Max Drawdown: {self.single_backtest_result['max_drawdown']:.2f}%")
                print(f"   Total Trades: {self.single_backtest_result['total_trades']}")
                print(f"   Results saved to: {result_filename}")
                
                return True
            else:
                print("❌ Single backtest failed")
                return False
                
        except Exception as e:
            print(f"❌ Error in single backtest: {e}")
            return False
    
    def step3_parameter_optimization(self, custom_ranges: Dict = None) -> bool:
        """Step 3: Run parameter optimization"""
        
        print("\n⚙️ STEP 3: Parameter Optimization")
        print("-" * 40)
        
        if not self.backtester:
            print("❌ Backtester not initialized. Run step1_import_data first.")
            return False
        
        try:
            # Use custom ranges or defaults
            param_ranges = custom_ranges if custom_ranges else get_optimization_ranges()
            
            # Calculate total combinations
            total_combinations = 1
            for values in param_ranges.values():
                total_combinations *= len(values)
            
            print(f"Starting parameter optimization...")
            print(f"Parameters to optimize: {list(param_ranges.keys())}")
            print(f"Total combinations: {total_combinations}")
            print("This may take a while...")
            
            # Run optimization
            self.optimization_results = self.backtester.optimize_parameters(param_ranges)
            
            if self.optimization_results:
                # Save results
                opt_filename = Config.get_timestamped_filename(
                    'optimization_results', '.json', Config.RESULTS_DIR
                )
                
                with open(opt_filename, 'w') as f:
                    json.dump({
                        'parameter_ranges': param_ranges,
                        'results': self.optimization_results,
                        'symbol': self.symbol,
                        'period': f"{self.start_date} to {self.end_date}",
                        'timestamp': self.timestamp,
                        'total_combinations': total_combinations
                    }, f, indent=2)
                
                print(f"✅ Parameter optimization completed!")
                print(f"   Total combinations tested: {len(self.optimization_results)}")
                print(f"   Best return: {self.optimization_results[0]['total_return']:.2f}%")
                print(f"   Best win rate: {self.optimization_results[0]['win_rate']:.1f}%")
                print(f"   Best Sharpe: {self.optimization_results[0]['sharpe_ratio']:.2f}")
                print(f"   Results saved to: {opt_filename}")
                
                # Show top 5 results
                print("\n🏆 Top 5 Parameter Combinations:")
                for i, result in enumerate(self.optimization_results[:5]):
                    print(f"{i+1}. Return: {result['total_return']:.2f}%, "
                          f"Win Rate: {result['win_rate']:.1f}%, "
                          f"Sharpe: {result['sharpe_ratio']:.2f}")
                
                return True
            else:
                print("❌ Parameter optimization failed")
                return False
                
        except Exception as e:
            print(f"❌ Error in parameter optimization: {e}")
            return False
    
    def step4_walkforward_analysis(self, window_months: int = 6, step_months: int = 1) -> bool:
        """Step 4: Run walk-forward analysis"""
        
        print("\n🚀 STEP 4: Walk-Forward Analysis")
        print("-" * 40)
        
        if not self.backtester or not self.optimization_results:
            print("❌ Prerequisites not met. Run steps 1-3 first.")
            return False
        
        try:
            print(f"Starting walk-forward analysis...")
            print(f"Optimization window: {window_months} months")
            print(f"Testing step: {step_months} month(s)")
            
            # Use parameter ranges from optimization
            param_ranges = get_optimization_ranges()
            
            # Run walk-forward analysis
            self.walkforward_results = self.backtester.walk_forward_analysis(
                param_ranges=param_ranges,
                window_months=window_months,
                step_months=step_months
            )
            
            if self.walkforward_results:
                # Save results
                wf_filename = Config.get_timestamped_filename(
                    'walkforward_results', '.csv', Config.RESULTS_DIR
                )
                
                df_results = pd.DataFrame(self.walkforward_results)
                df_results.to_csv(wf_filename, index=False)
                
                # Analyze results
                analysis_results = self.backtester.analyze_results(self.walkforward_results)
                
                print(f"✅ Walk-forward analysis completed!")
                print(f"   Periods tested: {len(self.walkforward_results)}")
                print(f"   Average test return: {df_results['test_return'].mean():.2f}%")
                print(f"   Positive periods: {(df_results['test_return'] > 0).sum()}/{len(df_results)}")
                print(f"   Results saved to: {wf_filename}")
                
                return True
            else:
                print("❌ Walk-forward analysis failed")
                return False
                
        except Exception as e:
            print(f"❌ Error in walk-forward analysis: {e}")
            return False
    
    def step5_export_optimal_parameters(self) -> bool:
        """Step 5: Export optimal parameters in multiple formats"""
        
        print("\n📤 STEP 5: Export Optimal Parameters")
        print("-" * 40)
        
        if not self.optimization_results or not self.walkforward_results:
            print("❌ Prerequisites not met. Run steps 1-4 first.")
            return False
        
        try:
            # Get best parameters
            best_result = self.optimization_results[0]
            best_params = best_result['parameters']
            
            # Calculate validation metrics
            df_wf = pd.DataFrame(self.walkforward_results)
            validation_metrics = {
                'walkforward_periods': len(df_wf),
                'avg_test_return': df_wf['test_return'].mean(),
                'positive_periods': (df_wf['test_return'] > 0).sum(),
                'consistency_score': (df_wf['test_return'] > 0).mean(),
                'best_period_return': df_wf['test_return'].max(),
                'worst_period_return': df_wf['test_return'].min()
            }
            
            # Export JSON format
            json_filename = Config.get_timestamped_filename(
                'optimal_parameters', '.json', Config.EXPORTS_DIR
            )
            
            export_data = {
                'strategy_name': 'FirstCandleBreakout_Optimized',
                'optimization_date': datetime.now().isoformat(),
                'symbol': self.symbol,
                'data_period': f"{self.start_date} to {self.end_date}",
                'best_parameters': best_params,
                'performance_metrics': {
                    'total_return': best_result['total_return'],
                    'win_rate': best_result['win_rate'],
                    'sharpe_ratio': best_result['sharpe_ratio'],
                    'max_drawdown': best_result['max_drawdown'],
                    'total_trades': best_result['total_trades']
                },
                'validation_results': validation_metrics
            }
            
            with open(json_filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            # Export MT5 configuration format
            mt5_filename = Config.get_timestamped_filename(
                'mt5_config', '.txt', Config.EXPORTS_DIR
            )
            
            with open(mt5_filename, 'w') as f:
                f.write(f"// Optimized parameters for FirstCandleBreakout EA\n")
                f.write(f"// Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"// Symbol: {self.symbol}\n")
                f.write(f"// Validation: {validation_metrics['walkforward_periods']} periods, "
                       f"{validation_metrics['consistency_score']*100:.1f}% positive\n")
                f.write(f"// Best return: {best_result['total_return']:.2f}%, "
                       f"Win rate: {best_result['win_rate']:.1f}%\n\n")
                
                # Parameter mapping
                param_mapping = {
                    'risk_percent': 'RiskPercent',
                    'risk_reward_ratio': 'RiskRewardRatio',
                    'volume_multiplier': 'VolumeMultiplier',
                    'min_atr_multiplier': 'MinATRMultiplier',
                    'max_atr_multiplier': 'MaxATRMultiplier',
                    'range_percentile': 'RangePercentile',
                    'breakeven_ratio': 'BreakevenAtRatio',
                    'trailing_pips': 'TrailingStopPips'
                }
                
                for py_param, mt5_param in param_mapping.items():
                    if py_param in best_params:
                        value = best_params[py_param]
                        if isinstance(value, float):
                            f.write(f"input double {mt5_param} = {value};\n")
                        else:
                            f.write(f"input int {mt5_param} = {value};\n")
            
            print(f"✅ Optimal parameters exported!")
            print(f"   JSON format: {json_filename}")
            print(f"   MT5 format: {mt5_filename}")
            print(f"\n🏆 Best Parameters:")
            for param, value in best_params.items():
                print(f"   {param}: {value}")
            
            print(f"\n📊 Validation Results:")
            print(f"   Periods tested: {validation_metrics['walkforward_periods']}")
            print(f"   Average return: {validation_metrics['avg_test_return']:.2f}%")
            print(f"   Consistency: {validation_metrics['consistency_score']*100:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ Error exporting parameters: {e}")
            return False
    
    def run_complete_workflow(self, use_alpha_vantage: bool = True, 
                            custom_params: Dict = None, custom_ranges: Dict = None) -> bool:
        """Run the complete workflow from start to finish"""
        
        print("🎯 RUNNING COMPLETE BACKTESTING WORKFLOW")
        print("=" * 60)
        
        # Step 1: Import Data
        if not self.step1_import_data(use_alpha_vantage):
            return False
        
        # Step 2: Single Backtest
        if not self.step2_single_backtest(custom_params):
            return False
        
        # Step 3: Parameter Optimization
        if not self.step3_parameter_optimization(custom_ranges):
            return False
        
        # Step 4: Walk-Forward Analysis
        if not self.step4_walkforward_analysis():
            return False
        
        # Step 5: Export Parameters
        if not self.step5_export_optimal_parameters():
            return False
        
        print("\n" + "=" * 60)
        print("🎉 COMPLETE WORKFLOW FINISHED SUCCESSFULLY!")
        print("=" * 60)
        print(f"\n📁 All files saved under: backtesting/")
        print(f"📊 Data: backtesting/data/")
        print(f"📈 Results: backtesting/results/")
        print(f"📤 Exports: backtesting/exports/")
        print(f"📋 Reports: backtesting/reports/")
        
        return True


def main():
    """Main function to run the complete analysis"""
    
    # Configuration
    symbol = input("Enter symbol (default: EURUSD): ").strip() or 'EURUSD'
    start_date = input("Enter start date (default: 2023-01-01): ").strip() or '2023-01-01'
    end_date = input("Enter end date (default: 2024-01-01): ").strip() or '2024-01-01'
    
    use_av = input("Use Alpha Vantage for real forex data? (y/n, default: y): ").strip().lower()
    use_alpha_vantage = use_av != 'n'
    
    # Initialize workflow
    workflow = CompleteAnalysisWorkflow(symbol, start_date, end_date)
    
    # Run complete workflow
    success = workflow.run_complete_workflow(use_alpha_vantage)
    
    if success:
        print("\n✅ Analysis complete! Check the backtesting/ folder for all results.")
    else:
        print("\n❌ Analysis failed. Check error messages above.")


if __name__ == "__main__":
    main()
