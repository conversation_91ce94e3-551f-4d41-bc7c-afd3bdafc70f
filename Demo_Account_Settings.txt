=== DEMO ACCOUNT SETTINGS FOR COMPREHENSIVE DAY TRADING EA ===

PROBLEM FIXED: Volume threshold too high for demo accounts with small volumes

KEY CHANGES MADE:
1. VROC_Threshold reduced from 20% to 5% (much more suitable for demo)
2. Added UseVolumeFilter toggle (can disable if still having issues)
3. Added MinVolumeThreshold = 1.0 (handles very small volumes)
4. Enhanced logging to show volume data and signals
5. Better dashboard display showing current volume

RECOMMENDED DEMO SETTINGS:
==========================

Strategy Settings:
- VROC_Threshold = 3.0 (reduce from 5.0 for more signals)
- UseVolumeFilter = true (set to false if still no signals)  
- MinVolumeThreshold = 1.0 (adjust based on your broker's volume)
- VolatilityFilter = 1.0 (reduced from 1.2 for more signals)

BASED ON YOUR LOG: Your VROC was -3.2% but threshold was 5.0%
SOLUTION: Reduce VROC_Threshold to 3.0 to catch this signal

Risk Management (Conservative for testing):
- RiskPercentPerTrade = 0.5 (very conservative for demo testing)
- DailyLossLimit = 2.0 (tight control for testing)
- MaxConcurrentPositions = 1 (test one position at a time initially)

Session Settings:
- TradeOverlapOnly = false (allows more trading opportunities)
- TradeLondonSession = true
- TradeNYSession = true
- DetailedLogging = true (IMPORTANT: Enable for debugging)

Monitoring Settings:
- ShowDashboard = true (monitor volume and signals)
- EnableAlerts = true
- DetailedLogging = true

TROUBLESHOOTING STEPS:
=====================

1. ENABLE DETAILED LOGGING:
   Set DetailedLogging = true to see:
   - Volume data availability
   - VROC calculations
   - Signal detection
   - Trade execution attempts

2. CHECK LOGS FOR:
   - "Volume data available - Current volume: X"
   - "VROC Signal: X% (Current: Y, Previous: Z)"
   - "Bullish/Bearish signal detected"
   - "Signal blocked by volume filter"

3. IF NO SIGNALS APPEARING:
   - Set UseVolumeFilter = false (temporarily)
   - Reduce VROC_Threshold to 2.0 or 1.0
   - Check if VWMA_RSI and MFI values are in range (shown on dashboard)

4. IF VOLUMES ARE EXTREMELY LOW:
   - Set MinVolumeThreshold = 0.1 (or even lower)
   - Consider switching to VOLUME_REAL instead of VOLUME_TICK

5. DEMO ACCOUNT CONSIDERATIONS:
   - Demo volumes might be artificial/reduced
   - Spreads might be different from live
   - Some brokers have limited demo volume data

TESTING CHECKLIST:
=================
□ EA loads without errors
□ Dashboard appears and updates
□ Logs show "Volume data available"
□ VROC values appear in dashboard
□ Signal strength updates
□ Risk management values are correct
□ Test with small position sizes first

EXPECTED LOG MESSAGES (when working):
====================================
- "Volume data available - Current volume: XXX"
- "VROC Signal: X.X% (Current: XXX, Previous: XXX)" 
- "Bullish/Bearish signal detected - VWMA_RSI: XX, MFI: XX, VROC: XX"
- Position opening messages

If you're still not seeing trades after these changes, check the Expert tab logs and let me know what messages you see!