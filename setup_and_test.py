#!/usr/bin/env python3
"""
Setup and Test Script for First Candle EA Backtesting System
This script will:
1. Test your Alpha Vantage API key
2. Download sample data
3. Run a quick backtest
4. Verify everything is working
"""

import sys
import os
import subprocess
import importlib.util
from datetime import datetime

def check_dependencies():
    """Check if all required packages are installed"""
    
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'pandas', 'numpy', 'backtrader', 'yfinance', 
        'matplotlib', 'seaborn', 'requests', 'streamlit'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ All packages installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install packages: {e}")
            return False
    
    return True

def test_api_key():
    """Test Alpha Vantage API key"""
    
    print("\n🔑 Testing Alpha Vantage API key...")
    
    try:
        from config import get_alpha_vantage_key
        from mt5_data_importer import MT5DataImporter
        
        api_key = get_alpha_vantage_key()
        
        if not api_key or api_key == 'YOUR_API_KEY_HERE':
            print("❌ API key not configured properly")
            return False
        
        print(f"API Key: {api_key[:8]}...{api_key[-4:]} (masked)")
        
        # Quick test with small data request
        importer = MT5DataImporter()
        
        print("Testing API connectivity...")
        test_data = importer.download_alpha_vantage('EURUSD', api_key, '30min')
        
        if not test_data.empty:
            print(f"✅ API key working! Downloaded {len(test_data)} bars")
            return True
        else:
            print("❌ API key test failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API key: {e}")
        return False

def run_quick_backtest():
    """Run a quick backtest to verify everything works"""
    
    print("\n🔬 Running quick backtest...")
    
    try:
        from first_candle_backtester import FirstCandleBacktester
        
        # Create a simple backtest
        backtester = FirstCandleBacktester(
            symbol='EURUSD=X',
            start_date='2023-06-01',
            end_date='2023-07-01'
        )
        
        # Simple parameters for quick test
        test_params = {
            'risk_percent': 1.0,
            'risk_reward_ratio': 2.0,
            'use_volume_filter': False,  # Disable for quick test
            'use_atr_filter': False,     # Disable for quick test
            'use_mtf': False,            # Disable for quick test
            'use_range_filter': False,   # Disable for quick test
            'max_trade_hours': 24
        }
        
        print("Running backtest with simplified parameters...")
        result, _ = backtester.run_backtest(test_params)
        
        if result:
            print("✅ Backtest completed successfully!")
            print(f"   Total Return: {result['total_return']:.2f}%")
            print(f"   Total Trades: {result['total_trades']}")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            return True
        else:
            print("❌ Backtest failed")
            return False
            
    except Exception as e:
        print(f"❌ Error running backtest: {e}")
        return False

def test_streamlit():
    """Test if Streamlit can be launched"""
    
    print("\n🌐 Testing Streamlit web interface...")
    
    try:
        import streamlit
        print("✅ Streamlit is available")
        print("💡 You can run the web interface with: streamlit run streamlit_app.py")
        return True
    except ImportError:
        print("❌ Streamlit not available")
        return False

def create_sample_data():
    """Create sample data files for testing"""
    
    print("\n📊 Creating sample data...")
    
    try:
        from mt5_data_importer import MT5DataImporter
        from config import get_alpha_vantage_key
        
        importer = MT5DataImporter()
        api_key = get_alpha_vantage_key()
        
        # Download and save sample data
        print("Downloading EURUSD sample data...")
        sample_data = importer.download_alpha_vantage('EURUSD', api_key, '30min')
        
        if not sample_data.empty:
            filename = f"sample_eurusd_30m_{datetime.now().strftime('%Y%m%d')}.csv"
            importer.save_data(sample_data, filename)
            print(f"✅ Sample data saved to: {filename}")
            return True
        else:
            print("❌ Failed to create sample data")
            return False
            
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return False

def print_next_steps():
    """Print next steps for the user"""
    
    print("\n" + "=" * 60)
    print("🎉 SETUP COMPLETE! Your First Candle EA Backtesting System is ready!")
    print("=" * 60)
    
    print("\n📋 NEXT STEPS:")
    print("\n1. 🌐 WEB INTERFACE (Recommended for beginners):")
    print("   streamlit run streamlit_app.py")
    print("   Then open your browser to the displayed URL")
    
    print("\n2. 🖥️  COMMAND LINE:")
    print("   python first_candle_backtester.py")
    
    print("\n3. 🔑 TEST API KEY:")
    print("   python test_api_key.py")
    
    print("\n4. 📊 IMPORT DATA:")
    print("   python mt5_data_importer.py")
    
    print("\n💡 TIPS:")
    print("- Start with the web interface for easy parameter testing")
    print("- Your Alpha Vantage API key is pre-configured")
    print("- Sample data has been downloaded for immediate testing")
    print("- Check the setup_instructions.md file for detailed documentation")
    
    print("\n🔧 CONFIGURATION:")
    print("- Edit config.py to modify default parameters")
    print("- Add more symbols or change API settings there")
    
    print("\n📈 STRATEGY TESTING:")
    print("- Test your original EA parameters first")
    print("- Then try the enhanced version with filters")
    print("- Finally test the advanced version with MTF analysis")

def main():
    """Main setup and test function"""
    
    print("🚀 First Candle EA Backtesting System - Setup & Test")
    print("=" * 60)
    print("This script will verify your installation and test all components")
    print()
    
    # Step 1: Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed. Please install required packages manually.")
        return False
    
    # Step 2: Test API key
    if not test_api_key():
        print("⚠️  API key test failed. You can still use Yahoo Finance data.")
        print("Check your API key in config.py or get a new one from Alpha Vantage.")
    
    # Step 3: Run quick backtest
    if not run_quick_backtest():
        print("❌ Quick backtest failed. Please check the error messages above.")
        return False
    
    # Step 4: Test Streamlit
    test_streamlit()
    
    # Step 5: Create sample data
    create_sample_data()
    
    # Step 6: Print next steps
    print_next_steps()
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ Setup completed successfully!")
        
        # Ask if user wants to launch web interface
        launch_web = input("\n🌐 Launch web interface now? (y/n): ").lower().startswith('y')
        
        if launch_web:
            print("Launching Streamlit web interface...")
            try:
                subprocess.run([sys.executable, '-m', 'streamlit', 'run', 'streamlit_app.py'])
            except KeyboardInterrupt:
                print("\nWeb interface closed.")
            except Exception as e:
                print(f"Error launching web interface: {e}")
                print("You can launch it manually with: streamlit run streamlit_app.py")
    else:
        print("\n❌ Setup encountered errors. Please check the messages above.")
        print("You may need to install dependencies manually or check your API key.")
