#!/usr/bin/env python3
"""
MT5 Data Importer for Mac Users
Provides utilities to work with MT5 exported data and alternative data sources
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yfinance as yf
import requests
import json
from typing import Optional, Dict, List
import os

class MT5DataImporter:
    """
    Handles data import from various sources for Mac users who can't use MT5 Python library
    """
    
    def __init__(self):
        self.data_cache = {}
        
    def load_mt5_csv(self, filepath: str, symbol: str = None) -> pd.DataFrame:
        """
        Load MT5 exported CSV data
        
        Expected CSV format from MT5:
        Date,Time,Open,High,Low,Close,Volume
        2023.01.01,00:00,1.0500,1.0520,1.0495,1.0510,1000
        """
        try:
            # Read CSV with MT5 format
            df = pd.read_csv(filepath)
            
            # Handle different possible column names
            column_mapping = {
                'Date': 'date',
                'Time': 'time', 
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume',
                'Tick Volume': 'volume',
                'Real Volume': 'real_volume'
            }
            
            # Rename columns to standard format
            df = df.rename(columns=column_mapping)
            
            # Combine date and time columns if separate
            if 'date' in df.columns and 'time' in df.columns:
                df['datetime'] = pd.to_datetime(df['date'] + ' ' + df['time'])
            elif 'date' in df.columns:
                df['datetime'] = pd.to_datetime(df['date'])
            else:
                # Assume first column is datetime
                df['datetime'] = pd.to_datetime(df.iloc[:, 0])
            
            # Set datetime as index
            df.set_index('datetime', inplace=True)
            
            # Ensure numeric columns
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Remove any rows with NaN values
            df.dropna(inplace=True)
            
            print(f"Loaded {len(df)} bars from {filepath}")
            if symbol:
                self.data_cache[symbol] = df
                
            return df
            
        except Exception as e:
            print(f"Error loading MT5 CSV: {e}")
            return pd.DataFrame()
    
    def download_yahoo_finance(self, symbol: str, start_date: str, end_date: str, 
                              interval: str = '1d') -> pd.DataFrame:
        """
        Download data from Yahoo Finance
        
        Available intervals: 1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo
        Note: Minute data is only available for last 60 days
        """
        try:
            ticker = yf.Ticker(symbol)
            df = ticker.history(start=start_date, end=end_date, interval=interval)
            
            if df.empty:
                print(f"No data found for {symbol}")
                return pd.DataFrame()
            
            # Standardize column names
            df.columns = [col.lower() for col in df.columns]
            
            # Yahoo Finance uses 'adj close' - we'll use 'close'
            if 'adj close' in df.columns:
                df['close'] = df['adj close']
            
            print(f"Downloaded {len(df)} bars for {symbol} from Yahoo Finance")
            self.data_cache[symbol] = df
            
            return df
            
        except Exception as e:
            print(f"Error downloading from Yahoo Finance: {e}")
            return pd.DataFrame()
    
    def download_alpha_vantage(self, symbol: str, api_key: str, 
                              interval: str = '30min') -> pd.DataFrame:
        """
        Download forex data from Alpha Vantage
        
        Get free API key from: https://www.alphavantage.co/support/#api-key
        Available intervals: 1min, 5min, 15min, 30min, 60min
        """
        try:
            # Convert symbol format (EURUSD -> EUR, USD)
            if len(symbol) == 6:
                from_currency = symbol[:3]
                to_currency = symbol[3:]
            else:
                raise ValueError("Symbol should be 6 characters (e.g., EURUSD)")
            
            url = f"https://www.alphavantage.co/query"
            params = {
                'function': 'FX_INTRADAY',
                'from_symbol': from_currency,
                'to_symbol': to_currency,
                'interval': interval,
                'apikey': api_key,
                'outputsize': 'full',
                'datatype': 'json'
            }
            
            response = requests.get(url, params=params)
            data = response.json()
            
            # Check for API errors
            if 'Error Message' in data:
                print(f"Alpha Vantage Error: {data['Error Message']}")
                return pd.DataFrame()
            
            if 'Note' in data:
                print(f"Alpha Vantage Note: {data['Note']}")
                return pd.DataFrame()
            
            # Extract time series data
            time_series_key = f'Time Series FX ({interval})'
            if time_series_key not in data:
                print(f"No time series data found. Available keys: {list(data.keys())}")
                return pd.DataFrame()
            
            time_series = data[time_series_key]
            
            # Convert to DataFrame
            df = pd.DataFrame.from_dict(time_series, orient='index')
            df.index = pd.to_datetime(df.index)
            df = df.sort_index()
            
            # Standardize column names
            column_mapping = {
                '1. open': 'open',
                '2. high': 'high', 
                '3. low': 'low',
                '4. close': 'close'
            }
            df = df.rename(columns=column_mapping)
            
            # Convert to numeric
            for col in ['open', 'high', 'low', 'close']:
                df[col] = pd.to_numeric(df[col])
            
            # Add volume (Alpha Vantage doesn't provide forex volume)
            df['volume'] = 1000  # Placeholder
            
            print(f"Downloaded {len(df)} bars for {symbol} from Alpha Vantage")
            self.data_cache[symbol] = df
            
            return df
            
        except Exception as e:
            print(f"Error downloading from Alpha Vantage: {e}")
            return pd.DataFrame()
    
    def resample_data(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """
        Resample data to different timeframes
        
        timeframe options: '1T', '5T', '15T', '30T', '1H', '4H', '1D'
        """
        try:
            # Define aggregation rules
            agg_rules = {
                'open': 'first',
                'high': 'max',
                'low': 'min', 
                'close': 'last',
                'volume': 'sum'
            }
            
            # Resample
            resampled = df.resample(timeframe).agg(agg_rules)
            
            # Remove incomplete bars (last bar might be incomplete)
            resampled = resampled.dropna()
            
            print(f"Resampled to {timeframe}: {len(resampled)} bars")
            
            return resampled
            
        except Exception as e:
            print(f"Error resampling data: {e}")
            return df
    
    def create_synthetic_intraday(self, daily_df: pd.DataFrame, 
                                 target_timeframe: str = '30T') -> pd.DataFrame:
        """
        Create synthetic intraday data from daily data
        This is a simplified approach for backtesting when intraday data is not available
        """
        try:
            synthetic_data = []
            
            for date, row in daily_df.iterrows():
                # Calculate number of bars per day
                if target_timeframe == '30T':
                    bars_per_day = 48  # 24 hours * 2 (30-min bars)
                elif target_timeframe == '1H':
                    bars_per_day = 24
                elif target_timeframe == '15T':
                    bars_per_day = 96
                else:
                    bars_per_day = 48  # Default to 30min
                
                # Create intraday price movement
                daily_range = row['high'] - row['low']
                price_increments = np.random.normal(0, daily_range/bars_per_day, bars_per_day)
                
                # Start from open price
                current_price = row['open']
                intraday_prices = [current_price]
                
                for increment in price_increments[:-1]:
                    current_price += increment
                    # Keep within daily range
                    current_price = max(row['low'], min(row['high'], current_price))
                    intraday_prices.append(current_price)
                
                # Ensure we end at close price
                intraday_prices[-1] = row['close']
                
                # Create intraday bars
                for i in range(bars_per_day):
                    bar_time = date + timedelta(minutes=i * (1440/bars_per_day))  # 1440 min per day
                    
                    if i == 0:
                        open_price = row['open']
                    else:
                        open_price = intraday_prices[i-1]
                    
                    close_price = intraday_prices[i]
                    
                    # Create realistic high/low for the bar
                    bar_range = daily_range / bars_per_day
                    high_price = max(open_price, close_price) + np.random.uniform(0, bar_range/2)
                    low_price = min(open_price, close_price) - np.random.uniform(0, bar_range/2)
                    
                    # Ensure within daily limits
                    high_price = min(high_price, row['high'])
                    low_price = max(low_price, row['low'])
                    
                    synthetic_data.append({
                        'datetime': bar_time,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': row['volume'] / bars_per_day
                    })
            
            synthetic_df = pd.DataFrame(synthetic_data)
            synthetic_df.set_index('datetime', inplace=True)
            
            print(f"Created {len(synthetic_df)} synthetic intraday bars")
            
            return synthetic_df
            
        except Exception as e:
            print(f"Error creating synthetic data: {e}")
            return daily_df
    
    def save_data(self, df: pd.DataFrame, filepath: str, format: str = 'csv'):
        """Save data to file"""
        try:
            if format.lower() == 'csv':
                df.to_csv(filepath)
            elif format.lower() == 'pickle':
                df.to_pickle(filepath)
            elif format.lower() == 'parquet':
                df.to_parquet(filepath)
            
            print(f"Data saved to {filepath}")
            
        except Exception as e:
            print(f"Error saving data: {e}")
    
    def load_data(self, filepath: str, format: str = 'csv') -> pd.DataFrame:
        """Load data from file"""
        try:
            if format.lower() == 'csv':
                df = pd.read_csv(filepath, index_col=0, parse_dates=True)
            elif format.lower() == 'pickle':
                df = pd.read_pickle(filepath)
            elif format.lower() == 'parquet':
                df = pd.read_parquet(filepath)
            
            print(f"Data loaded from {filepath}")
            return df
            
        except Exception as e:
            print(f"Error loading data: {e}")
            return pd.DataFrame()
    
    def get_cached_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Get cached data for symbol"""
        return self.data_cache.get(symbol)
    
    def list_cached_symbols(self) -> List[str]:
        """List all cached symbols"""
        return list(self.data_cache.keys())


def main():
    """Example usage of MT5DataImporter"""
    
    importer = MT5DataImporter()
    
    # Example 1: Load MT5 exported CSV
    print("=== Loading MT5 CSV Example ===")
    # mt5_data = importer.load_mt5_csv('eurusd_m30.csv', 'EURUSD')
    
    # Example 2: Download from Yahoo Finance
    print("=== Yahoo Finance Download Example ===")
    yahoo_data = importer.download_yahoo_finance(
        symbol='EURUSD=X',
        start_date='2023-01-01',
        end_date='2024-01-01',
        interval='1d'
    )
    
    if not yahoo_data.empty:
        # Create synthetic 30-minute data
        print("=== Creating Synthetic Intraday Data ===")
        synthetic_30m = importer.create_synthetic_intraday(yahoo_data, '30T')
        
        # Save for later use
        importer.save_data(synthetic_30m, 'eurusd_30m_synthetic.csv')
    
    # Example 3: Alpha Vantage (requires API key)
    print("=== Alpha Vantage Example (requires API key) ===")
    # api_key = 'YOUR_API_KEY_HERE'
    # av_data = importer.download_alpha_vantage('EURUSD', api_key, '30min')
    
    print("=== Cached Data ===")
    print(f"Cached symbols: {importer.list_cached_symbols()}")


if __name__ == "__main__":
    main()
