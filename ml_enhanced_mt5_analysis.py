#!/usr/bin/env python3
"""
ML-Enhanced MT5 Analysis Tool
Optimized for Apple M1 Max with Machine Learning Integration
Uses breakout success prediction with confidence-based filtering
"""

import numpy as np
import pandas as pd
import backtrader as bt
from datetime import datetime, time
import json
import os
import glob
import warnings
warnings.filterwarnings('ignore')

# ML imports optimized for M1 Max
try:
    # Use scikit-learn with optimized BLAS for M1
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import train_test_split, TimeSeriesSplit
    from sklearn.metrics import classification_report, confusion_matrix
    from sklearn.preprocessing import StandardScaler
    import joblib
    ML_AVAILABLE = True
except ImportError:
    print("⚠️ ML libraries not available. Install with: pip install scikit-learn")
    ML_AVAILABLE = False

# Technical analysis optimized for vectorized operations
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ TA-Lib not available. Using custom indicators.")

class OptimizedFeatureEngine:
    """Vectorized feature engineering optimized for M1 Max"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.feature_names = []
    
    def calculate_rsi(self, prices, period=14):
        """Optimized RSI calculation"""
        if TALIB_AVAILABLE:
            return talib.RSI(prices.values, timeperiod=period)
        else:
            # Vectorized RSI calculation
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))
    
    def calculate_bollinger_position(self, prices, period=20, std=2):
        """Bollinger Band position (0-1 scale)"""
        sma = prices.rolling(window=period).mean()
        std_dev = prices.rolling(window=period).std()
        upper = sma + (std_dev * std)
        lower = sma - (std_dev * std)
        return (prices - lower) / (upper - lower)
    
    def calculate_atr_percentile(self, high, low, close, period=14, lookback=100):
        """ATR percentile over lookback period"""
        if TALIB_AVAILABLE:
            atr = talib.ATR(high.values, low.values, close.values, timeperiod=period)
            atr_series = pd.Series(atr, index=close.index)
        else:
            # Manual ATR calculation
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr_series = tr.rolling(window=period).mean()
        
        return atr_series.rolling(window=lookback).rank(pct=True)
    
    def extract_features(self, data):
        """Extract all ML features efficiently"""
        print("🔧 Extracting ML features...")
        
        features = pd.DataFrame(index=data.index)
        
        # Price-based features (vectorized)
        features['close_sma_5'] = data['Close'].rolling(5).mean()
        features['close_sma_20'] = data['Close'].rolling(20).mean()
        features['close_sma_50'] = data['Close'].rolling(50).mean()
        
        # Technical indicators
        features['rsi_14'] = self.calculate_rsi(data['Close'], 14)
        features['rsi_21'] = self.calculate_rsi(data['Close'], 21)
        features['bb_position'] = self.calculate_bollinger_position(data['Close'])
        features['atr_percentile'] = self.calculate_atr_percentile(
            data['High'], data['Low'], data['Close']
        )
        
        # Volume features
        features['volume_sma'] = data['Volume'].rolling(20).mean()
        features['volume_ratio'] = data['Volume'] / features['volume_sma']
        features['volume_percentile'] = data['Volume'].rolling(100).rank(pct=True)
        
        # Price action features
        features['daily_range'] = (data['High'] - data['Low']) / data['Close']
        features['body_size'] = abs(data['Close'] - data['Open']) / data['Close']
        features['upper_shadow'] = (data['High'] - data[['Open', 'Close']].max(axis=1)) / data['Close']
        features['lower_shadow'] = (data[['Open', 'Close']].min(axis=1) - data['Low']) / data['Close']
        
        # Time-based features
        features['hour'] = data.index.hour
        features['day_of_week'] = data.index.dayofweek
        features['is_london_session'] = ((data.index.hour >= 8) & (data.index.hour <= 16)).astype(int)
        features['is_ny_session'] = ((data.index.hour >= 13) & (data.index.hour <= 21)).astype(int)
        features['is_overlap'] = ((data.index.hour >= 13) & (data.index.hour <= 16)).astype(int)
        
        # Momentum features
        features['price_change_1h'] = data['Close'].pct_change(2)  # 2 bars = 1 hour for M30
        features['price_change_4h'] = data['Close'].pct_change(8)  # 8 bars = 4 hours
        features['price_change_1d'] = data['Close'].pct_change(48)  # 48 bars = 1 day
        
        # Volatility features
        features['volatility_5'] = data['Close'].rolling(5).std()
        features['volatility_20'] = data['Close'].rolling(20).std()
        features['volatility_ratio'] = features['volatility_5'] / features['volatility_20']
        
        # Support/Resistance features
        features['distance_to_high_20'] = (data['High'].rolling(20).max() - data['Close']) / data['Close']
        features['distance_to_low_20'] = (data['Close'] - data['Low'].rolling(20).min()) / data['Close']
        
        # Remove NaN values
        features = features.dropna()
        
        self.feature_names = features.columns.tolist()
        print(f"✅ Extracted {len(self.feature_names)} features for {len(features)} samples")
        
        return features

class BreakoutLabelGenerator:
    """Generate labels for breakout success prediction"""
    
    def __init__(self, risk_reward_ratio=2.0, max_trade_hours=8):
        self.risk_reward_ratio = risk_reward_ratio
        self.max_trade_bars = max_trade_hours * 2  # 2 bars per hour for M30
    
    def identify_session_levels(self, data, session_start_hour=8):
        """Identify first hour high/low for each day"""
        session_levels = []
        
        # Group by date
        for date, day_data in data.groupby(data.index.date):
            # Find session start (8 AM)
            session_bars = day_data[
                (day_data.index.hour == session_start_hour) & 
                (day_data.index.minute.isin([0, 30]))
            ]
            
            if len(session_bars) >= 2:  # Need at least 2 bars for first hour
                first_hour = session_bars.iloc[:2]  # First hour (2 x 30min bars)
                session_high = first_hour['High'].max()
                session_low = first_hour['Low'].min()
                
                # Store session info
                for idx in day_data.index:
                    if idx >= first_hour.index[-1]:  # After first hour
                        session_levels.append({
                            'datetime': idx,
                            'session_high': session_high,
                            'session_low': session_low,
                            'range_pips': (session_high - session_low) * 10000
                        })
        
        return pd.DataFrame(session_levels).set_index('datetime')
    
    def generate_labels(self, data):
        """Generate breakout success labels"""
        print("🏷️ Generating breakout labels...")
        
        session_levels = self.identify_session_levels(data)
        labels = []
        
        for idx in session_levels.index:
            if idx not in data.index:
                continue
                
            current_idx = data.index.get_loc(idx)
            session_info = session_levels.loc[idx]
            
            # Skip if range too small
            if session_info['range_pips'] < 5:
                labels.append(-1)  # No trade
                continue
            
            # Look forward for breakout
            breakout_label = self.check_breakout_success(
                data, current_idx, session_info
            )
            labels.append(breakout_label)
        
        print(f"✅ Generated {len(labels)} labels")
        print(f"   Successful breakouts: {sum(1 for l in labels if l == 1)}")
        print(f"   Failed breakouts: {sum(1 for l in labels if l == 0)}")
        print(f"   No trades: {sum(1 for l in labels if l == -1)}")
        
        return pd.Series(labels, index=session_levels.index)
    
    def check_breakout_success(self, data, start_idx, session_info):
        """Check if breakout is successful within time limit"""
        session_high = session_info['session_high']
        session_low = session_info['session_low']
        
        # Define breakout levels
        buy_trigger = session_high + 0.0001
        sell_trigger = session_low - 0.0001
        
        # Look forward for breakout
        end_idx = min(start_idx + self.max_trade_bars, len(data))
        
        for i in range(start_idx, end_idx):
            current_bar = data.iloc[i]
            
            # Check for buy breakout
            if current_bar['High'] > buy_trigger:
                return self.check_trade_outcome(
                    data, i, 'buy', session_high, session_low
                )
            
            # Check for sell breakout
            elif current_bar['Low'] < sell_trigger:
                return self.check_trade_outcome(
                    data, i, 'sell', session_high, session_low
                )
        
        return -1  # No breakout occurred
    
    def check_trade_outcome(self, data, entry_idx, direction, session_high, session_low):
        """Check if trade reaches target before stop"""
        entry_price = data.iloc[entry_idx]['Close']
        
        if direction == 'buy':
            stop_loss = session_low - 0.0001
            take_profit = entry_price + (entry_price - stop_loss) * self.risk_reward_ratio
        else:  # sell
            stop_loss = session_high + 0.0001
            take_profit = entry_price - (stop_loss - entry_price) * self.risk_reward_ratio
        
        # Check outcome in following bars
        end_idx = min(entry_idx + self.max_trade_bars, len(data))
        
        for i in range(entry_idx + 1, end_idx):
            bar = data.iloc[i]
            
            if direction == 'buy':
                if bar['High'] >= take_profit:
                    return 1  # Successful
                elif bar['Low'] <= stop_loss:
                    return 0  # Failed
            else:  # sell
                if bar['Low'] <= take_profit:
                    return 1  # Successful
                elif bar['High'] >= stop_loss:
                    return 0  # Failed
        
        return 0  # Time limit reached without target

class MLBreakoutPredictor:
    """ML model for breakout success prediction"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
        self.is_trained = False
    
    def train(self, features, labels):
        """Train ML model with time series validation"""
        print("🧠 Training ML model...")
        
        # Remove no-trade samples for binary classification
        mask = labels != -1
        X = features[mask].copy()
        y = labels[mask].copy()
        
        print(f"Training samples: {len(X)} (Positive: {sum(y)}, Negative: {len(y) - sum(y)})")
        
        if len(X) < 100:
            print("❌ Insufficient training data. Need at least 100 samples.")
            return False
        
        # Time series split for validation
        tscv = TimeSeriesSplit(n_splits=3)
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train Random Forest optimized for M1 Max
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42,
            n_jobs=-1,  # Use all CPU cores
            class_weight='balanced'
        )
        
        # Cross-validation scores
        cv_scores = []
        for train_idx, val_idx in tscv.split(X_scaled):
            X_train, X_val = X_scaled[train_idx], X_scaled[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            self.model.fit(X_train, y_train)
            score = self.model.score(X_val, y_val)
            cv_scores.append(score)
        
        # Final training on all data
        self.model.fit(X_scaled, y)
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"✅ Model trained successfully!")
        print(f"   Cross-validation scores: {cv_scores}")
        print(f"   Average CV score: {np.mean(cv_scores):.3f} ± {np.std(cv_scores):.3f}")
        print(f"\n🔝 Top 5 Important Features:")
        for _, row in feature_importance.head().iterrows():
            print(f"   {row['feature']}: {row['importance']:.3f}")
        
        self.feature_names = X.columns.tolist()
        self.is_trained = True
        
        return True
    
    def predict_probability(self, features):
        """Predict breakout success probability"""
        if not self.is_trained:
            return 0.5  # Default probability
        
        # Ensure features match training
        features_aligned = features[self.feature_names]
        features_scaled = self.scaler.transform([features_aligned])
        
        # Return probability of successful breakout
        return self.model.predict_proba(features_scaled)[0][1]
    
    def save_model(self, filepath):
        """Save trained model"""
        if self.is_trained:
            joblib.dump({
                'model': self.model,
                'scaler': self.scaler,
                'feature_names': self.feature_names
            }, filepath)
            print(f"💾 Model saved to: {filepath}")
    
    def load_model(self, filepath):
        """Load trained model"""
        try:
            saved_data = joblib.load(filepath)
            self.model = saved_data['model']
            self.scaler = saved_data['scaler']
            self.feature_names = saved_data['feature_names']
            self.is_trained = True
            print(f"📂 Model loaded from: {filepath}")
            return True
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False

class MLEnhancedFirstCandleStrategy(bt.Strategy):
    """ML-Enhanced First Candle Strategy with confidence filtering"""

    params = (
        ('risk_percent', 1.0),
        ('risk_reward_ratio', 2.0),
        ('session_start_hour', 8),
        ('max_trade_hours', 8),
        ('min_range_pips', 5.0),
        ('ml_confidence_threshold', 0.65),  # Only trade if ML confidence > 65%
    )

    def __init__(self):
        self.ml_predictor = None
        self.feature_engine = OptimizedFeatureEngine()
        self.trades_log = []
        self.session_high = None
        self.session_low = None
        self.session_start = None
        self.trade_start_time = None
        self.ml_predictions = []

    def set_ml_predictor(self, predictor):
        """Set the trained ML predictor"""
        self.ml_predictor = predictor

    def extract_current_features(self):
        """Extract features for current bar"""
        # Get recent data for feature calculation
        try:
            recent_data = []
            for i in range(min(100, len(self.data))):
                recent_data.append([
                    self.data.open[-i],
                    self.data.high[-i],
                    self.data.low[-i],
                    self.data.close[-i],
                    self.data.volume[-i] if hasattr(self.data, 'volume') else 1000
                ])

            if len(recent_data) < 50:
                return None

            # Convert to DataFrame
            df = pd.DataFrame(recent_data[::-1], columns=['Open', 'High', 'Low', 'Close', 'Volume'])

            # Add datetime index
            current_time = self.data.datetime.datetime()
            df.index = pd.date_range(end=current_time, periods=len(df), freq='30T')

            # Extract features
            features = self.feature_engine.extract_features(df)

            if len(features) > 0:
                return features.iloc[-1]  # Return latest features
            return None
        except Exception as e:
            print(f"Error extracting features: {e}")
            return None

    def next(self):
        current_time = self.data.datetime.time()
        current_hour = current_time.hour

        # Session detection logic (same as original)
        if current_hour == self.params.session_start_hour and current_time.minute == 0:
            self.session_start = len(self.data)
            self.session_high = self.data.high[0]
            self.session_low = self.data.low[0]

        elif (self.session_start and
              len(self.data) - self.session_start <= 2 and
              current_hour == self.params.session_start_hour):
            self.session_high = max(self.session_high, self.data.high[0])
            self.session_low = min(self.session_low, self.data.low[0])

        # ML-enhanced trade decision
        elif (self.session_start and
              len(self.data) - self.session_start == 3 and
              not self.position):

            range_pips = (self.session_high - self.session_low) * 10000

            if range_pips > self.params.min_range_pips:
                # Extract current features for ML prediction
                current_features = self.extract_current_features()

                if current_features is not None and self.ml_predictor and self.ml_predictor.is_trained:
                    # Get ML prediction
                    ml_confidence = self.ml_predictor.predict_probability(current_features)
                    self.ml_predictions.append(ml_confidence)

                    # Only trade if ML confidence is high enough
                    if ml_confidence >= self.params.ml_confidence_threshold:
                        self.execute_breakout_trade(ml_confidence)
                    else:
                        # Log skipped trade
                        self.trades_log.append({
                            'action': 'skipped',
                            'ml_confidence': ml_confidence,
                            'threshold': self.params.ml_confidence_threshold,
                            'range_pips': range_pips
                        })
                else:
                    # Fallback to original logic if ML not available
                    self.execute_breakout_trade(0.5)  # Default confidence

        # Position management (same as original)
        if self.position:
            if hasattr(self, 'trade_start_time') and self.trade_start_time:
                hours_in_trade = (self.data.datetime.datetime() - self.trade_start_time).total_seconds() / 3600
                if hours_in_trade >= self.params.max_trade_hours:
                    self.close()
                    return

            if self.position.size > 0:  # Long position
                if (self.data.low[0] <= self.buy_stop_price or
                    self.data.high[0] >= self.buy_target_price):
                    self.close()
            else:  # Short position
                if (self.data.high[0] >= self.sell_stop_price or
                    self.data.low[0] <= self.sell_target_price):
                    self.close()

    def execute_breakout_trade(self, ml_confidence):
        """Execute breakout trade with ML confidence logging"""
        buy_price = self.session_high + 0.0001
        sell_price = self.session_low - 0.0001

        if self.data.close[0] > buy_price:
            self.buy(size=1000)
            self.buy_stop_price = self.session_low - 0.0001
            self.buy_target_price = buy_price + (buy_price - self.buy_stop_price) * self.params.risk_reward_ratio
            self.trade_start_time = self.data.datetime.datetime()

            self.trades_log.append({
                'action': 'buy',
                'entry_price': buy_price,
                'ml_confidence': ml_confidence,
                'range_pips': (self.session_high - self.session_low) * 10000
            })

        elif self.data.close[0] < sell_price:
            self.sell(size=1000)
            self.sell_stop_price = self.session_high + 0.0001
            self.sell_target_price = sell_price - (self.sell_stop_price - sell_price) * self.params.risk_reward_ratio
            self.trade_start_time = self.data.datetime.datetime()

            self.trades_log.append({
                'action': 'sell',
                'entry_price': sell_price,
                'ml_confidence': ml_confidence,
                'range_pips': (self.session_high - self.session_low) * 10000
            })

def find_mt5_csv_files():
    """Find MT5 CSV files in current directory"""
    patterns = ["EURUSD_M30_*.csv", "EURUSD*.csv", "*M30*.csv", "*EURUSD*.csv"]
    found_files = []
    for pattern in patterns:
        files = glob.glob(pattern)
        found_files.extend(files)

    unique_files = []
    for file in found_files:
        if file not in unique_files and 'cleaned' not in file.lower():
            unique_files.append(file)

    return unique_files

def convert_mt5_csv(filename):
    """Convert MT5 CSV to standard format (same as original)"""
    print(f"🔧 Converting {filename} to standard format...")

    try:
        df = pd.read_csv(filename, sep='\t')

        column_mapping = {
            '<DATE>': 'Date', '<TIME>': 'Time', '<OPEN>': 'Open',
            '<HIGH>': 'High', '<LOW>': 'Low', '<CLOSE>': 'Close',
            '<TICKVOL>': 'Volume', '<VOL>': 'RealVolume', '<SPREAD>': 'Spread'
        }

        df = df.rename(columns=column_mapping)
        df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
        df.set_index('DateTime', inplace=True)

        df_clean = df[['Open', 'High', 'Low', 'Close', 'Volume']].copy()
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')

        df_clean = df_clean.dropna()

        cleaned_filename = filename.replace('.csv', '_cleaned.csv')
        df_clean.to_csv(cleaned_filename)

        print(f"✅ Cleaned data: {len(df_clean)} bars")
        print(f"💾 Saved cleaned file as: {cleaned_filename}")

        return df_clean, cleaned_filename

    except Exception as e:
        print(f"❌ Error converting file: {e}")
        return None, None

def run_ml_enhanced_backtest(data, ml_predictor=None, params=None):
    """Run backtest with ML enhancement"""
    if params is None:
        params = {
            'risk_percent': 1.0,
            'risk_reward_ratio': 2.0,
            'session_start_hour': 8,
            'max_trade_hours': 8,
            'min_range_pips': 5.0,
            'ml_confidence_threshold': 0.65
        }

    print("🧠 Running ML-enhanced backtest...")

    cerebro = bt.Cerebro()
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)

    # Add ML-enhanced strategy
    cerebro.addstrategy(MLEnhancedFirstCandleStrategy, **params)

    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.0001)

    # Add analyzers
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')

    print("Starting capital: " + str(cerebro.broker.getvalue()))

    # Run backtest
    results = cerebro.run()
    strategy = results[0]

    # Set ML predictor after initialization
    if ml_predictor:
        strategy.set_ml_predictor(ml_predictor)

    final_value = cerebro.broker.getvalue()

    # Extract results
    trade_analyzer = strategy.analyzers.trades.get_analysis()
    sharpe_analyzer = strategy.analyzers.sharpe.get_analysis()
    drawdown_analyzer = strategy.analyzers.drawdown.get_analysis()

    # Calculate metrics
    total_return = (final_value - 10000) / 10000 * 100
    total_trades = trade_analyzer.get('total', {}).get('total', 0)
    winning_trades = trade_analyzer.get('won', {}).get('total', 0)
    losing_trades = trade_analyzer.get('lost', {}).get('total', 0)
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    max_drawdown = drawdown_analyzer.get('max', {}).get('drawdown', 0)
    sharpe_ratio = sharpe_analyzer.get('sharperatio', 0) or 0

    # ML-specific metrics
    ml_predictions = strategy.ml_predictions
    avg_ml_confidence = np.mean(ml_predictions) if ml_predictions else 0

    return {
        'total_return': total_return,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'final_value': final_value,
        'parameters': params,
        'ml_metrics': {
            'avg_confidence': avg_ml_confidence,
            'predictions_count': len(ml_predictions),
            'trades_log': strategy.trades_log
        }
    }

def save_ml_results(results, data, ml_predictor=None):
    """Save ML-enhanced results"""
    os.makedirs('backtesting/results', exist_ok=True)
    os.makedirs('backtesting/exports', exist_ok=True)
    os.makedirs('backtesting/models', exist_ok=True)

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Save results
    result_file = f'backtesting/results/ml_enhanced_analysis_{timestamp}.json'

    save_data = {
        'strategy': 'ML_Enhanced_FirstCandleBreakout',
        'symbol': 'EURUSD',
        'data_period': f'{data.index[0].date()} to {data.index[-1].date()}',
        'timestamp': timestamp,
        'results': results,
        'ml_model_info': {
            'model_trained': ml_predictor.is_trained if ml_predictor else False,
            'feature_count': len(ml_predictor.feature_names) if ml_predictor and ml_predictor.is_trained else 0
        }
    }

    with open(result_file, 'w') as f:
        json.dump(save_data, f, indent=2, default=str)

    # Save ML model
    if ml_predictor and ml_predictor.is_trained:
        model_file = f'backtesting/models/ml_model_{timestamp}.joblib'
        ml_predictor.save_model(model_file)

    # Create MT5 parameters
    mt5_file = f'backtesting/exports/ml_enhanced_mt5_params_{timestamp}.txt'

    with open(mt5_file, 'w') as f:
        f.write('// ML-Enhanced First Candle Breakout EA Parameters\\n')
        f.write(f'// Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\\n')
        f.write(f'// Total Return: {results["total_return"]:.2f}%\\n')
        f.write(f'// Win Rate: {results["win_rate"]:.1f}%\\n')
        f.write(f'// ML Avg Confidence: {results["ml_metrics"]["avg_confidence"]:.3f}\\n\\n')

        params = results['parameters']
        f.write('input double RiskPercent = ' + str(params['risk_percent']) + ';\\n')
        f.write('input double RiskRewardRatio = ' + str(params['risk_reward_ratio']) + ';\\n')
        f.write('input int SessionStartHour = ' + str(params['session_start_hour']) + ';\\n')
        f.write('input int MaxTradeHours = ' + str(params['max_trade_hours']) + ';\\n')
        f.write('input double MinRangePips = ' + str(params['min_range_pips']) + ';\\n')
        f.write('input double MLConfidenceThreshold = ' + str(params['ml_confidence_threshold']) + ';\\n')

    print(f"💾 Results saved to: {result_file}")
    print(f"💾 MT5 parameters saved to: {mt5_file}")

    return result_file, mt5_file

def main():
    """Main function - ML-Enhanced MT5 Analysis"""

    print("🧠 ML-Enhanced MT5 Analysis Tool (Optimized for M1 Max)")
    print("=" * 60)
    print("This tool will:")
    print("1. Find and convert your MT5 CSV files")
    print("2. Extract ML features from price data")
    print("3. Train ML model to predict breakout success")
    print("4. Run ML-enhanced backtesting")
    print("5. Export optimized parameters for MT5")
    print()

    if not ML_AVAILABLE:
        print("❌ ML libraries not available. Please install:")
        print("   pip install scikit-learn joblib")
        return False

    # Step 1: Find and convert CSV files
    csv_files = find_mt5_csv_files()

    if not csv_files:
        print("❌ No MT5 CSV files found in current directory")
        return False

    print(f"✅ Found {len(csv_files)} CSV files:")
    for i, file in enumerate(csv_files):
        file_size = os.path.getsize(file) / (1024 * 1024)
        print(f"  {i+1}. {file} ({file_size:.1f} MB)")

    selected_file = csv_files[0]
    if len(csv_files) > 1:
        try:
            choice = input(f"\\nSelect file (1-{len(csv_files)}, default=1): ").strip()
            if choice and choice.isdigit():
                file_index = int(choice) - 1
                if 0 <= file_index < len(csv_files):
                    selected_file = csv_files[file_index]
        except:
            pass

    print(f"\\n📁 Using file: {selected_file}")

    # Step 2: Convert CSV
    data, cleaned_filename = convert_mt5_csv(selected_file)
    if data is None:
        return False

    print(f"✅ Data conversion successful!")
    print(f"   Bars: {len(data)}")
    print(f"   Period: {data.index[0].date()} to {data.index[-1].date()}")

    # Step 3: ML Feature Engineering and Training
    print("\\n🧠 Step 1: ML Model Training...")

    feature_engine = OptimizedFeatureEngine()
    label_generator = BreakoutLabelGenerator()
    ml_predictor = MLBreakoutPredictor()

    try:
        # Extract features
        features = feature_engine.extract_features(data)

        # Generate labels
        labels = label_generator.generate_labels(data)

        # Align features and labels
        common_index = features.index.intersection(labels.index)
        if len(common_index) < 100:
            print("❌ Insufficient aligned data for ML training")
            print("Falling back to non-ML backtest...")
            ml_predictor = None
        else:
            features_aligned = features.loc[common_index]
            labels_aligned = labels.loc[common_index]

            # Train ML model
            success = ml_predictor.train(features_aligned, labels_aligned)
            if not success:
                print("❌ ML training failed. Falling back to non-ML backtest...")
                ml_predictor = None

    except Exception as e:
        print(f"❌ Error in ML training: {e}")
        print("Falling back to non-ML backtest...")
        ml_predictor = None

    # Step 4: Run ML-Enhanced Backtest
    print("\\n📊 Step 2: ML-Enhanced Backtesting...")

    # Test different confidence thresholds
    confidence_thresholds = [0.5, 0.6, 0.65, 0.7, 0.75] if ml_predictor else [0.5]
    best_result = None
    best_threshold = 0.5

    for threshold in confidence_thresholds:
        params = {
            'risk_percent': 1.0,
            'risk_reward_ratio': 2.0,
            'session_start_hour': 8,
            'max_trade_hours': 8,
            'min_range_pips': 5.0,
            'ml_confidence_threshold': threshold
        }

        try:
            result = run_ml_enhanced_backtest(data, ml_predictor, params)

            print(f"\\n📊 Results with ML confidence threshold {threshold}:")
            print(f"   Total Return: {result['total_return']:.2f}%")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Total Trades: {result['total_trades']}")
            print(f"   Sharpe Ratio: {result['sharpe_ratio']:.2f}")

            if ml_predictor:
                ml_metrics = result['ml_metrics']
                print(f"   Avg ML Confidence: {ml_metrics['avg_confidence']:.3f}")
                print(f"   ML Predictions: {ml_metrics['predictions_count']}")

            # Track best result
            if best_result is None or result['sharpe_ratio'] > best_result['sharpe_ratio']:
                best_result = result
                best_threshold = threshold

        except Exception as e:
            print(f"❌ Error in backtest with threshold {threshold}: {e}")
            continue

    if best_result is None:
        print("❌ All backtests failed")
        return False

    # Step 5: Display Best Results
    print("\\n🏆 BEST ML-ENHANCED RESULTS:")
    print("=" * 50)
    print(f"Best ML Confidence Threshold: {best_threshold}")
    print(f"Total Return: {best_result['total_return']:.2f}%")
    print(f"Win Rate: {best_result['win_rate']:.1f}%")
    print(f"Total Trades: {best_result['total_trades']}")
    print(f"Winning Trades: {best_result['winning_trades']}")
    print(f"Losing Trades: {best_result['losing_trades']}")
    print(f"Max Drawdown: {best_result['max_drawdown']:.2f}%")
    print(f"Sharpe Ratio: {best_result['sharpe_ratio']:.2f}")

    if ml_predictor and ml_predictor.is_trained:
        ml_metrics = best_result['ml_metrics']
        print(f"\\n🧠 ML Performance:")
        print(f"Average ML Confidence: {ml_metrics['avg_confidence']:.3f}")
        print(f"ML Predictions Made: {ml_metrics['predictions_count']}")

        # Analyze trade decisions
        trades_taken = [t for t in ml_metrics['trades_log'] if t['action'] in ['buy', 'sell']]
        trades_skipped = [t for t in ml_metrics['trades_log'] if t['action'] == 'skipped']

        print(f"Trades Taken: {len(trades_taken)}")
        print(f"Trades Skipped: {len(trades_skipped)}")

        if trades_taken:
            avg_confidence_taken = np.mean([t['ml_confidence'] for t in trades_taken])
            print(f"Avg Confidence (Taken): {avg_confidence_taken:.3f}")

        if trades_skipped:
            avg_confidence_skipped = np.mean([t['ml_confidence'] for t in trades_skipped])
            print(f"Avg Confidence (Skipped): {avg_confidence_skipped:.3f}")

    # Step 6: Save Results
    print("\\n💾 Step 3: Saving Results...")

    result_file, mt5_file = save_ml_results(best_result, data, ml_predictor)

    # Step 7: Summary
    print("\\n🎉 ML-ENHANCED ANALYSIS COMPLETE!")
    print("=" * 60)
    print("📁 Files created:")
    print(f"   Results: {result_file}")
    print(f"   MT5 Parameters: {mt5_file}")
    print(f"   Cleaned Data: {cleaned_filename}")

    if ml_predictor and ml_predictor.is_trained:
        print(f"   ML Model: backtesting/models/ml_model_*.joblib")

    print("\\n📊 Next Steps:")
    print("1. Review ML model performance in results file")
    print("2. Copy parameters to your MT5 EA")
    print("3. Implement ML confidence filtering in EA")
    print("4. Test on demo account with paper trading")
    print("5. Monitor ML model accuracy over time")

    print("\\n🧠 ML Enhancement Benefits:")
    if ml_predictor and ml_predictor.is_trained:
        print("✅ Breakout success prediction trained")
        print("✅ Confidence-based trade filtering")
        print("✅ Reduced false breakouts")
        print("✅ Improved risk-adjusted returns")
    else:
        print("⚠️  ML training failed - using traditional approach")
        print("💡 Try with more data or different parameters")

    return True

if __name__ == "__main__":
    main()
