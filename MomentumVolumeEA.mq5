//+------------------------------------------------------------------+
//|                                            MomentumVolumeEA.mq5 |
//|                                     Momentum-Volume Strategy EA |
//|                              Designed for small accounts ≤$1000 |
//+------------------------------------------------------------------+
#property copyright "Momentum Volume Strategy"
#property version   "1.10" // Corrected Version
#property strict

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parameters
// Strategy Settings
input int      VWMA_Period = 21;           // VWMA Period
input int      RSI_Period = 14;            // RSI Period for fusion
input int      MFI_Period = 14;            // Money Flow Index Period
input int      VROC_Period = 10;           // Volume Rate of Change Period
input double   VROC_Threshold = 20.0;      // VROC threshold %

// Risk Management
input double   RiskPercent = 2.0;          // Risk per trade %
input double   DailyLossLimit = 6.0;       // Daily loss limit %
input double   WeeklyDrawdownLimit = 10.0; // Weekly drawdown limit %
input int      MaxPositions = 2;           // Maximum concurrent positions
input double   MinRiskReward = 1.5;        // Minimum Risk:Reward ratio
input int      MaxStopLoss = 15;           // Maximum stop loss in pips

// Position Sizing
input double   EURUSD_LotSize = 0.02;      // EUR/USD lot size
input double   GBPUSD_LotSize = 0.02;      // GBP/USD lot size  
input double   USDJPY_LotSize = 0.015;     // USD/JPY lot size
input double   AUDUSD_LotSize = 0.025;     // AUD/USD lot size

// Trading Sessions
input bool     TradeAsianSession = false;   // Trade Asian session
input bool     TradeLondonSession = true;   // Trade London session
input bool     TradeNYSession = true;       // Trade NY session
input bool     TradeOverlapOnly = true;     // Trade overlap sessions only

// EA Settings
input int      MagicNumber = 12345;         // Magic number
input string   TradeComment = "MomVol_EA";  // Trade comment
input bool     EnableLogging = true;        // Enable detailed logging

// Dashboard Settings
input bool   ShowDashboard = true;          // Show visual dashboard
input int    Corner = 0;                    // 0-3 for chart corner
input int    X_Offset = 10;                 // X offset from corner
input int    Y_Offset = 20;                 // Y offset from corner
input color  BackgroundColor = clrBlack;
input color  TextColor = clrWhite;
input color  BullishColor = clrLimeGreen;
input color  BearishColor = clrRed;
input color  NeutralColor = clrGray;

//--- Global variables
double g_AccountBalance;
double g_DailyStartBalance;
double g_WeeklyStartBalance;
int g_CurrentPositions;
datetime g_LastTradeTime;
datetime g_DayStart;
datetime g_WeekStart;

//--- Indicator handles
int h_RSI;
int h_MFI;
int h_Volume;

//--- Arrays for indicator values
double RSI_Buffer[];
double MFI_Buffer[];

//--- Current indicator values
double g_CurrentVWMA_RSI;
double g_CurrentMFI;
double g_CurrentVROC;

// Forward declarations
double CalculateStopLoss(ENUM_ORDER_TYPE orderType, double price);
double CalculateTakeProfit(ENUM_ORDER_TYPE orderType, double price, double sl);
double GetLotSize();

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize global variables
    g_AccountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    g_DailyStartBalance = g_AccountBalance;
    g_WeeklyStartBalance = g_AccountBalance;
    g_CurrentPositions = 0;
    g_DayStart = TimeCurrent();
    g_WeekStart = TimeCurrent();
    
    // Initialize indicator handles
    h_RSI = iRSI(_Symbol, PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);
    h_MFI = iMFI(_Symbol, PERIOD_CURRENT, MFI_Period, VOLUME_TICK);
    h_Volume = iVolumes(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
    
    if(h_RSI == INVALID_HANDLE || h_MFI == INVALID_HANDLE || h_Volume == INVALID_HANDLE)
    {
        Print("Error creating indicator handles");
        return INIT_FAILED;
    }
    
    // Set array properties
    ArraySetAsSeries(RSI_Buffer, true);
    ArraySetAsSeries(MFI_Buffer, true);
    
    if(EnableLogging)
    {
        Print("MomentumVolumeEA initialized successfully");
        long allowed_filling = SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);
        Print("Allowed Filling Modes for ", _Symbol, ": ", allowed_filling);
    }
    
    if(ShowDashboard)
        CreateDashboard();
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if(h_RSI != INVALID_HANDLE) IndicatorRelease(h_RSI);
    if(h_MFI != INVALID_HANDLE) IndicatorRelease(h_MFI);
    if(h_Volume != INVALID_HANDLE) IndicatorRelease(h_Volume);
    
    if(EnableLogging)
        Print("MomentumVolumeEA deinitialized");
        
    if(ShowDashboard)
        DeleteDashboard();
}

//+------------------------------------------------------------------+
//| Normalize price to tick size                                     |
//+------------------------------------------------------------------+
double NormalizeToTickSize(double price)
{
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    if(tickSize == 0) tickSize = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    return NormalizeDouble(MathRound(price / tickSize) * tickSize, (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS));
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(ShowDashboard)
        UpdateDashboard();

    // Check if new bar formed
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    if(currentBarTime == lastBarTime)
        return;
    lastBarTime = currentBarTime;
    
    // Update daily and weekly tracking
    UpdateTimeFrameTracking();
    
    // Check risk management limits
    if(!CheckRiskLimits())
        return;
    
    // Update current positions count
    UpdatePositionCount();
    
    // Check if we can open new positions
    if(g_CurrentPositions >= MaxPositions)
        return;
    
    // Check trading session
    if(!IsValidTradingSession())
        return;
    
    // Get indicator values
    if(!GetIndicatorValues())
        return;
    
    // Check for trading signals
    if(Bars(_Symbol, PERIOD_CURRENT) > MathMax(VWMA_Period, MathMax(RSI_Period, MFI_Period)) + 50)
        CheckTradingSignals();
    
    // Manage existing positions
    ManageExistingPositions();
}

//+------------------------------------------------------------------+
//| Calculate Volume Weighted Moving Average with RSI fusion       |
//+------------------------------------------------------------------+
double CalculateVWMA_RSI(int shift)
{
    if(Bars(_Symbol, PERIOD_CURRENT) < shift + VWMA_Period + 10)
        return 50.0;
    
    double vwma = 0.0;
    double volumeSum = 0.0;
    
    double volumeArray[];
    ArraySetAsSeries(volumeArray, true);
    if(CopyBuffer(h_Volume, 0, shift, VWMA_Period, volumeArray) < VWMA_Period)
        return 50.0;
    
    for(int i = 0; i < VWMA_Period; i++)
    {
        double price = iClose(_Symbol, PERIOD_CURRENT, shift + i);
        double volume = volumeArray[i];
        vwma += price * volume;
        volumeSum += volume;
    }
    
    if(volumeSum > 0)
        vwma = vwma / volumeSum;
    else
        vwma = iClose(_Symbol, PERIOD_CURRENT, shift);
    
    double rsi = 0.0;
    double rsiArray[];
    ArraySetAsSeries(rsiArray, true);
    if(CopyBuffer(h_RSI, 0, shift, 1, rsiArray) > 0)
        rsi = rsiArray[0];
    
    double currentPrice = iClose(_Symbol, PERIOD_CURRENT, shift);
    double vwmaPosition = 0.0;
    
    if(vwma != 0)
        vwmaPosition = ((currentPrice - vwma) / vwma) * 1000 + 50;
    
    double fusion = (vwmaPosition * 0.7) + (rsi * 0.3);
    
    return MathMax(0, MathMin(100, fusion));
}

//+------------------------------------------------------------------+
//| Calculate Volume Rate of Change                                 |
//+------------------------------------------------------------------+
double CalculateVROC(int shift)
{
    if(Bars(_Symbol, PERIOD_CURRENT) < shift + VROC_Period + 10)
        return 0.0;
        
    double volumeArray[];
    ArraySetAsSeries(volumeArray, true);
    
    if(CopyBuffer(h_Volume, 0, shift, VROC_Period + 1, volumeArray) <= VROC_Period)
        return 0.0;
    
    double currentVolume = volumeArray[0];
    double previousVolume = volumeArray[VROC_Period];
    
    if(previousVolume == 0)
        return 0.0;
    
    return ((currentVolume - previousVolume) / previousVolume) * 100.0;
}

//+------------------------------------------------------------------+
//| Get all indicator values                                         |
//+------------------------------------------------------------------+
bool GetIndicatorValues()
{
    if(CopyBuffer(h_MFI, 0, 1, 1, MFI_Buffer) <= 0)
        return false;
    
    g_CurrentVWMA_RSI = CalculateVWMA_RSI(1);
    g_CurrentVROC = CalculateVROC(1);
    g_CurrentMFI = MFI_Buffer[0];
    
    return true;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    if(g_CurrentVWMA_RSI > 50 && g_CurrentMFI > 50 && g_CurrentVROC > VROC_Threshold)
    {
        if(IsConfluenceZone(ORDER_TYPE_BUY))
        {
            OpenPosition(ORDER_TYPE_BUY);
        }
    }
    
    if(g_CurrentVWMA_RSI < 50 && g_CurrentMFI < 50 && g_CurrentVROC > VROC_Threshold)
    {
        if(IsConfluenceZone(ORDER_TYPE_SELL))
        {
            OpenPosition(ORDER_TYPE_SELL);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if price is at confluence zone                            |
//+------------------------------------------------------------------+
bool IsConfluenceZone(ENUM_ORDER_TYPE orderType)
{
    double currentPrice = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    double vwma = 0.0;
    double volumeSum = 0.0;
    
    double volumeArray[];
    ArraySetAsSeries(volumeArray, true);
    if(CopyBuffer(h_Volume, 0, 1, VWMA_Period, volumeArray) < VWMA_Period)
        return false;
    
    for(int i = 0; i < VWMA_Period; i++)
    {
        double price = iClose(_Symbol, PERIOD_CURRENT, i + 1);
        double volume = volumeArray[i];
        vwma += price * volume;
        volumeSum += volume;
    }
    
    if(volumeSum > 0)
        vwma = vwma / volumeSum;
    else
        return false;
    
    double distance = MathAbs(currentPrice - vwma);
    int h_ATR = iATR(_Symbol, PERIOD_CURRENT, 14);
    double atrArray[];
    ArraySetAsSeries(atrArray, true);
    double atr = 0;
    if(CopyBuffer(h_ATR, 0, 1, 1, atrArray) > 0)
        atr = atrArray[0];
    IndicatorRelease(h_ATR);
    
    return (distance <= atr * 0.5);
}

//+------------------------------------------------------------------+
//| Open new position                                               |
//+------------------------------------------------------------------+
void OpenPosition(ENUM_ORDER_TYPE orderType)
{
    double lotSize = GetLotSize();
    if(lotSize == 0) return;
    
    double price = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = CalculateStopLoss(orderType, price);
    double tp = CalculateTakeProfit(orderType, price, sl);
    
    if(EnableLogging)
    {
        Print("--- Preparing to Open ", EnumToString(orderType), " Trade ---");
        Print("Entry Price: ", price);
        Print("Calculated SL: ", sl, " | Calculated TP: ", tp);
        double stop_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
        Print("Broker Minimum Stop Distance: ", stop_level, " points");
        Print("Calculated SL Distance: ", MathAbs(price - sl), " points");
    }
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = orderType;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = MagicNumber;
    request.comment = TradeComment;
    request.deviation = 3;

    //--- Determine and set the correct filling policy
    long filling_mode = SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);
    if(filling_mode == SYMBOL_FILLING_FOK)
        request.type_filling = ORDER_FILLING_FOK;
    else if(filling_mode == SYMBOL_FILLING_IOC)
        request.type_filling = ORDER_FILLING_IOC;
    else
        request.type_filling = ORDER_FILLING_RETURN;

    if(EnableLogging) Print("Using Filling Mode: ", request.type_filling);

    //--- Send the request once with the correct policy
    if(!OrderSend(request, result))
    {
        if(EnableLogging)
            Print("OrderSend failed. Error: ", result.retcode, " - ", result.comment);
    }
    else
    {
        if(result.retcode == TRADE_RETCODE_DONE || result.retcode == TRADE_RETCODE_PLACED)
        {
            if(EnableLogging)
                Print("Position opened successfully: ", EnumToString(orderType), " at ", price);
        }
        else
        {
            if(EnableLogging)
                Print("OrderSend executed but returned code: ", result.retcode, " - ", result.comment);
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate stop loss                                              |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE orderType, double price)
{
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double stops_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * point;
    
    //--- Calculate SL based on MaxStopLoss input
    double sl_distance = MaxStopLoss * point * 10; // Convert pips to points for 5-digit brokers

    //--- Ensure SL distance is at least the minimum required by the broker + 1 pip buffer
    double min_sl_distance = stops_level + (1 * point * 10);
    if(sl_distance < min_sl_distance)
    {
        sl_distance = min_sl_distance;
    }

    double sl = 0;
    if(orderType == ORDER_TYPE_BUY)
        sl = price - sl_distance;
    else
        sl = price + sl_distance;
        
    return NormalizeToTickSize(sl);
}

//+------------------------------------------------------------------+
//| Calculate take profit                                            |
//+------------------------------------------------------------------+
double CalculateTakeProfit(ENUM_ORDER_TYPE orderType, double price, double sl)
{
    double slDistance = MathAbs(price - sl);
    double tpDistance = slDistance * MinRiskReward;
    
    double tp = 0;
    if(orderType == ORDER_TYPE_BUY)
        tp = price + tpDistance;
    else
        tp = price - tpDistance;
        
    return NormalizeToTickSize(tp);
}

//+------------------------------------------------------------------+
//| Get lot size for the current symbol                              |
//+------------------------------------------------------------------+
double GetLotSize()
{
    string symbol = _Symbol;
    if(symbol == "EURUSD") return EURUSD_LotSize;
    if(symbol == "GBPUSD") return GBPUSD_LotSize;
    if(symbol == "USDJPY") return USDJPY_LotSize;
    if(symbol == "AUDUSD") return AUDUSD_LotSize;
    return 0.01; // Default
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManageExistingPositions()
{
    // Trailing stop logic can be added here if needed
}

//+------------------------------------------------------------------+
//| Update time frame tracking                                       |
//+------------------------------------------------------------------+
void UpdateTimeFrameTracking()
{
    MqlDateTime now, day_start, week_start;
    TimeCurrent(now);
    TimeToStruct(g_DayStart, day_start);
    TimeToStruct(g_WeekStart, week_start);
    
    if(now.day != day_start.day)
    {
        g_DailyStartBalance = g_AccountBalance;
        g_DayStart = TimeCurrent();
    }
    
    if(now.day_of_week < week_start.day_of_week)
    {
        g_WeeklyStartBalance = g_AccountBalance;
        g_WeekStart = TimeCurrent();
    }
    
    g_AccountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
}

//+------------------------------------------------------------------+
//| Check risk limits                                                |
//+------------------------------------------------------------------+
bool CheckRiskLimits()
{
    double dailyLoss = g_DailyStartBalance - g_AccountBalance;
    if(dailyLoss > g_DailyStartBalance * (DailyLossLimit / 100.0))
    {
        if(EnableLogging)
            Print("Daily loss limit reached. No new trades allowed.");
        return false;
    }
    
    double weeklyLoss = g_WeeklyStartBalance - g_AccountBalance;
    if(weeklyLoss > g_WeeklyStartBalance * (WeeklyDrawdownLimit / 100.0))
    {
        if(EnableLogging)
            Print("Weekly drawdown limit reached. No new trades allowed.");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update current position count                                    |
//+------------------------------------------------------------------+
void UpdatePositionCount()
{
    g_CurrentPositions = PositionsTotal();
}

//+------------------------------------------------------------------+
//| Check if current time is within a valid trading session          |
//+------------------------------------------------------------------+
bool IsValidTradingSession()
{
    MqlDateTime time;
    TimeCurrent(time);
    
    int hour = time.hour;
    
    bool isAsian = (hour >= 0 && hour < 8);
    bool isLondon = (hour >= 8 && hour < 16);
    bool isNY = (hour >= 13 && hour < 21);
    
    if(TradeOverlapOnly)
    {
        bool isLondonNYOverlap = (hour >= 13 && hour < 16);
        return isLondonNYOverlap;
    }
    
    if(TradeAsianSession && isAsian) return true;
    if(TradeLondonSession && isLondon) return true;
    if(TradeNYSession && isNY) return true;
    
    return false;
}
//+------------------------------------------------------------------+
//| Dashboard Functions                                              |
//+------------------------------------------------------------------+
void CreateDashboard()
{
    //--- Create background
    ObjectCreate(0, "Dashboard_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_CORNER, Corner);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_XDISTANCE, X_Offset);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_YDISTANCE, Y_Offset);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_XSIZE, 250);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_YSIZE, 150);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_BGCOLOR, BackgroundColor);
    ObjectSetInteger(0, "Dashboard_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);

    //--- Create text labels (placeholders for now)
    string labels[] = {"Title", "Risk_Header", "Daily_PL", "Weekly_DD", "Market_Header", "VWMA_RSI", "MFI", "VROC", "EA_Header", "Status", "Positions"};
    string texts[] = {"MomentumVolumeEA v1.10", "--- Risk Status ---", "Daily P/L:", "Weekly DD:", "--- Market Status ---", "VWMA-RSI:", "MFI:", "VROC:", "--- EA Status ---", "Status:", "Positions:"};

    for(int i=0; i<ArraySize(labels); i++)
    {
        ObjectCreate(0, "Label_"+labels[i], OBJ_LABEL, 0, 0, 0);
        ObjectSetString(0, "Label_"+labels[i], OBJPROP_TEXT, texts[i]);
        ObjectSetInteger(0, "Label_"+labels[i], OBJPROP_CORNER, Corner);
        ObjectSetInteger(0, "Label_"+labels[i], OBJPROP_XDISTANCE, X_Offset + 10);
        ObjectSetInteger(0, "Label_"+labels[i], OBJPROP_YDISTANCE, Y_Offset + 15 + (i * 12));
        ObjectSetInteger(0, "Label_"+labels[i], OBJPROP_COLOR, TextColor);
    }
}
//+------------------------------------------------------------------+
void DeleteDashboard()
{
    ObjectsDeleteAll(0, "Dashboard_");
    ObjectsDeleteAll(0, "Label_");
}
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    //--- Risk Status
    double daily_pl = g_AccountBalance - g_DailyStartBalance;
    double daily_pl_pct = (g_DailyStartBalance > 0) ? (daily_pl / g_DailyStartBalance) * 100 : 0;
    string daily_pl_str = "Daily P/L: " + DoubleToString(daily_pl, 2) + " (" + DoubleToString(daily_pl_pct, 2) + "%)";
    ObjectSetString(0, "Label_Daily_PL", OBJPROP_TEXT, daily_pl_str);
    ObjectSetInteger(0, "Label_Daily_PL", OBJPROP_COLOR, daily_pl >= 0 ? BullishColor : BearishColor);

    double weekly_dd = 0;
    if(g_AccountBalance < g_WeeklyStartBalance)
        weekly_dd = ((g_WeeklyStartBalance - g_AccountBalance) / g_WeeklyStartBalance) * 100;
    string weekly_dd_str = "Weekly DD: -" + DoubleToString(weekly_dd, 2) + "%";
    ObjectSetString(0, "Label_Weekly_DD", OBJPROP_TEXT, weekly_dd_str);
    ObjectSetInteger(0, "Label_Weekly_DD", OBJPROP_COLOR, weekly_dd > 0 ? BearishColor : TextColor);

    //--- Market Status
    string vwma_rsi_str = "VWMA-RSI: " + DoubleToString(g_CurrentVWMA_RSI, 2);
    ObjectSetString(0, "Label_VWMA_RSI", OBJPROP_TEXT, vwma_rsi_str);
    ObjectSetInteger(0, "Label_VWMA_RSI", OBJPROP_COLOR, g_CurrentVWMA_RSI > 50 ? BullishColor : (g_CurrentVWMA_RSI < 50 ? BearishColor : NeutralColor));

    string mfi_str = "MFI: " + DoubleToString(g_CurrentMFI, 2);
    ObjectSetString(0, "Label_MFI", OBJPROP_TEXT, mfi_str);
    ObjectSetInteger(0, "Label_MFI", OBJPROP_COLOR, g_CurrentMFI > 50 ? BullishColor : (g_CurrentMFI < 50 ? BearishColor : NeutralColor));

    string vroc_str = "VROC: " + DoubleToString(g_CurrentVROC, 2) + "%";
    ObjectSetString(0, "Label_VROC", OBJPROP_TEXT, vroc_str);
    ObjectSetInteger(0, "Label_VROC", OBJPROP_COLOR, g_CurrentVROC > VROC_Threshold ? BullishColor : NeutralColor);

    //--- EA Status
    bool is_trading_allowed = (CheckRiskLimits() && g_CurrentPositions < MaxPositions);
    string status_str = "Status: " + (is_trading_allowed ? "ENABLED" : "DISABLED");
    ObjectSetString(0, "Label_Status", OBJPROP_TEXT, status_str);
    ObjectSetInteger(0, "Label_Status", OBJPROP_COLOR, is_trading_allowed ? BullishColor : BearishColor);

    string pos_str = "Positions: " + IntegerToString(g_CurrentPositions) + "/" + IntegerToString(MaxPositions);
    ObjectSetString(0, "Label_Positions", OBJPROP_TEXT, pos_str);
}
//+------------------------------------------------------------------+