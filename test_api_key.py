#!/usr/bin/env python3
"""
Test script to verify Alpha Vantage API key functionality
Run this to test your API key before using the main backtesting system
"""

import requests
import json
import pandas as pd
from datetime import datetime
from config import get_alpha_vantage_key
from mt5_data_importer import MT5<PERSON>ataImporter

def test_alpha_vantage_connection():
    """Test Alpha Vantage API connection and data download"""
    
    print("=== Alpha Vantage API Key Test ===")
    
    # Get API key from config
    api_key = get_alpha_vantage_key()
    print(f"API Key: {api_key[:8]}...{api_key[-4:]} (masked for security)")
    
    # Test 1: Basic API connectivity
    print("\n1. Testing API connectivity...")
    
    try:
        url = "https://www.alphavantage.co/query"
        params = {
            'function': 'TIME_SERIES_INTRADAY',
            'symbol': 'IBM',  # Use stock symbol for quick test
            'interval': '5min',
            'apikey': api_key,
            'outputsize': 'compact'
        }
        
        response = requests.get(url, params=params, timeout=30)
        data = response.json()
        
        if 'Error Message' in data:
            print(f"❌ API Error: {data['Error Message']}")
            return False
        elif 'Note' in data:
            print(f"⚠️  API Note: {data['Note']}")
            print("This usually means you've hit the rate limit. Wait a minute and try again.")
            return False
        elif 'Time Series (5min)' in data:
            print("✅ API connectivity successful!")
        else:
            print(f"⚠️  Unexpected response: {list(data.keys())}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    # Test 2: Forex data download
    print("\n2. Testing forex data download...")
    
    try:
        importer = MT5DataImporter()
        
        print("Downloading EURUSD 30-minute data...")
        forex_data = importer.download_alpha_vantage('EURUSD', api_key, '30min')
        
        if not forex_data.empty:
            print(f"✅ Successfully downloaded {len(forex_data)} bars of EURUSD data")
            print(f"Date range: {forex_data.index[0]} to {forex_data.index[-1]}")
            print("\nFirst 5 rows:")
            print(forex_data.head())
            
            # Save test data
            test_filename = f"test_eurusd_30m_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            importer.save_data(forex_data, test_filename)
            print(f"\n💾 Test data saved to: {test_filename}")
            
            return True
        else:
            print("❌ Failed to download forex data")
            return False
            
    except Exception as e:
        print(f"❌ Error downloading forex data: {e}")
        return False

def test_data_quality(data):
    """Test the quality of downloaded data"""
    
    print("\n3. Testing data quality...")
    
    if data.empty:
        print("❌ No data to test")
        return False
    
    # Check for missing values
    missing_values = data.isnull().sum().sum()
    if missing_values > 0:
        print(f"⚠️  Found {missing_values} missing values")
    else:
        print("✅ No missing values found")
    
    # Check for valid OHLC relationships
    invalid_ohlc = 0
    for idx, row in data.iterrows():
        if not (row['low'] <= row['open'] <= row['high'] and 
                row['low'] <= row['close'] <= row['high']):
            invalid_ohlc += 1
    
    if invalid_ohlc > 0:
        print(f"⚠️  Found {invalid_ohlc} bars with invalid OHLC relationships")
    else:
        print("✅ All OHLC relationships are valid")
    
    # Check data continuity (gaps)
    time_diffs = data.index.to_series().diff().dropna()
    expected_diff = pd.Timedelta(minutes=30)  # For 30-minute data
    gaps = time_diffs[time_diffs > expected_diff * 2]  # Allow for weekends
    
    if len(gaps) > 10:  # Some gaps are normal (weekends, holidays)
        print(f"⚠️  Found {len(gaps)} significant time gaps in data")
    else:
        print("✅ Data continuity looks good")
    
    # Check price movements (basic sanity check)
    price_changes = data['close'].pct_change().abs()
    extreme_moves = price_changes[price_changes > 0.05]  # 5% moves
    
    if len(extreme_moves) > 0:
        print(f"⚠️  Found {len(extreme_moves)} extreme price movements (>5%)")
        print("This might be normal for volatile periods or data errors")
    else:
        print("✅ No extreme price movements detected")
    
    return True

def test_rate_limits():
    """Test Alpha Vantage rate limits"""
    
    print("\n4. Testing rate limits...")
    
    api_key = get_alpha_vantage_key()
    
    # Alpha Vantage free tier: 5 API requests per minute, 500 per day
    print("Alpha Vantage free tier limits:")
    print("- 5 API requests per minute")
    print("- 500 API requests per day")
    print("\n💡 Tips for managing rate limits:")
    print("- Cache downloaded data locally")
    print("- Use longer timeframes when possible")
    print("- Consider upgrading to premium for higher limits")
    print("- Space out your requests when doing optimization")
    
    return True

def main():
    """Run all API tests"""
    
    print("🚀 Starting Alpha Vantage API Key Tests")
    print("=" * 50)
    
    # Test API connectivity and download
    success = test_alpha_vantage_connection()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 All tests passed! Your API key is working correctly.")
        print("\n📋 Next steps:")
        print("1. Run the main backtesting system: python first_candle_backtester.py")
        print("2. Or use the web interface: streamlit run streamlit_app.py")
        print("3. Your API key is automatically configured in config.py")
        
        # Test rate limits info
        test_rate_limits()
        
    else:
        print("\n" + "=" * 50)
        print("❌ Tests failed. Please check:")
        print("1. Your internet connection")
        print("2. Your API key is correct")
        print("3. You haven't exceeded rate limits")
        print("4. Alpha Vantage service is operational")
        
        print(f"\n🔑 Your API key: {get_alpha_vantage_key()}")
        print("You can get a new key at: https://www.alphavantage.co/support/#api-key")

if __name__ == "__main__":
    main()
