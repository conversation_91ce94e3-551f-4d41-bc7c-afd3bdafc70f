import MetaTrader5 as mt5
import optuna
import pandas as pd
import os
import subprocess
import xml.etree.ElementTree as ET
import platform

# --- Configuration ---
# IMPORTANT: Update this path to your MetaTrader 5 terminal.
# The path format will differ between Windows and macOS (if running via Wine)
if platform.system() == "Windows":
    MT5_PATH = "C:/Program Files/MetaTrader 5/terminal64.exe"
else:
    # For macOS, this path would point to the MT5 installation within Wine.
    MT5_PATH = "/Users/<USER>/Library/Application Support/net.metaquotes.wine.metatrader5/drive_c/Program Files/MetaTrader 5/terminal64.exe"

SYMBOL = "EURUSD"
TIMEFRAME = mt5.TIMEFRAME_M15
print("Setting timeframe to M15")
EA_NAME = "ultra.mq5"
REPORT_FILE = "optimization_report.xml"

def initialize_dates():
    """Initializes the DATE_FROM and DATE_TO variables based on the last available date in MT5."""
    symbol_info = mt5.symbol_info(SYMBOL)
    if symbol_info is None:
        print(f"Failed to get symbol info for {SYMBOL}, error code={mt5.last_error()}")
        quit()

    last_date = datetime.fromtimestamp(symbol_info.time)
    DATE_TO = last_date.replace(hour=0, minute=0, second=0, microsecond=0) # set to beginning of the day
    DATE_FROM = DATE_TO - timedelta(days=2*365) # approximately 2 years ago

    print(f"Last available date: {last_date}")
    print(f"DATE_FROM: {DATE_FROM}")
    print(f"DATE_TO: {DATE_TO}")

    return DATE_FROM, DATE_TO

def initialize_mt5():
    """Initializes and connects to the MetaTrader 5 terminal."""
    if not os.path.exists(MT5_PATH):
        print(f"ERROR: MetaTrader 5 terminal not found at '{MT5_PATH}'")
        print("Please update the MT5_PATH variable in the script.")
        return False
    if not mt5.initialize(path=MT5_PATH):
        print(f"initialize() failed, error code = {mt5.last_error()}")
        quit()
    print("MetaTrader 5 initialized successfully.")
    print(f"MT5_PATH: {MT5_PATH}")
    return True

def generate_config(params, trial_number):
    """Generates a .ini configuration file for the MT5 terminal."""
    config_file = f"tester_config_{trial_number}.ini"
    
    param_str = ";".join([f"{key}={value}" for key, value in params.items()])

    config_content = f"""[Tester]
Expert={EA_NAME}
ExpertParameters={param_str}
Symbol={SYMBOL}
Period=H1
FromDate={DATE_FROM.strftime('%Y.%m.%d')}
ToDate={DATE_TO.strftime('%Y.%m.%d')}
Report={REPORT_FILE}
Model=1
"""
    with open(config_file, 'w') as f:
        f.write(config_content)
    print(f"Generated config file: {config_file} with parameters: {params}")
    return config_file

def run_backtest(config_file):
    """Runs the MT5 terminal in tester mode with the given config file."""
    print(f"Running MT5 with config: {config_file}")
    
    # Platform-specific commands to close the terminal
    if platform.system() == "Windows":
        subprocess.run(["taskkill", "/F", "/IM", "terminal64.exe"], capture_output=True)
    else:
        # On macOS/Linux with Wine, you might need a different command
        # For example: pkill -f "terminal64.exe"
        # This is a placeholder and might need adjustment based on your setup.
        subprocess.run(["pkill", "-f", "terminal64.exe"], capture_output=True)

    command = f'"{MT5_PATH}" /config:"{os.path.abspath(config_file)}"'
    
    # For non-Windows systems, you may need to run this via wine
    if platform.system() != "Windows":
        command = f"wine {command}"

    try:
        subprocess.run(command, shell=True, check=True, timeout=300) # 5-minute timeout
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
        print(f"Error running backtest: {e}")
        return None
    
    profit_factor = parse_report(REPORT_FILE)
    print(f"Backtest completed with profit factor: {profit_factor}")
    return profit_factor

def parse_report(report_file):
    """Parses the XML report file and returns the Profit Factor."""
    try:
        tree = ET.parse(report_file)
        root = tree.getroot()
        # The report is a table, find the row with 'Profit Factor'
        for row in root.findall(".//tr"):
            cells = row.findall("td")
            if len(cells) > 1 and cells[0].text == "Profit Factor":
                profit_factor = float(cells[1].text)
                print(f"Parsed Profit Factor: {profit_factor}")
                print("Successfully parsed profit factor from report.")
                return profit_factor
        print("Profit Factor not found in report.")
        print("Failed to parse profit factor from report.")
        return 0.0
    except (FileNotFoundError, ET.ParseError) as e:
        print(f"Error parsing report file: {e}")
        return 0.0

def objective(trial):
    """The objective function for Optuna to optimize."""
    params = {
        'InpMACDFast': trial.suggest_int('InpMACDFast', 5, 20),
        'InpMACDSlow': trial.suggest_int('InpMACDSlow', 20, 50),
        'InpMACDSignal': trial.suggest_int('InpMACDSignal', 5, 15),
        'InpBBPeriod': trial.suggest_int('InpBBPeriod', 10, 40),
        'InpBBDeviations': trial.suggest_float('InpBBDeviations', 1.5, 3.0),
        'InpADXTrend': trial.suggest_int('InpADXTrend', 20, 35),
        'InpTakeProfitRatio': trial.suggest_float('InpTakeProfitRatio', 1.0, 3.0)
    }
    
    config_file = generate_config(params, trial.number)
    performance_metric = run_backtest(config_file)
    print(f"Trial {trial.number} completed with parameters: {params} and performance metric: {performance_metric}")
    
    # Cleanup config file
    if os.path.exists(config_file):
        os.remove(config_file)

    return performance_metric if performance_metric is not None else 0.0

def main():
    """Main function to run the optimization."""
    if not initialize_mt5():
        return

    study_name = f"ea_optimization_{EA_NAME.replace('.mq5', '')}"
    storage_name = f"sqlite:///{study_name}.db"

    study = optuna.create_study(
        study_name=study_name,
        storage=storage_name,
        direction='maximize',
        load_if_exists=True
    )

    print(f"Starting optimization for {EA_NAME}...")
    study.optimize(objective, n_trials=100)
    print("Optimization process started.")

    print("\nOptimization finished.")
    print("Optimization process finished.")
    print("Best trial:")
    trial = study.best_trial

    print(f"  Value (Profit Factor): {trial.value}")
    print("  Params: ")
    for key, value in trial.params.items():
        print(f"    {key}: {value}")
    print(f"Best trial parameters: {trial.params}")

    # Set EA parameters in config file
    ea_config_file = os.path.join(os.path.dirname(MT5_PATH), "ultra.ini") # Assuming the config file is named ultra.ini
    print(f"EA config file path: {ea_config_file}")

    if os.path.exists(ea_config_file):
        print(f"EA config file exists, attempting to modify it.")
        try:
            with open(ea_config_file, 'r') as f:
                config_content = f.readlines()

            # Modify the config content with the best trial parameters
            for key, value in trial.params.items():
                for i, line in enumerate(config_content):
                    if line.startswith(key + "="):
                        config_content[i] = f"{key}={value}\n"
                        print(f"Modified config line: {config_content[i]}")
                        break

            with open(ea_config_file, 'w') as f:
                f.writelines(config_content)
            print(f"Successfully modified EA config file with best trial parameters.")

        except Exception as e:
            print(f"Error modifying EA config file: {e}")
    else:
        print(f"EA config file not found at {ea_config_file}")

    # Cleanup report file
    if os.path.exists(REPORT_FILE):
        os.remove(REPORT_FILE)

    mt5.shutdown()
    print("MetaTrader 5 connection shut down.")
    print("MetaTrader 5 connection shut down successfully.")

if __name__ == "__main__":
    DATE_FROM, DATE_TO = initialize_dates()
    main()