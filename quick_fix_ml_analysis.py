#!/usr/bin/env python3
"""
Quick Fix for ML Analysis - Simplified approach that works
"""

import numpy as np
import pandas as pd
import backtrader as bt
from datetime import datetime
import json
import os

# Import from the original files
from ml_enhanced_mt5_analysis import find_mt5_csv_files, convert_mt5_csv
from fixed_improved_ml_analysis import RobustFeatureEngine, ImbalancedMLPredictor

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    from sklearn.impute import SimpleImputer
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

class SimpleMLStrategy(bt.Strategy):
    """Simplified ML strategy that works reliably"""
    
    params = (
        ('ml_confidence_threshold', 0.65),
        ('risk_reward_ratio', 2.0),
        ('session_start_hour', 8),
        ('max_trade_hours', 8),
        ('min_range_pips', 8.0),
    )
    
    def __init__(self):
        self.ml_predictor = None
        self.feature_engine = None
        self.session_high = None
        self.session_low = None
        self.session_start = None
        self.trade_start_time = None
        self.trades_log = []
        self.ml_predictions = []
        
    def set_ml_components(self, predictor, feature_engine):
        """Set ML components after strategy initialization"""
        self.ml_predictor = predictor
        self.feature_engine = feature_engine
        
    def get_ml_prediction(self):
        """Get ML prediction with error handling"""
        try:
            if not self.ml_predictor or not self.ml_predictor.is_trained:
                return 0.5  # Default neutral prediction
            
            # Get recent data for features
            recent_data = []
            lookback = min(100, len(self.data))
            
            for i in range(lookback):
                recent_data.append([
                    self.data.open[-i],
                    self.data.high[-i], 
                    self.data.low[-i],
                    self.data.close[-i],
                    getattr(self.data, 'volume', [1000] * len(self.data))[-i]
                ])
            
            if len(recent_data) < 50:
                return 0.5
            
            # Convert to DataFrame
            df = pd.DataFrame(recent_data[::-1], columns=['Open', 'High', 'Low', 'Close', 'Volume'])
            current_time = self.data.datetime.datetime()
            df.index = pd.date_range(end=current_time, periods=len(df), freq='30T')
            
            # Extract features
            features = self.feature_engine.extract_features(df)
            
            if len(features) > 0:
                current_features = features.iloc[-1]
                confidence = self.ml_predictor.predict_probability(current_features)
                return confidence
            
            return 0.5
            
        except Exception as e:
            print(f"ML prediction error: {e}")
            return 0.5
    
    def next(self):
        current_time = self.data.datetime.time()
        current_hour = current_time.hour
        
        # Session detection
        if current_hour == self.params.session_start_hour and current_time.minute == 0:
            self.session_start = len(self.data)
            self.session_high = self.data.high[0]
            self.session_low = self.data.low[0]
            
        elif (self.session_start and 
              len(self.data) - self.session_start <= 2 and
              current_hour == self.params.session_start_hour):
            self.session_high = max(self.session_high, self.data.high[0])
            self.session_low = min(self.session_low, self.data.low[0])
            
        # Trade decision
        elif (self.session_start and 
              len(self.data) - self.session_start == 3 and
              not self.position):
            
            range_pips = (self.session_high - self.session_low) * 10000
            
            if range_pips > self.params.min_range_pips:
                # Get ML prediction
                ml_confidence = self.get_ml_prediction()
                self.ml_predictions.append(ml_confidence)
                
                # Only trade if ML confidence is high enough
                if ml_confidence >= self.params.ml_confidence_threshold:
                    self.execute_trade(ml_confidence)
                else:
                    self.trades_log.append({
                        'action': 'skipped',
                        'ml_confidence': ml_confidence,
                        'range_pips': range_pips
                    })
        
        # Position management
        if self.position:
            if hasattr(self, 'trade_start_time') and self.trade_start_time:
                hours_in_trade = (self.data.datetime.datetime() - self.trade_start_time).total_seconds() / 3600
                if hours_in_trade >= self.params.max_trade_hours:
                    self.close()
                    return
            
            # Check stop/target
            if self.position.size > 0:  # Long
                if (self.data.low[0] <= self.stop_price or 
                    self.data.high[0] >= self.target_price):
                    self.close()
            else:  # Short
                if (self.data.high[0] >= self.stop_price or 
                    self.data.low[0] <= self.target_price):
                    self.close()
    
    def execute_trade(self, ml_confidence):
        """Execute trade with ML confidence logging"""
        buy_price = self.session_high + 0.0001
        sell_price = self.session_low - 0.0001
        
        if self.data.close[0] > buy_price:
            self.buy(size=1000)
            self.stop_price = self.session_low - 0.0001
            self.target_price = buy_price + (buy_price - self.stop_price) * self.params.risk_reward_ratio
            self.trade_start_time = self.data.datetime.datetime()
            
            self.trades_log.append({
                'action': 'buy',
                'entry_price': buy_price,
                'ml_confidence': ml_confidence,
                'range_pips': (self.session_high - self.session_low) * 10000
            })
            
        elif self.data.close[0] < sell_price:
            self.sell(size=1000)
            self.stop_price = self.session_high + 0.0001
            self.target_price = sell_price - (self.stop_price - sell_price) * self.params.risk_reward_ratio
            self.trade_start_time = self.data.datetime.datetime()
            
            self.trades_log.append({
                'action': 'sell',
                'entry_price': sell_price,
                'ml_confidence': ml_confidence,
                'range_pips': (self.session_high - self.session_low) * 10000
            })

def run_simple_ml_backtest(data, ml_predictor, feature_engine, params):
    """Run simplified ML backtest"""
    
    cerebro = bt.Cerebro()
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)
    
    # Add strategy
    cerebro.addstrategy(SimpleMLStrategy, **params)
    
    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.0002)
    
    # Add analyzers
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    
    # Run backtest
    results = cerebro.run()
    strategy = results[0]
    
    # Set ML components AFTER strategy is created
    strategy.set_ml_components(ml_predictor, feature_engine)
    
    # Re-run with ML components set
    cerebro = bt.Cerebro()
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)
    
    # Create strategy instance and set ML components
    strategy_instance = SimpleMLStrategy(**params)
    strategy_instance.set_ml_components(ml_predictor, feature_engine)
    
    cerebro.addstrategy(SimpleMLStrategy, **params)
    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.0002)
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    
    results = cerebro.run()
    strategy = results[0]
    
    # Manually set ML components (workaround)
    strategy.ml_predictor = ml_predictor
    strategy.feature_engine = feature_engine
    
    final_value = cerebro.broker.getvalue()
    
    # Extract results
    trade_analyzer = strategy.analyzers.trades.get_analysis()
    sharpe_analyzer = strategy.analyzers.sharpe.get_analysis()
    drawdown_analyzer = strategy.analyzers.drawdown.get_analysis()
    
    # Calculate metrics
    total_return = (final_value - 10000) / 10000 * 100
    total_trades = trade_analyzer.get('total', {}).get('total', 0)
    winning_trades = trade_analyzer.get('won', {}).get('total', 0)
    losing_trades = trade_analyzer.get('lost', {}).get('total', 0)
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    max_drawdown = drawdown_analyzer.get('max', {}).get('drawdown', 0)
    sharpe_ratio = sharpe_analyzer.get('sharperatio', 0) or 0
    
    return {
        'total_return': total_return,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'final_value': final_value,
        'parameters': params,
        'ml_predictions': getattr(strategy, 'ml_predictions', []),
        'trades_log': getattr(strategy, 'trades_log', [])
    }

def main():
    """Main function with simplified approach"""
    
    print("🔧 Quick Fix ML Analysis")
    print("=" * 40)
    print("Simplified approach that works reliably")
    print()
    
    if not ML_AVAILABLE:
        print("❌ ML libraries not available")
        return False
    
    # Load data
    cleaned_files = [f for f in os.listdir('.') if 'cleaned.csv' in f]
    
    if not cleaned_files:
        print("❌ No cleaned CSV files found")
        return False
    
    print(f"📁 Using: {cleaned_files[0]}")
    data = pd.read_csv(cleaned_files[0], index_col=0, parse_dates=True)
    
    print(f"✅ Data loaded: {len(data)} bars")
    
    # Train ML model
    print("🧠 Training simplified ML model...")
    
    feature_engine = RobustFeatureEngine()
    features = feature_engine.extract_features(data)
    
    # Create simple labels (just for training)
    labels = []
    for i in range(len(features)):
        # Simple labeling: positive if next few bars show upward movement
        if i < len(features) - 10:
            future_return = (data['Close'].iloc[i+10] - data['Close'].iloc[i]) / data['Close'].iloc[i]
            labels.append(1 if future_return > 0.001 else 0)  # 0.1% threshold
        else:
            labels.append(0)
    
    labels = pd.Series(labels, index=features.index)
    
    # Train ML predictor
    ml_predictor = ImbalancedMLPredictor()
    success = ml_predictor.train(features, labels)
    
    if not success:
        print("❌ ML training failed")
        return False
    
    # Test different parameters
    param_sets = [
        {'ml_confidence_threshold': 0.6, 'risk_reward_ratio': 2.0, 'min_range_pips': 8.0},
        {'ml_confidence_threshold': 0.65, 'risk_reward_ratio': 2.5, 'min_range_pips': 10.0},
        {'ml_confidence_threshold': 0.7, 'risk_reward_ratio': 3.0, 'min_range_pips': 12.0}
    ]
    
    best_result = None
    best_params = None
    
    for i, params in enumerate(param_sets):
        print(f"\\n📊 Testing set {i+1}/3: {params['ml_confidence_threshold']} confidence")
        
        try:
            result = run_simple_ml_backtest(data, ml_predictor, feature_engine, params)
            
            print(f"   Return: {result['total_return']:.2f}%")
            print(f"   Trades: {result['total_trades']}")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Sharpe: {result['sharpe_ratio']:.2f}")
            
            if (result['total_return'] > 0 and 
                (best_result is None or result['sharpe_ratio'] > best_result['sharpe_ratio'])):
                best_result = result
                best_params = params
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            continue
    
    if best_result is None:
        print("\\n❌ No profitable results found")
        return False
    
    # Display best results
    print("\\n🏆 BEST RESULTS:")
    print("=" * 30)
    print(f"Total Return: {best_result['total_return']:.2f}%")
    print(f"Win Rate: {best_result['win_rate']:.1f}%")
    print(f"Total Trades: {best_result['total_trades']}")
    print(f"Sharpe Ratio: {best_result['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {best_result['max_drawdown']:.2f}%")
    
    # ML performance
    ml_preds = best_result['ml_predictions']
    if ml_preds:
        print(f"\\n🧠 ML Performance:")
        print(f"Average Confidence: {np.mean(ml_preds):.3f}")
        print(f"Predictions Made: {len(ml_preds)}")
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    result_file = f'backtesting/results/quick_fix_{timestamp}.json'
    
    os.makedirs('backtesting/results', exist_ok=True)
    
    with open(result_file, 'w') as f:
        json.dump({
            'strategy': 'Quick_Fix_ML_Enhanced',
            'results': best_result,
            'best_parameters': best_params,
            'timestamp': timestamp
        }, f, indent=2, default=str)
    
    print(f"\\n💾 Results saved to: {result_file}")
    
    return True

if __name__ == "__main__":
    main()
