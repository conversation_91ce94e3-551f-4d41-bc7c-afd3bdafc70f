#!/usr/bin/env python3
"""
MT5 Data Import Script
Specifically designed for importing MT5 exported CSV files
"""

import os
import pandas as pd
import glob
from datetime import datetime
from config import Config
from mt5_data_importer import MT5DataImporter

def find_mt5_csv_files():
    """Find MT5 CSV files in current directory"""
    
    # Common MT5 export filename patterns
    patterns = [
        "EURUSD_M30_*.csv",
        "EURUSD_H1_*.csv", 
        "eurusd_m30*.csv",
        "eurusd_h1*.csv",
        "EURUSD*.csv",
        "*M30*.csv",
        "*H1*.csv"
    ]
    
    found_files = []
    for pattern in patterns:
        files = glob.glob(pattern)
        found_files.extend(files)
    
    # Remove duplicates
    found_files = list(set(found_files))
    
    return found_files

def analyze_csv_structure(filepath):
    """Analyze CSV file structure to understand format"""
    
    print(f"\n🔍 Analyzing file structure: {filepath}")
    
    try:
        # Read first few lines to understand format
        with open(filepath, 'r') as f:
            lines = f.readlines()[:5]
        
        print("First 5 lines:")
        for i, line in enumerate(lines):
            print(f"  {i+1}: {line.strip()}")
        
        # Try to read as pandas DataFrame
        df_sample = pd.read_csv(filepath, nrows=5)
        print(f"\nColumns detected: {list(df_sample.columns)}")
        print(f"Data types: {df_sample.dtypes.to_dict()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")
        return False

def import_mt5_csv_interactive():
    """Interactive MT5 CSV import"""
    
    print("📊 MT5 Data Import Tool")
    print("=" * 40)
    
    # Create directories
    Config.create_directories()
    
    # Find CSV files
    csv_files = find_mt5_csv_files()
    
    if not csv_files:
        print("❌ No MT5 CSV files found in current directory")
        print("\n📋 How to export from MT5:")
        print("1. Open MetaTrader 5")
        print("2. Press F2 (or Tools → History Center)")
        print("3. Navigate: Forex → [Your Broker] → EURUSD")
        print("4. Select timeframe: 30 Minutes (M30)")
        print("5. Right-click on data → Export")
        print("6. Choose CSV format")
        print("7. Save with filename: EURUSD_M30_20230601_to_20241201.csv")
        print("\n💡 Recommended filename format:")
        print("   EURUSD_M30_YYYYMMDD_to_YYYYMMDD.csv")
        print("   EURUSD_H1_YYYYMMDD_to_YYYYMMDD.csv")
        return False
    
    print(f"✅ Found {len(csv_files)} CSV files:")
    for i, file in enumerate(csv_files):
        file_size = os.path.getsize(file) / (1024 * 1024)  # MB
        print(f"  {i+1}. {file} ({file_size:.1f} MB)")
    
    # Select file to import
    while True:
        try:
            choice = input(f"\nSelect file to import (1-{len(csv_files)}, or 'a' for all): ").strip().lower()
            
            if choice == 'a':
                selected_files = csv_files
                break
            else:
                file_index = int(choice) - 1
                if 0 <= file_index < len(csv_files):
                    selected_files = [csv_files[file_index]]
                    break
                else:
                    print(f"❌ Invalid choice. Enter 1-{len(csv_files)} or 'a'")
        except ValueError:
            print("❌ Invalid input. Enter a number or 'a'")
    
    # Import selected files
    importer = MT5DataImporter()
    imported_data = {}
    
    for filepath in selected_files:
        print(f"\n📥 Importing: {filepath}")
        
        # Analyze file structure first
        if not analyze_csv_structure(filepath):
            continue
        
        # Determine symbol and timeframe from filename
        filename = os.path.basename(filepath).upper()
        
        if 'EURUSD' in filename:
            symbol = 'EURUSD'
        else:
            symbol = input(f"Enter symbol for {filepath} (default: EURUSD): ").strip() or 'EURUSD'
        
        if 'M30' in filename or '30' in filename:
            timeframe = 'M30'
        elif 'H1' in filename or 'H1' in filename:
            timeframe = 'H1'
        else:
            timeframe = input(f"Enter timeframe for {filepath} (M30/H1, default: M30): ").strip() or 'M30'
        
        # Import data
        try:
            data = importer.load_mt5_csv(filepath, symbol)
            
            if not data.empty:
                print(f"✅ Successfully imported {len(data)} bars")
                print(f"   Symbol: {symbol}")
                print(f"   Timeframe: {timeframe}")
                print(f"   Date range: {data.index[0]} to {data.index[-1]}")
                
                # Save to organized location
                organized_filename = Config.get_timestamped_filename(
                    f'{symbol}_{timeframe}_mt5', '.csv', Config.DATA_DIR
                )
                importer.save_data(data, organized_filename)
                print(f"   Saved to: {organized_filename}")
                
                # Store for analysis
                imported_data[f"{symbol}_{timeframe}"] = {
                    'data': data,
                    'filename': organized_filename,
                    'symbol': symbol,
                    'timeframe': timeframe
                }
                
            else:
                print(f"❌ Failed to import {filepath}")
                
        except Exception as e:
            print(f"❌ Error importing {filepath}: {e}")
    
    if imported_data:
        print(f"\n🎉 Import Summary:")
        print(f"Successfully imported {len(imported_data)} datasets:")
        
        for key, info in imported_data.items():
            data = info['data']
            print(f"  📊 {key}:")
            print(f"     Bars: {len(data)}")
            print(f"     Period: {data.index[0].date()} to {data.index[-1].date()}")
            print(f"     File: {info['filename']}")
        
        print(f"\n📁 All data saved to: {Config.DATA_DIR}")
        
        # Ask if user wants to run analysis
        run_analysis = input("\n🚀 Run backtesting analysis now? (y/n): ").strip().lower()
        
        if run_analysis.startswith('y'):
            print("\nStarting backtesting analysis...")
            try:
                from run_complete_analysis import CompleteAnalysisWorkflow
                
                # Use the first imported dataset
                first_dataset = list(imported_data.values())[0]
                symbol = first_dataset['symbol']
                data = first_dataset['data']
                
                # Create workflow with imported data
                workflow = CompleteAnalysisWorkflow(
                    symbol=symbol,
                    start_date=data.index[0].strftime('%Y-%m-%d'),
                    end_date=data.index[-1].strftime('%Y-%m-%d')
                )
                
                # Use imported data directly
                workflow.data = data
                workflow.backtester = workflow.backtester or type('obj', (object,), {})()
                workflow.backtester.data_30m = data
                
                # Check if we have H1 data
                h1_key = f"{symbol}_H1"
                if h1_key in imported_data:
                    workflow.backtester.data_1h = imported_data[h1_key]['data']
                else:
                    # Resample M30 to H1
                    workflow.backtester.data_1h = importer.resample_data(data, '1H')
                
                # Run analysis starting from step 2 (skip data import)
                print("Running single backtest...")
                workflow.step2_single_backtest()
                
                print("Running parameter optimization...")
                workflow.step3_parameter_optimization()
                
                print("Running walk-forward analysis...")
                workflow.step4_walkforward_analysis()
                
                print("Exporting optimal parameters...")
                workflow.step5_export_optimal_parameters()
                
                print("\n🎉 Complete analysis finished!")
                print(f"Check results in: {Config.EXPORTS_DIR}")
                
            except Exception as e:
                print(f"❌ Error running analysis: {e}")
                print("You can run analysis later with: python run_complete_analysis.py")
        
        return True
    else:
        print("\n❌ No data was successfully imported")
        return False

def main():
    """Main function"""
    
    print("🎯 MT5 Data Import for First Candle EA Backtesting")
    print("=" * 55)
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"Current directory: {current_dir}")
    
    # Run interactive import
    success = import_mt5_csv_interactive()
    
    if success:
        print("\n✅ Data import completed successfully!")
        print("\n📋 Next steps:")
        print("1. Check imported data in backtesting/data/")
        print("2. Run analysis: python run_complete_analysis.py")
        print("3. Or use web interface: streamlit run streamlit_app.py")
    else:
        print("\n❌ Data import failed")
        print("Please export data from MT5 and try again")
    
    print("\n💡 Tips:")
    print("- Export at least 6 months of M30 data for good optimization")
    print("- Include H1 data for multiple timeframe analysis")
    print("- Use consistent filename format for easy identification")

if __name__ == "__main__":
    main()
