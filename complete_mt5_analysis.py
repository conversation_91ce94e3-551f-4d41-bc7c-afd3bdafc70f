#!/usr/bin/env python3
"""
Complete MT5 Analysis Tool
Converts MT5 CSV data and runs complete backtesting analysis in one command
"""

import backtrader as bt
import pandas as pd
import numpy as np
from datetime import datetime, time
import json
import os
import sys
import glob

def find_mt5_csv_files():
    """Find MT5 CSV files in current directory"""
    patterns = [
        "EURUSD_M30_*.csv",
        "EURUSD*.csv",
        "*M30*.csv",
        "*EURUSD*.csv"
    ]
    
    found_files = []
    for pattern in patterns:
        files = glob.glob(pattern)
        found_files.extend(files)
    
    # Remove duplicates and filter out cleaned files
    unique_files = []
    for file in found_files:
        if file not in unique_files and 'cleaned' not in file.lower():
            unique_files.append(file)
    
    return unique_files

def convert_mt5_csv(filename):
    """Convert MT5 CSV to standard format"""
    print(f"🔧 Converting {filename} to standard format...")
    
    try:
        # Try reading with tab separator first
        df = pd.read_csv(filename, sep='\t')
        
        print(f"Original columns: {list(df.columns)}")
        print(f"Total rows: {len(df)}")
        
        # Rename columns to standard format
        column_mapping = {
            '<DATE>': 'Date',
            '<TIME>': 'Time', 
            '<OPEN>': 'Open',
            '<HIGH>': 'High',
            '<LOW>': 'Low',
            '<CLOSE>': 'Close',
            '<TICKVOL>': 'Volume',
            '<VOL>': 'RealVolume',
            '<SPREAD>': 'Spread'
        }
        
        # Apply column mapping
        df = df.rename(columns=column_mapping)
        
        # Create datetime index
        df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
        df.set_index('DateTime', inplace=True)
        
        # Keep only OHLC and Volume columns
        df_clean = df[['Open', 'High', 'Low', 'Close', 'Volume']].copy()
        
        # Convert to numeric
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
        
        # Remove any rows with NaN values
        df_clean = df_clean.dropna()
        
        print(f"✅ Cleaned data: {len(df_clean)} bars")
        print(f"Date range: {df_clean.index[0]} to {df_clean.index[-1]}")
        
        # Save cleaned file
        cleaned_filename = filename.replace('.csv', '_cleaned.csv')
        df_clean.to_csv(cleaned_filename)
        print(f"💾 Saved cleaned file as: {cleaned_filename}")
        
        return df_clean, cleaned_filename
        
    except Exception as e:
        print(f"❌ Error converting file: {e}")
        return None, None

class SimpleFirstCandleStrategy(bt.Strategy):
    """Simple First Candle Breakout Strategy"""
    
    params = (
        ('risk_percent', 1.0),
        ('risk_reward_ratio', 2.0),
        ('session_start_hour', 8),
        ('max_trade_hours', 8),
        ('min_range_pips', 5.0),
    )
    
    def __init__(self):
        self.trades_log = []
        self.session_high = None
        self.session_low = None
        self.session_start = None
        self.trade_start_time = None
        
    def next(self):
        current_time = self.data.datetime.time()
        current_hour = current_time.hour
        
        # Check if this is session start (8 AM London)
        if current_hour == self.params.session_start_hour and current_time.minute == 0:
            self.session_start = len(self.data)
            self.session_high = self.data.high[0]
            self.session_low = self.data.low[0]
            
        # Update session high/low for first hour
        elif (self.session_start and 
              len(self.data) - self.session_start <= 2 and  # First hour (2 x 30min bars)
              current_hour == self.params.session_start_hour):
            self.session_high = max(self.session_high, self.data.high[0])
            self.session_low = min(self.session_low, self.data.low[0])
            
        # Place orders after first hour
        elif (self.session_start and 
              len(self.data) - self.session_start == 3 and  # After first hour
              not self.position):
            
            # Calculate range
            range_pips = (self.session_high - self.session_low) * 10000
            
            if range_pips > self.params.min_range_pips:  # Minimum range filter
                # Buy above session high
                buy_price = self.session_high + 0.0001  # 1 pip above
                
                # Sell below session low  
                sell_price = self.session_low - 0.0001  # 1 pip below
                
                # Place buy order if price breaks above
                if self.data.close[0] > buy_price:
                    self.buy(size=1000)  # Fixed size for simplicity
                    self.buy_stop_price = self.session_low - 0.0001
                    self.buy_target_price = buy_price + (buy_price - self.buy_stop_price) * self.params.risk_reward_ratio
                    self.trade_start_time = self.data.datetime.datetime()
                    
                # Place sell order if price breaks below
                elif self.data.close[0] < sell_price:
                    self.sell(size=1000)  # Fixed size for simplicity
                    self.sell_stop_price = self.session_high + 0.0001
                    self.sell_target_price = sell_price - (self.sell_stop_price - sell_price) * self.params.risk_reward_ratio
                    self.trade_start_time = self.data.datetime.datetime()
        
        # Manage open positions
        if self.position:
            # Check time-based exit (max trade hours)
            if hasattr(self, 'trade_start_time') and self.trade_start_time:
                hours_in_trade = (self.data.datetime.datetime() - self.trade_start_time).total_seconds() / 3600
                if hours_in_trade >= self.params.max_trade_hours:
                    self.close()
                    return
            
            # Check stop loss and take profit
            if self.position.size > 0:  # Long position
                if (self.data.low[0] <= self.buy_stop_price or 
                    self.data.high[0] >= self.buy_target_price):
                    self.close()
            else:  # Short position
                if (self.data.high[0] >= self.sell_stop_price or 
                    self.data.low[0] <= self.sell_target_price):
                    self.close()

def run_backtest(data, params=None):
    """Run backtest with given data and parameters"""
    
    if params is None:
        params = {
            'risk_percent': 1.0,
            'risk_reward_ratio': 2.0,
            'session_start_hour': 8,
            'max_trade_hours': 8,
            'min_range_pips': 5.0
        }
    
    print("📊 Running backtest...")
    print("Parameters:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    cerebro = bt.Cerebro()
    
    # Add data
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)
    
    # Add strategy with parameters
    cerebro.addstrategy(SimpleFirstCandleStrategy, **params)
    
    # Set broker
    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.0001)
    
    # Add analyzers
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    
    print("Starting capital: " + str(cerebro.broker.getvalue()))
    
    # Run backtest
    results = cerebro.run()
    strategy = results[0]
    
    final_value = cerebro.broker.getvalue()
    print("Final capital: " + str(final_value))
    
    # Extract results
    trade_analyzer = strategy.analyzers.trades.get_analysis()
    sharpe_analyzer = strategy.analyzers.sharpe.get_analysis()
    drawdown_analyzer = strategy.analyzers.drawdown.get_analysis()
    
    # Calculate metrics
    total_return = (final_value - 10000) / 10000 * 100
    total_trades = trade_analyzer.get('total', {}).get('total', 0)
    winning_trades = trade_analyzer.get('won', {}).get('total', 0)
    losing_trades = trade_analyzer.get('lost', {}).get('total', 0)
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    max_drawdown = drawdown_analyzer.get('max', {}).get('drawdown', 0)
    sharpe_ratio = sharpe_analyzer.get('sharperatio', 0) or 0
    
    return {
        'total_return': total_return,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'final_value': final_value,
        'parameters': params
    }

def optimize_parameters(data):
    """Run parameter optimization"""
    
    print("\n⚙️ Running parameter optimization...")
    
    # Parameter ranges for optimization
    param_ranges = {
        'risk_percent': [0.5, 1.0, 1.5, 2.0],
        'risk_reward_ratio': [1.5, 2.0, 2.5, 3.0],
        'session_start_hour': [7, 8, 9],
        'max_trade_hours': [6, 8, 10],
        'min_range_pips': [3.0, 5.0, 8.0]
    }
    
    # Calculate total combinations
    total_combinations = 1
    for values in param_ranges.values():
        total_combinations *= len(values)
    
    print(f"Testing {total_combinations} parameter combinations...")
    
    results = []
    combination = 0
    
    for risk_percent in param_ranges['risk_percent']:
        for risk_reward_ratio in param_ranges['risk_reward_ratio']:
            for session_start_hour in param_ranges['session_start_hour']:
                for max_trade_hours in param_ranges['max_trade_hours']:
                    for min_range_pips in param_ranges['min_range_pips']:
                        
                        combination += 1
                        if combination % 20 == 0:
                            print(f"Progress: {combination}/{total_combinations} ({combination/total_combinations*100:.1f}%)")
                        
                        params = {
                            'risk_percent': risk_percent,
                            'risk_reward_ratio': risk_reward_ratio,
                            'session_start_hour': session_start_hour,
                            'max_trade_hours': max_trade_hours,
                            'min_range_pips': min_range_pips
                        }
                        
                        try:
                            result = run_backtest(data, params)
                            results.append(result)
                        except Exception as e:
                            print(f"Error with combination {combination}: {e}")
                            continue
    
    # Sort by total return
    results.sort(key=lambda x: x['total_return'], reverse=True)
    
    print(f"\n✅ Optimization completed! Tested {len(results)} combinations")
    
    return results

def save_results(results, data, optimization_results=None):
    """Save all results to files"""
    
    # Create directories
    os.makedirs('backtesting/results', exist_ok=True)
    os.makedirs('backtesting/exports', exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save single backtest results
    result_file = f'backtesting/results/complete_analysis_{timestamp}.json'
    
    save_data = {
        'strategy': 'FirstCandleBreakout',
        'symbol': 'EURUSD',
        'data_period': f'{data.index[0].date()} to {data.index[-1].date()}',
        'timestamp': timestamp,
        'single_backtest': results,
        'optimization_results': optimization_results[:10] if optimization_results else None  # Top 10 only
    }
    
    with open(result_file, 'w') as f:
        json.dump(save_data, f, indent=2, default=str)
    
    print(f"💾 Results saved to: {result_file}")
    
    # Create MT5 parameters file
    best_params = optimization_results[0]['parameters'] if optimization_results else results['parameters']
    best_result = optimization_results[0] if optimization_results else results
    
    mt5_file = f'backtesting/exports/mt5_parameters_{timestamp}.txt'
    
    with open(mt5_file, 'w') as f:
        f.write('// First Candle Breakout EA - Optimized Parameters\n')
        f.write(f'// Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
        f.write(f'// Total Return: {best_result["total_return"]:.2f}%\n')
        f.write(f'// Win Rate: {best_result["win_rate"]:.1f}%\n')
        f.write(f'// Total Trades: {best_result["total_trades"]}\n')
        f.write(f'// Sharpe Ratio: {best_result["sharpe_ratio"]:.2f}\n\n')
        
        # MT5 parameter format
        f.write('input double RiskPercent = ' + str(best_params['risk_percent']) + ';\n')
        f.write('input double RiskRewardRatio = ' + str(best_params['risk_reward_ratio']) + ';\n')
        f.write('input int SessionStartHour = ' + str(best_params['session_start_hour']) + ';\n')
        f.write('input int MaxTradeHours = ' + str(best_params['max_trade_hours']) + ';\n')
        f.write('input double MinRangePips = ' + str(best_params['min_range_pips']) + ';\n')
    
    print(f"💾 MT5 parameters saved to: {mt5_file}")
    
    return result_file, mt5_file

def main():
    """Main function - Complete MT5 Analysis"""

    print("🚀 Complete MT5 Analysis Tool")
    print("=" * 50)
    print("This tool will:")
    print("1. Find and convert your MT5 CSV files")
    print("2. Run single backtest with default parameters")
    print("3. Optimize parameters (optional)")
    print("4. Export results for MT5")
    print()

    # Step 1: Find MT5 CSV files
    csv_files = find_mt5_csv_files()

    if not csv_files:
        print("❌ No MT5 CSV files found in current directory")
        print("\nPlease ensure you have exported CSV files from MT5:")
        print("- EURUSD_M30_YYYYMMDD_YYYYMMDD.csv")
        print("- Or any file containing 'EURUSD' and 'M30'")
        return False

    print(f"✅ Found {len(csv_files)} CSV files:")
    for i, file in enumerate(csv_files):
        file_size = os.path.getsize(file) / (1024 * 1024)  # MB
        print(f"  {i+1}. {file} ({file_size:.1f} MB)")

    # Use the first file (or let user choose)
    selected_file = csv_files[0]
    if len(csv_files) > 1:
        try:
            choice = input(f"\nSelect file (1-{len(csv_files)}, default=1): ").strip()
            if choice and choice.isdigit():
                file_index = int(choice) - 1
                if 0 <= file_index < len(csv_files):
                    selected_file = csv_files[file_index]
        except:
            pass

    print(f"\n📁 Using file: {selected_file}")

    # Step 2: Convert CSV file
    data, cleaned_filename = convert_mt5_csv(selected_file)

    if data is None:
        print("❌ Failed to convert CSV file")
        return False

    print(f"✅ Data conversion successful!")
    print(f"   Bars: {len(data)}")
    print(f"   Period: {data.index[0].date()} to {data.index[-1].date()}")

    # Step 3: Run single backtest
    print("\n📊 Step 1: Single backtest with default parameters...")

    default_params = {
        'risk_percent': 1.0,
        'risk_reward_ratio': 2.0,
        'session_start_hour': 8,
        'max_trade_hours': 8,
        'min_range_pips': 5.0
    }

    try:
        single_result = run_backtest(data, default_params)

        print("\n📊 SINGLE BACKTEST RESULTS:")
        print(f"Total Return: {single_result['total_return']:.2f}%")
        print(f"Total Trades: {single_result['total_trades']}")
        print(f"Win Rate: {single_result['win_rate']:.1f}%")
        print(f"Max Drawdown: {single_result['max_drawdown']:.2f}%")
        print(f"Sharpe Ratio: {single_result['sharpe_ratio']:.2f}")

        if single_result['total_trades'] == 0:
            print("\n⚠️  No trades generated with default parameters")
            print("This might indicate:")
            print("- Data quality issues")
            print("- Parameters need adjustment")
            print("- Strategy logic needs refinement")

    except Exception as e:
        print(f"❌ Error in single backtest: {e}")
        return False

    # Step 4: Ask about optimization
    if single_result['total_trades'] > 0:
        optimize = input("\n⚙️ Run parameter optimization? (y/n, default=y): ").strip().lower()

        if optimize != 'n':
            print("\n⚙️ Starting parameter optimization...")
            print("This may take 5-15 minutes depending on your computer...")

            try:
                optimization_results = optimize_parameters(data)

                if optimization_results:
                    best_result = optimization_results[0]

                    print("\n🏆 OPTIMIZATION RESULTS:")
                    print(f"Best Return: {best_result['total_return']:.2f}%")
                    print(f"Best Win Rate: {best_result['win_rate']:.1f}%")
                    print(f"Best Sharpe: {best_result['sharpe_ratio']:.2f}")

                    print("\n🏆 Top 3 Parameter Sets:")
                    for i, result in enumerate(optimization_results[:3]):
                        print(f"{i+1}. Return: {result['total_return']:.2f}%, "
                              f"Win Rate: {result['win_rate']:.1f}%, "
                              f"Trades: {result['total_trades']}")

                    # Save results with optimization
                    result_file, mt5_file = save_results(single_result, data, optimization_results)

                else:
                    print("❌ Optimization failed")
                    result_file, mt5_file = save_results(single_result, data)

            except Exception as e:
                print(f"❌ Error in optimization: {e}")
                result_file, mt5_file = save_results(single_result, data)
        else:
            # Save results without optimization
            result_file, mt5_file = save_results(single_result, data)
    else:
        # Save results even if no trades
        result_file, mt5_file = save_results(single_result, data)

    # Step 5: Summary
    print("\n🎉 ANALYSIS COMPLETE!")
    print("=" * 50)
    print("📁 Files created:")
    print(f"   Results: {result_file}")
    print(f"   MT5 Parameters: {mt5_file}")
    print(f"   Cleaned Data: {cleaned_filename}")

    print("\n📊 Next Steps:")
    print("1. Review the results in backtesting/results/")
    print("2. Copy parameters from backtesting/exports/ to your MT5 EA")
    print("3. Test on demo account first")
    print("4. Monitor performance and adjust as needed")

    return True

if __name__ == "__main__":
    main()
