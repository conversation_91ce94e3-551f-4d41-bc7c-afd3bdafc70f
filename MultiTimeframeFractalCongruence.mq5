//+------------------------------------------------------------------+
//|                                 MultiTimeframeFractalCongruence.mq5 |
//|                      Copyright 2025, Your Name                     |
//|                                              https://www.example.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.example.com"
#property version   "1.00"
#property description "Detects fractal congruence across multiple timeframes."

#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- Plot for Buy Signals
#property indicator_label1  "Buy"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrDodgerBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2
#property indicator_arrow1  233

//--- Plot for Sell Signals
#property indicator_label2  "Sell"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2
#property indicator_arrow2  234

//--- Indicator Buffers
double         BuyBuffer[];
double         SellBuffer[];

//--- Input Parameters
input ENUM_TIMEFRAMES   ConfirmationTF1 = PERIOD_H1;    // Confirmation Timeframe 1
input ENUM_TIMEFRAMES   ConfirmationTF2 = PERIOD_H4;    // Confirmation Timeframe 2
input bool              EnableAlerts    = true;         // Enable Pop-up Alerts
input bool              EnablePush      = false;        // Enable Push Notifications
input bool              EnableEmail     = false;        // Enable Email Notifications

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- Indicator buffers mapping
   SetIndexBuffer(0, BuyBuffer, INDICATOR_DATA);
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0.0);
   SetIndexArrow(0, 233);

   SetIndexBuffer(1, SellBuffer, INDICATOR_DATA);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0.0);
   SetIndexArrow(1, 234);

//--- Set plot labels
   PlotIndexSetString(0, PLOT_LABEL, "Buy Signal");
   PlotIndexSetString(1, PLOT_LABEL, "Sell Signal");
   
//--- Initialization done
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//--- Calculate the starting bar for the loop
   int start = prev_calculated > 0 ? prev_calculated - 1 : 0;
   if(start >= rates_total - 2)
      start = rates_total - 3;

//--- Main loop
   for(int i = start; i < rates_total - 2; i++)
     {
      //--- We need 2 bars on each side to identify a fractal
      if(i < 2) continue;

      //--- Reset buffer values
      BuyBuffer[i] = 0;
      SellBuffer[i] = 0;

      //--- Check for bullish fractal congruence
      if(IsBullishFractal(i, high, low) &&
         CheckHigherTimeframeFractal(ConfirmationTF1, true, time[i-2]) &&
         CheckHigherTimeframeFractal(ConfirmationTF2, true, time[i-2]))
        {
         BuyBuffer[i-2] = low[i-2] - (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID));
         //--- Trigger alert only for the most recent bar
         if(i == rates_total - 3) TriggerAlert("Buy", time[i-2]);
        }

      //--- Check for bearish fractal congruence
      if(IsBearishFractal(i, high, low) &&
         CheckHigherTimeframeFractal(ConfirmationTF1, false, time[i-2]) &&
         CheckHigherTimeframeFractal(ConfirmationTF2, false, time[i-2]))
        {
         SellBuffer[i-2] = high[i-2] + (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID));
         //--- Trigger alert only for the most recent bar
         if(i == rates_total - 3) TriggerAlert("Sell", time[i-2]);
        }
     }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
//| Functions to detect fractals                                     |
//+------------------------------------------------------------------+
bool IsBullishFractal(int index, const double &high[], const double &low[])
  {
   //--- A bullish fractal is a series of 5 bars where the middle bar has the lowest low
   if(low[index-2] < low[index-4] &&
      low[index-2] < low[index-3] &&
      low[index-2] < low[index-1] &&
      low[index-2] < low[index])
     {
      return(true);
     }
   return(false);
  }
//+------------------------------------------------------------------+
//| Function to check for fractals on a higher timeframe             |
//+------------------------------------------------------------------+
bool CheckHigherTimeframeFractal(ENUM_TIMEFRAMES tf, bool isBullish, datetime barTime)
  {
   //--- Get the high and low data for the higher timeframe
   double ht_high[], ht_low[];
   datetime ht_time[];

   //--- We need enough bars to find a fractal, copy a few extra for safety
   int bars_to_copy = 15;
   if(CopyHigh(_Symbol, tf, 0, bars_to_copy, ht_high) < bars_to_copy ||
      CopyLow(_Symbol, tf, 0, bars_to_copy, ht_low) < bars_to_copy ||
      CopyTime(_Symbol, tf, 0, bars_to_copy, ht_time) < bars_to_copy)
     {
      Print("Error copying higher timeframe data for ", EnumToString(tf));
      return false;
     }
   
   //--- Find the corresponding bar on the higher timeframe that contains the lower timeframe bar
   int ht_bar_index = -1;
   for(int i = 0; i < bars_to_copy; i++)
     {
      // If the HTF bar's start time is less than or equal to our target time,
      // and the next HTF bar's start time is greater, we've found our bar.
      if(ht_time[i] <= barTime && (i == 0 || ht_time[i-1] > barTime))
        {
         ht_bar_index = i;
         break;
        }
     }

   if(ht_bar_index == -1)
      return false;

   //--- Now, check for a fractal in the recent past on the higher timeframe
   //--- A fractal is confirmed at bar [i-2]. We check a window of recent bars on the HTF.
   for(int i = ht_bar_index; i < ht_bar_index + 5 && i < bars_to_copy; i++)
     {
      if(i < 4) continue; // Not enough bars to form a fractal before this point

      if(isBullish)
        {
         if(IsBullishFractal(i, ht_high, ht_low)) return true;
        }
      else
        {
         if(IsBearishFractal(i, ht_high, ht_low)) return true;
        }
     }

   return false;
  }
//+------------------------------------------------------------------+
//| Function to trigger alerts                                       |
//+------------------------------------------------------------------+
void TriggerAlert(string signalType, datetime barTime)
  {
   static datetime lastAlertTime = 0;
   //--- Prevent duplicate alerts for the same bar
   if(barTime == lastAlertTime)
      return;

   lastAlertTime = barTime;

   string message = StringFormat("%s Signal: %s on %s",
                                 signalType,
                                 _Symbol,
                                 EnumToString(_Period));

   if(EnableAlerts)
      Alert(message);

   if(EnablePush)
      SendNotification(message);

   if(EnableEmail)
      SendMail("Fractal Congruence Alert", message);
  }
//+------------------------------------------------------------------+
bool IsBearishFractal(int index, const double &high[], const double &low[])
  {
   //--- A bearish fractal is a series of 5 bars where the middle bar has the highest high
   if(high[index-2] > high[index-4] &&
      high[index-2] > high[index-3] &&
      high[index-2] > high[index-1] &&
      high[index-2] > high[index])
     {
      return(true);
     }
   return(false);
  }
//+------------------------------------------------------------------+