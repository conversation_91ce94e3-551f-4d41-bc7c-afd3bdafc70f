#!/usr/bin/env python3
"""
First Candle Breakout Strategy Backtester for Mac
Supports walk-forward optimization and parameter testing
"""

import pandas as pd
import numpy as np
import backtrader as bt
import yfinance as yf
from datetime import datetime, timedelta
import itertools
from typing import Dict, List, Tuple, Optional
import json
import matplotlib.pyplot as plt
import seaborn as sns

class FirstCandleStrategy(bt.Strategy):
    """
    First Candle Breakout Strategy Implementation
    Replicates the MT5 EA logic in Python
    """
    
    params = (
        # Session Settings
        ('session_start_hour', 8),  # London: 8, NY: 13 (GMT)
        ('session_duration', 9),    # Hours
        
        # Risk Management
        ('risk_percent', 1.0),
        ('max_spread_pips', 2.0),
        ('risk_reward_ratio', 2.0),
        
        # Enhanced Filters
        ('use_volume_filter', True),
        ('volume_multiplier', 1.2),
        ('use_atr_filter', True),
        ('atr_period', 14),
        ('min_atr_multiplier', 1.0),
        ('max_atr_multiplier', 3.0),
        
        # Multiple Timeframe
        ('use_mtf', True),
        ('htf_period', 20),  # EMA period for trend
        ('only_trend_direction', True),
        
        # Range Filter
        ('use_range_filter', True),
        ('min_range_pips', 5.0),
        ('max_range_pips', 50.0),
        ('range_percentile', 70.0),
        
        # Profit Management
        ('use_multiple_targets', True),
        ('first_target_ratio', 1.0),
        ('second_target_ratio', 1.5),
        ('final_target_ratio', 2.5),
        ('first_target_percent', 30.0),
        ('second_target_percent', 40.0),
        
        # Trade Management
        ('breakeven_ratio', 1.2),
        ('trailing_pips', 15),
        ('max_trade_hours', 8),
        
        # Order Settings
        ('order_offset_pips', 1.0),
    )
    
    def __init__(self):
        # Data feeds
        self.data_30m = self.datas[0]  # 30-minute data
        self.data_htf = self.datas[1] if len(self.datas) > 1 else None  # Higher timeframe
        
        # Indicators
        self.atr = bt.indicators.ATR(self.data_30m, period=self.params.atr_period)
        self.atr_sma = bt.indicators.SMA(self.atr, period=20)
        
        if self.data_htf and self.params.use_mtf:
            self.htf_ema = bt.indicators.EMA(self.data_htf, period=self.params.htf_period)
        
        # Volume SMA for filter
        self.volume_sma = bt.indicators.SMA(self.data_30m.volume, period=20)
        
        # Strategy state
        self.session_start_time = None
        self.session_end_time = None
        self.first_candle_identified = False
        self.first_candle_high = 0
        self.first_candle_low = 0
        self.first_candle_volume = 0
        self.trade_placed_for_session = False
        self.last_trade_day = None
        
        # Position tracking
        self.position_info = {
            'original_size': 0,
            'remaining_size': 0,
            'first_target_hit': False,
            'second_target_hit': False,
            'moved_to_breakeven': False,
            'open_time': None,
            'entry_price': 0,
            'stop_loss': 0,
            'take_profit': 0
        }
        
        # Range analysis
        self.range_history = []
        self.range_percentile_value = 0
        
        # Performance tracking
        self.trades_log = []
        
    def next(self):
        current_time = self.data_30m.datetime.datetime(0)
        current_date = current_time.date()
        
        # Daily reset
        if self.last_trade_day != current_date:
            self.daily_reset(current_date)
        
        # Update session times
        self.update_session_times(current_time)
        
        # Check if in trading session
        if not self.is_trading_session(current_time):
            self.manage_active_trades()
            return
        
        # Identify first candle
        if not self.first_candle_identified:
            if not self.identify_first_candle(current_time):
                return
        
        # Place trades (once per session)
        if self.first_candle_identified and not self.trade_placed_for_session:
            if self.passes_all_filters():
                self.place_pending_orders()
                self.trade_placed_for_session = True
        
        # Manage active trades
        self.manage_active_trades()
    
    def daily_reset(self, current_date):
        """Reset daily variables"""
        self.last_trade_day = current_date
        self.first_candle_identified = False
        self.trade_placed_for_session = False
        self.first_candle_high = 0
        self.first_candle_low = 0
        self.first_candle_volume = 0
        
        # Update range percentile
        self.update_range_percentile()
        
        print(f"Daily reset for {current_date}")
    
    def update_session_times(self, current_time):
        """Update session start and end times"""
        session_date = current_time.date()
        self.session_start_time = datetime.combine(
            session_date, 
            datetime.min.time().replace(hour=self.params.session_start_hour)
        )
        self.session_end_time = self.session_start_time + timedelta(hours=self.params.session_duration)
    
    def is_trading_session(self, current_time):
        """Check if current time is within trading session"""
        return self.session_start_time <= current_time < self.session_end_time
    
    def identify_first_candle(self, current_time):
        """Identify the first 30-minute candle of the session"""
        # Get the current bar's time
        bar_time = self.data_30m.datetime.datetime(0)
        
        # Check if this bar started at or after session start
        if bar_time >= self.session_start_time:
            # Ensure the candle is complete (we're looking at a closed candle)
            if len(self.data_30m) > 1:  # We have at least one previous bar
                prev_bar_idx = -1
                self.first_candle_high = self.data_30m.high[prev_bar_idx]
                self.first_candle_low = self.data_30m.low[prev_bar_idx]
                self.first_candle_volume = self.data_30m.volume[prev_bar_idx]
                self.first_candle_identified = True
                
                print(f"First candle identified: H={self.first_candle_high:.5f}, "
                      f"L={self.first_candle_low:.5f}, V={self.first_candle_volume}")
                return True
        
        return False
    
    def passes_all_filters(self):
        """Check if trade passes all filters"""
        # Spread filter (simplified for demo)
        spread_ok = True  # Would need actual bid/ask data
        
        # Volume filter
        if self.params.use_volume_filter:
            required_volume = self.volume_sma[0] * self.params.volume_multiplier
            if self.first_candle_volume < required_volume:
                print(f"Volume filter failed: {self.first_candle_volume} < {required_volume}")
                return False
        
        # ATR filter
        if self.params.use_atr_filter:
            current_atr = self.atr[0]
            avg_atr = self.atr_sma[0]
            min_atr = avg_atr * self.params.min_atr_multiplier
            max_atr = avg_atr * self.params.max_atr_multiplier
            
            if not (min_atr <= current_atr <= max_atr):
                print(f"ATR filter failed: {current_atr} not in [{min_atr}, {max_atr}]")
                return False
        
        # Range filter
        if self.params.use_range_filter:
            candle_range_pips = (self.first_candle_high - self.first_candle_low) * 10000  # Assuming 4-digit quotes
            
            if not (self.params.min_range_pips <= candle_range_pips <= self.params.max_range_pips):
                print(f"Range filter failed: {candle_range_pips} pips not in range")
                return False
            
            if candle_range_pips < self.range_percentile_value:
                print(f"Range percentile filter failed: {candle_range_pips} < {self.range_percentile_value}")
                return False
        
        # MTF filter
        if self.params.use_mtf and self.data_htf:
            htf_trend = self.get_htf_trend()
            if self.params.only_trend_direction and htf_trend == 'sideways':
                print("MTF filter failed: sideways trend")
                return False
        
        print("All filters passed!")
        return True
    
    def get_htf_trend(self):
        """Determine higher timeframe trend"""
        if not self.data_htf:
            return 'unknown'
        
        current_price = self.data_htf.close[0]
        ema_value = self.htf_ema[0]
        prev_ema = self.htf_ema[-1]
        
        if current_price > ema_value and ema_value > prev_ema:
            return 'up'
        elif current_price < ema_value and ema_value < prev_ema:
            return 'down'
        else:
            return 'sideways'
    
    def update_range_percentile(self):
        """Update range percentile calculation"""
        if len(self.data_30m) < 50:
            self.range_percentile_value = 5.0  # Default
            return
        
        # Calculate ranges for last 50 bars
        ranges = []
        for i in range(1, min(51, len(self.data_30m))):
            bar_range = (self.data_30m.high[-i] - self.data_30m.low[-i]) * 10000
            ranges.append(bar_range)
        
        if ranges:
            self.range_percentile_value = np.percentile(ranges, self.params.range_percentile)
    
    def place_pending_orders(self):
        """Place pending buy/sell stop orders"""
        # Calculate order levels
        offset = self.params.order_offset_pips / 10000  # Convert pips to price
        
        buy_price = self.first_candle_high + offset
        sell_price = self.first_candle_low - offset
        
        buy_sl = self.first_candle_low
        sell_sl = self.first_candle_high
        
        stop_distance_buy = buy_price - buy_sl
        stop_distance_sell = sell_sl - sell_price
        
        buy_tp = buy_price + (stop_distance_buy * self.params.risk_reward_ratio)
        sell_tp = sell_price - (stop_distance_sell * self.params.risk_reward_ratio)
        
        # Calculate position size
        account_value = self.broker.getvalue()
        risk_amount = account_value * (self.params.risk_percent / 100)
        
        # For simplicity, using fixed position size
        # In real implementation, would calculate based on stop distance
        position_size = risk_amount / (stop_distance_buy * 10000)  # Simplified
        
        # Determine which orders to place based on MTF
        place_buy = True
        place_sell = True
        
        if self.params.use_mtf and self.params.only_trend_direction:
            htf_trend = self.get_htf_trend()
            if htf_trend == 'up':
                place_sell = False
            elif htf_trend == 'down':
                place_buy = False
        
        # Place orders (simplified - using market orders when price is hit)
        current_price = self.data_30m.close[0]
        
        if place_buy and current_price >= buy_price:
            self.buy(size=position_size, exectype=bt.Order.Stop, price=buy_price)
            print(f"Buy stop order placed at {buy_price}")
        
        if place_sell and current_price <= sell_price:
            self.sell(size=position_size, exectype=bt.Order.Stop, price=sell_price)
            print(f"Sell stop order placed at {sell_price}")
    
    def manage_active_trades(self):
        """Manage active positions"""
        if not self.position:
            return
        
        # Time-based exit
        if self.params.max_trade_hours > 0 and self.position_info['open_time']:
            current_time = self.data_30m.datetime.datetime(0)
            time_diff = current_time - self.position_info['open_time']
            if time_diff.total_seconds() / 3600 >= self.params.max_trade_hours:
                self.close()
                print("Position closed due to time limit")
                return
        
        # Breakeven and trailing stop logic would go here
        # Simplified for demo
    
    def notify_order(self, order):
        """Handle order notifications"""
        if order.status in [order.Completed]:
            if order.isbuy():
                print(f"BUY EXECUTED at {order.executed.price}")
            else:
                print(f"SELL EXECUTED at {order.executed.price}")
            
            # Update position info
            self.position_info['open_time'] = self.data_30m.datetime.datetime(0)
            self.position_info['entry_price'] = order.executed.price
            self.position_info['original_size'] = order.executed.size
    
    def notify_trade(self, trade):
        """Handle trade notifications"""
        if trade.isclosed:
            profit_pips = (trade.pnl / abs(trade.size)) * 10000
            self.trades_log.append({
                'entry_time': trade.dtopen,
                'exit_time': trade.dtclose,
                'size': trade.size,
                'entry_price': trade.price,
                'exit_price': trade.price + trade.pnl/trade.size,
                'pnl': trade.pnl,
                'pnl_pips': profit_pips,
                'duration_hours': (trade.dtclose - trade.dtopen).total_seconds() / 3600
            })
            
            print(f"Trade closed: PnL = {trade.pnl:.2f} ({profit_pips:.1f} pips)")


class FirstCandleBacktester:
    """
    Main backtesting class with walk-forward optimization
    """
    
    def __init__(self, symbol='EURUSD=X', start_date='2023-01-01', end_date='2024-01-01'):
        self.symbol = symbol
        self.start_date = start_date
        self.end_date = end_date
        self.data_30m = None
        self.data_1h = None
        
    def download_data(self):
        """Download forex data from Yahoo Finance"""
        print(f"Downloading {self.symbol} data from {self.start_date} to {self.end_date}")
        
        # Download daily data and resample to 30m and 1h
        ticker = yf.Ticker(self.symbol)
        daily_data = ticker.history(start=self.start_date, end=self.end_date, interval='1d')
        
        if daily_data.empty:
            raise ValueError(f"No data found for {self.symbol}")
        
        # For demo purposes, we'll simulate 30m data from daily data
        # In real implementation, you'd use actual 30m data
        self.data_30m = daily_data.copy()
        self.data_1h = daily_data.copy()
        
        print(f"Downloaded {len(self.data_30m)} data points")
        return True

    def run_backtest(self, params=None):
        """Run a single backtest with given parameters"""
        if not self.download_data():
            return None

        cerebro = bt.Cerebro()

        # Add data feeds
        data_30m = bt.feeds.PandasData(dataname=self.data_30m)
        data_1h = bt.feeds.PandasData(dataname=self.data_1h)

        cerebro.adddata(data_30m, name='30m')
        cerebro.adddata(data_1h, name='1h')

        # Add strategy with parameters
        if params:
            cerebro.addstrategy(FirstCandleStrategy, **params)
        else:
            cerebro.addstrategy(FirstCandleStrategy)

        # Set broker settings
        cerebro.broker.setcash(10000.0)
        cerebro.broker.setcommission(commission=0.0001)  # 1 pip spread

        # Add analyzers
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')

        print(f"Starting backtest with initial capital: {cerebro.broker.getvalue():.2f}")

        # Run backtest
        results = cerebro.run()
        strategy = results[0]

        final_value = cerebro.broker.getvalue()
        print(f"Final portfolio value: {final_value:.2f}")

        # Extract results
        trade_analyzer = strategy.analyzers.trades.get_analysis()
        sharpe_analyzer = strategy.analyzers.sharpe.get_analysis()
        drawdown_analyzer = strategy.analyzers.drawdown.get_analysis()
        returns_analyzer = strategy.analyzers.returns.get_analysis()

        results_dict = {
            'final_value': final_value,
            'total_return': (final_value - 10000) / 10000 * 100,
            'total_trades': trade_analyzer.get('total', {}).get('total', 0),
            'winning_trades': trade_analyzer.get('won', {}).get('total', 0),
            'losing_trades': trade_analyzer.get('lost', {}).get('total', 0),
            'win_rate': 0,
            'avg_win': trade_analyzer.get('won', {}).get('pnl', {}).get('average', 0),
            'avg_loss': trade_analyzer.get('lost', {}).get('pnl', {}).get('average', 0),
            'max_drawdown': drawdown_analyzer.get('max', {}).get('drawdown', 0),
            'sharpe_ratio': sharpe_analyzer.get('sharperatio', 0),
            'trades_log': strategy.trades_log
        }

        # Calculate win rate
        total_trades = results_dict['total_trades']
        if total_trades > 0:
            results_dict['win_rate'] = (results_dict['winning_trades'] / total_trades) * 100

        return results_dict, cerebro

    def optimize_parameters(self, param_ranges):
        """Run parameter optimization"""
        print("Starting parameter optimization...")

        # Generate all parameter combinations
        param_names = list(param_ranges.keys())
        param_values = list(param_ranges.values())
        combinations = list(itertools.product(*param_values))

        print(f"Testing {len(combinations)} parameter combinations")

        results = []

        for i, combination in enumerate(combinations):
            params = dict(zip(param_names, combination))

            print(f"Testing combination {i+1}/{len(combinations)}: {params}")

            try:
                result, _ = self.run_backtest(params)
                if result:
                    result['parameters'] = params
                    results.append(result)
            except Exception as e:
                print(f"Error in combination {i+1}: {e}")
                continue

        # Sort by total return
        results.sort(key=lambda x: x['total_return'], reverse=True)

        print(f"Optimization complete. Best result: {results[0]['total_return']:.2f}%")

        return results

    def walk_forward_analysis(self, param_ranges, window_months=6, step_months=1):
        """Perform walk-forward analysis"""
        print("Starting walk-forward analysis...")

        start_date = pd.to_datetime(self.start_date)
        end_date = pd.to_datetime(self.end_date)

        results = []
        current_date = start_date

        while current_date + pd.DateOffset(months=window_months) <= end_date:
            # Define optimization and testing periods
            opt_start = current_date
            opt_end = current_date + pd.DateOffset(months=window_months)
            test_start = opt_end
            test_end = opt_end + pd.DateOffset(months=step_months)

            if test_end > end_date:
                break

            print(f"Optimization period: {opt_start.date()} to {opt_end.date()}")
            print(f"Testing period: {test_start.date()} to {test_end.date()}")

            # Create backtester for optimization period
            opt_backtester = FirstCandleBacktester(
                symbol=self.symbol,
                start_date=opt_start.strftime('%Y-%m-%d'),
                end_date=opt_end.strftime('%Y-%m-%d')
            )

            # Optimize parameters
            opt_results = opt_backtester.optimize_parameters(param_ranges)

            if not opt_results:
                print("No valid optimization results")
                current_date += pd.DateOffset(months=step_months)
                continue

            # Get best parameters
            best_params = opt_results[0]['parameters']
            print(f"Best parameters: {best_params}")

            # Test on out-of-sample period
            test_backtester = FirstCandleBacktester(
                symbol=self.symbol,
                start_date=test_start.strftime('%Y-%m-%d'),
                end_date=test_end.strftime('%Y-%m-%d')
            )

            test_result, _ = test_backtester.run_backtest(best_params)

            if test_result:
                wf_result = {
                    'opt_period_start': opt_start,
                    'opt_period_end': opt_end,
                    'test_period_start': test_start,
                    'test_period_end': test_end,
                    'best_params': best_params,
                    'opt_return': opt_results[0]['total_return'],
                    'test_return': test_result['total_return'],
                    'test_trades': test_result['total_trades'],
                    'test_win_rate': test_result['win_rate'],
                    'test_sharpe': test_result['sharpe_ratio'],
                    'test_max_dd': test_result['max_drawdown']
                }
                results.append(wf_result)

            current_date += pd.DateOffset(months=step_months)

        print(f"Walk-forward analysis complete. {len(results)} periods tested.")

        return results

    def analyze_results(self, results):
        """Analyze and visualize results"""
        if not results:
            print("No results to analyze")
            return

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(results)

        # Summary statistics
        print("\n=== WALK-FORWARD ANALYSIS SUMMARY ===")
        print(f"Total periods tested: {len(df)}")
        print(f"Average test return: {df['test_return'].mean():.2f}%")
        print(f"Median test return: {df['test_return'].median():.2f}%")
        print(f"Best test return: {df['test_return'].max():.2f}%")
        print(f"Worst test return: {df['test_return'].min():.2f}%")
        print(f"Standard deviation: {df['test_return'].std():.2f}%")
        print(f"Positive periods: {(df['test_return'] > 0).sum()}/{len(df)} ({(df['test_return'] > 0).mean()*100:.1f}%)")

        # Average metrics
        print(f"\nAverage win rate: {df['test_win_rate'].mean():.1f}%")
        print(f"Average Sharpe ratio: {df['test_sharpe'].mean():.2f}")
        print(f"Average max drawdown: {df['test_max_dd'].mean():.2f}%")

        # Plot results
        plt.figure(figsize=(15, 10))

        # Returns over time
        plt.subplot(2, 2, 1)
        plt.plot(df.index, df['test_return'], marker='o')
        plt.title('Test Period Returns')
        plt.xlabel('Period')
        plt.ylabel('Return (%)')
        plt.grid(True)

        # Return distribution
        plt.subplot(2, 2, 2)
        plt.hist(df['test_return'], bins=20, alpha=0.7)
        plt.title('Return Distribution')
        plt.xlabel('Return (%)')
        plt.ylabel('Frequency')
        plt.grid(True)

        # Win rate over time
        plt.subplot(2, 2, 3)
        plt.plot(df.index, df['test_win_rate'], marker='o', color='green')
        plt.title('Win Rate Over Time')
        plt.xlabel('Period')
        plt.ylabel('Win Rate (%)')
        plt.grid(True)

        # Sharpe ratio over time
        plt.subplot(2, 2, 4)
        plt.plot(df.index, df['test_sharpe'], marker='o', color='purple')
        plt.title('Sharpe Ratio Over Time')
        plt.xlabel('Period')
        plt.ylabel('Sharpe Ratio')
        plt.grid(True)

        plt.tight_layout()
        plt.savefig('walk_forward_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        return df


def main():
    """Main function to run the backtesting system"""

    # Initialize backtester
    backtester = FirstCandleBacktester(
        symbol='EURUSD=X',
        start_date='2023-01-01',
        end_date='2024-01-01'
    )

    # Define parameter ranges for optimization
    param_ranges = {
        'risk_percent': [0.5, 1.0, 1.5, 2.0],
        'risk_reward_ratio': [1.5, 2.0, 2.5, 3.0],
        'volume_multiplier': [1.0, 1.2, 1.5],
        'min_atr_multiplier': [0.8, 1.0, 1.2],
        'max_atr_multiplier': [2.5, 3.0, 3.5],
        'range_percentile': [60.0, 70.0, 80.0],
        'breakeven_ratio': [1.0, 1.2, 1.5],
        'trailing_pips': [10, 15, 20]
    }

    # Run single backtest with default parameters
    print("Running single backtest with default parameters...")
    result, cerebro = backtester.run_backtest()

    if result:
        print(f"\nSingle Backtest Results:")
        print(f"Total Return: {result['total_return']:.2f}%")
        print(f"Total Trades: {result['total_trades']}")
        print(f"Win Rate: {result['win_rate']:.1f}%")
        print(f"Sharpe Ratio: {result['sharpe_ratio']:.2f}")
        print(f"Max Drawdown: {result['max_drawdown']:.2f}%")

    # Run parameter optimization
    print("\nRunning parameter optimization...")
    opt_results = backtester.optimize_parameters(param_ranges)

    if opt_results:
        print(f"\nTop 5 Parameter Combinations:")
        for i, result in enumerate(opt_results[:5]):
            print(f"{i+1}. Return: {result['total_return']:.2f}%, "
                  f"Trades: {result['total_trades']}, "
                  f"Win Rate: {result['win_rate']:.1f}%, "
                  f"Params: {result['parameters']}")

    # Run walk-forward analysis
    print("\nRunning walk-forward analysis...")
    wf_results = backtester.walk_forward_analysis(param_ranges, window_months=3, step_months=1)

    if wf_results:
        # Analyze and visualize results
        df_results = backtester.analyze_results(wf_results)

        # Save results to CSV
        df_results.to_csv('walk_forward_results.csv', index=False)
        print("\nResults saved to 'walk_forward_results.csv'")


if __name__ == "__main__":
    main()
