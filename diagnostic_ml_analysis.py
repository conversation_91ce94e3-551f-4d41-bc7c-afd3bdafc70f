#!/usr/bin/env python3
"""
Diagnostic ML Analysis - Find and fix why no trades are being taken
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json
import os

# Import from previous files
from working_ml_analysis import PrecomputedMLStrategy, precompute_ml_predictions, run_precomputed_backtest
from fixed_improved_ml_analysis import RobustFeatureEngine, ImbalancedMLPredictor

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import classification_report, confusion_matrix
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

def analyze_ml_model_performance(features, labels, ml_predictor):
    """Analyze why ML model has low confidence"""
    
    print("🔍 ANALYZING ML MODEL PERFORMANCE")
    print("=" * 50)
    
    # Remove no-trade samples
    mask = labels != -1
    X = features[mask].copy()
    y = labels[mask].copy()
    
    print(f"Training data: {len(X)} samples")
    print(f"Positive samples: {sum(y)} ({sum(y)/len(y)*100:.1f}%)")
    print(f"Negative samples: {len(y) - sum(y)} ({(len(y) - sum(y))/len(y)*100:.1f}%)")
    
    # Get model predictions and probabilities
    if ml_predictor.is_trained:
        X_scaled = ml_predictor.scaler.transform(X)
        predictions = ml_predictor.model.predict(X_scaled)
        probabilities = ml_predictor.model.predict_proba(X_scaled)[:, 1]  # Probability of positive class
        
        print(f"\n📊 Model Performance:")
        print(f"Accuracy: {(predictions == y).mean():.3f}")
        print(f"Average confidence: {probabilities.mean():.3f}")
        print(f"Confidence std: {probabilities.std():.3f}")
        print(f"Min confidence: {probabilities.min():.3f}")
        print(f"Max confidence: {probabilities.max():.3f}")
        
        # Confidence distribution
        print(f"\n📈 Confidence Distribution:")
        print(f"< 0.3: {(probabilities < 0.3).sum()} samples ({(probabilities < 0.3).mean()*100:.1f}%)")
        print(f"0.3-0.5: {((probabilities >= 0.3) & (probabilities < 0.5)).sum()} samples ({((probabilities >= 0.3) & (probabilities < 0.5)).mean()*100:.1f}%)")
        print(f"0.5-0.6: {((probabilities >= 0.5) & (probabilities < 0.6)).sum()} samples ({((probabilities >= 0.5) & (probabilities < 0.6)).mean()*100:.1f}%)")
        print(f"0.6-0.7: {((probabilities >= 0.6) & (probabilities < 0.7)).sum()} samples ({((probabilities >= 0.6) & (probabilities < 0.7)).mean()*100:.1f}%)")
        print(f"> 0.7: {(probabilities > 0.7).sum()} samples ({(probabilities > 0.7).mean()*100:.1f}%)")
        
        # Classification report
        print(f"\n📋 Classification Report:")
        print(classification_report(y, predictions))
        
        return probabilities
    
    return None

def analyze_breakout_opportunities(data):
    """Analyze potential breakout opportunities in the data"""
    
    print("\n🔍 ANALYZING BREAKOUT OPPORTUNITIES")
    print("=" * 50)
    
    breakout_stats = []
    session_start_hour = 8
    
    # Group by date to find daily sessions
    for date, day_data in data.groupby(data.index.date):
        # Find session bars (8 AM hour)
        session_bars = day_data[
            (day_data.index.hour == session_start_hour) & 
            (day_data.index.minute.isin([0, 30]))
        ]
        
        if len(session_bars) >= 2:  # Need at least first hour
            first_hour = session_bars.iloc[:2]
            session_high = first_hour['High'].max()
            session_low = first_hour['Low'].min()
            range_pips = (session_high - session_low) * 10000
            
            # Check for breakouts after first hour
            remaining_day = day_data[day_data.index > first_hour.index[-1]]
            
            if len(remaining_day) > 0:
                max_high = remaining_day['High'].max()
                min_low = remaining_day['Low'].min()
                
                # Breakout detection
                upward_breakout = max_high > session_high + 0.0001
                downward_breakout = min_low < session_low - 0.0001
                
                breakout_stats.append({
                    'date': date,
                    'range_pips': range_pips,
                    'upward_breakout': upward_breakout,
                    'downward_breakout': downward_breakout,
                    'any_breakout': upward_breakout or downward_breakout,
                    'session_high': session_high,
                    'session_low': session_low
                })
    
    breakout_df = pd.DataFrame(breakout_stats)
    
    if len(breakout_df) > 0:
        print(f"Total trading days analyzed: {len(breakout_df)}")
        print(f"Days with breakouts: {breakout_df['any_breakout'].sum()} ({breakout_df['any_breakout'].mean()*100:.1f}%)")
        print(f"Upward breakouts: {breakout_df['upward_breakout'].sum()}")
        print(f"Downward breakouts: {breakout_df['downward_breakout'].sum()}")
        
        print(f"\n📊 Range Statistics:")
        print(f"Average range: {breakout_df['range_pips'].mean():.1f} pips")
        print(f"Median range: {breakout_df['range_pips'].median():.1f} pips")
        print(f"Min range: {breakout_df['range_pips'].min():.1f} pips")
        print(f"Max range: {breakout_df['range_pips'].max():.1f} pips")
        
        # Range distribution
        print(f"\n📈 Range Distribution:")
        print(f"< 5 pips: {(breakout_df['range_pips'] < 5).sum()} days ({(breakout_df['range_pips'] < 5).mean()*100:.1f}%)")
        print(f"5-10 pips: {((breakout_df['range_pips'] >= 5) & (breakout_df['range_pips'] < 10)).sum()} days")
        print(f"10-15 pips: {((breakout_df['range_pips'] >= 10) & (breakout_df['range_pips'] < 15)).sum()} days")
        print(f"15-20 pips: {((breakout_df['range_pips'] >= 15) & (breakout_df['range_pips'] < 20)).sum()} days")
        print(f"> 20 pips: {(breakout_df['range_pips'] > 20).sum()} days")
        
        return breakout_df
    
    return None

def test_relaxed_parameters(data, ml_predictions):
    """Test with much more relaxed parameters"""
    
    print("\n🧪 TESTING RELAXED PARAMETERS")
    print("=" * 50)
    
    # Very relaxed parameter sets
    relaxed_params = [
        {
            'ml_confidence_threshold': 0.4,  # Much lower
            'risk_reward_ratio': 1.5,        # Lower R:R
            'min_range_pips': 3.0,           # Lower minimum range
            'session_start_hour': 8,
            'max_trade_hours': 12
        },
        {
            'ml_confidence_threshold': 0.45,
            'risk_reward_ratio': 2.0,
            'min_range_pips': 5.0,
            'session_start_hour': 8,
            'max_trade_hours': 10
        },
        {
            'ml_confidence_threshold': 0.5,
            'risk_reward_ratio': 2.5,
            'min_range_pips': 8.0,
            'session_start_hour': 8,
            'max_trade_hours': 8
        }
    ]
    
    results = []
    
    for i, params in enumerate(relaxed_params):
        print(f"\n📊 Testing relaxed set {i+1}/3:")
        print(f"   ML Threshold: {params['ml_confidence_threshold']}")
        print(f"   Risk:Reward: {params['risk_reward_ratio']}")
        print(f"   Min Range: {params['min_range_pips']} pips")
        
        try:
            result = run_precomputed_backtest(data, ml_predictions, params)
            
            print(f"   Return: {result['total_return']:.2f}%")
            print(f"   Trades: {result['total_trades']}")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Sharpe: {result['sharpe_ratio']:.2f}")
            
            results.append(result)
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append(None)
    
    return results

def create_improved_labels(data):
    """Create better labels with different criteria"""
    
    print("\n🏷️ CREATING IMPROVED LABELS")
    print("=" * 50)
    
    labels = []
    session_start_hour = 8
    
    for i in range(len(data)):
        current_time = data.index[i].time()
        current_hour = current_time.hour
        
        # Only create labels for potential trade times (after 9 AM)
        if current_hour == session_start_hour + 1 and current_time.minute == 0:  # 9 AM
            # Look back to find session range
            session_data = data.iloc[max(0, i-3):i]  # Previous 3 bars (1.5 hours)
            
            if len(session_data) >= 2:
                session_high = session_data['High'].max()
                session_low = session_data['Low'].min()
                range_pips = (session_high - session_low) * 10000
                
                if range_pips >= 3:  # Minimum range
                    # Look forward to see if breakout is successful
                    future_data = data.iloc[i:i+16]  # Next 8 hours
                    
                    if len(future_data) > 0:
                        # Check for successful breakout
                        max_future = future_data['High'].max()
                        min_future = future_data['Low'].min()
                        
                        # More lenient success criteria
                        upward_success = (max_future - session_high) / session_high > 0.001  # 0.1%
                        downward_success = (session_low - min_future) / session_low > 0.001
                        
                        if upward_success or downward_success:
                            labels.append(1)  # Successful
                        else:
                            labels.append(0)  # Failed
                    else:
                        labels.append(0)
                else:
                    labels.append(-1)  # No trade (range too small)
            else:
                labels.append(-1)
        else:
            labels.append(-1)  # No trade opportunity
    
    labels_series = pd.Series(labels, index=data.index)
    
    print(f"Label distribution:")
    print(f"Successful: {(labels_series == 1).sum()}")
    print(f"Failed: {(labels_series == 0).sum()}")
    print(f"No trade: {(labels_series == -1).sum()}")
    print(f"Success rate: {(labels_series == 1).sum() / ((labels_series == 1).sum() + (labels_series == 0).sum()) * 100:.1f}%")
    
    return labels_series

def main():
    """Main diagnostic function"""
    
    print("🔍 DIAGNOSTIC ML ANALYSIS")
    print("=" * 60)
    print("Finding why no trades are being taken...")
    print()
    
    if not ML_AVAILABLE:
        print("❌ ML libraries not available")
        return False
    
    # Load data
    cleaned_files = [f for f in os.listdir('.') if 'cleaned.csv' in f]
    
    if not cleaned_files:
        print("❌ No cleaned CSV files found")
        return False
    
    print(f"📁 Using: {cleaned_files[0]}")
    data = pd.read_csv(cleaned_files[0], index_col=0, parse_dates=True)
    
    print(f"✅ Data loaded: {len(data)} bars")
    print(f"   Period: {data.index[0].date()} to {data.index[-1].date()}")
    
    # Analyze breakout opportunities first
    breakout_df = analyze_breakout_opportunities(data)
    
    # Extract features
    print("\n🔧 Extracting features...")
    feature_engine = RobustFeatureEngine()
    features = feature_engine.extract_features(data)
    
    # Create improved labels
    labels = create_improved_labels(data)
    
    # Align data
    common_index = features.index.intersection(labels.index)
    features_aligned = features.loc[common_index]
    labels_aligned = labels.loc[common_index]
    
    print(f"\nAligned data: {len(features_aligned)} samples")
    
    # Train ML model
    print("\n🧠 Training ML model...")
    ml_predictor = ImbalancedMLPredictor()
    success = ml_predictor.train(features_aligned, labels_aligned)
    
    if not success:
        print("❌ ML training failed")
        return False
    
    # Analyze ML model performance
    probabilities = analyze_ml_model_performance(features_aligned, labels_aligned, ml_predictor)
    
    # Pre-compute predictions
    print("\n🧠 Pre-computing ML predictions...")
    ml_predictions = precompute_ml_predictions(data, ml_predictor, feature_engine)
    
    print(f"ML predictions computed: {len(ml_predictions)}")
    if ml_predictions:
        pred_values = list(ml_predictions.values())
        print(f"Prediction stats:")
        print(f"  Mean: {np.mean(pred_values):.3f}")
        print(f"  Std: {np.std(pred_values):.3f}")
        print(f"  Min: {np.min(pred_values):.3f}")
        print(f"  Max: {np.max(pred_values):.3f}")
        print(f"  > 0.5: {sum(1 for p in pred_values if p > 0.5)} ({sum(1 for p in pred_values if p > 0.5)/len(pred_values)*100:.1f}%)")
        print(f"  > 0.6: {sum(1 for p in pred_values if p > 0.6)} ({sum(1 for p in pred_values if p > 0.6)/len(pred_values)*100:.1f}%)")
    
    # Test relaxed parameters
    relaxed_results = test_relaxed_parameters(data, ml_predictions)
    
    # Find best result
    best_result = None
    best_params = None
    
    for i, result in enumerate(relaxed_results):
        if result and result['total_trades'] > 0:
            if best_result is None or result['total_return'] > best_result['total_return']:
                best_result = result
                best_params = result['parameters']
    
    # Display final results
    print("\n🏆 DIAGNOSTIC RESULTS:")
    print("=" * 40)
    
    if best_result and best_result['total_trades'] > 0:
        print(f"✅ Found working parameters!")
        print(f"Total Return: {best_result['total_return']:.2f}%")
        print(f"Win Rate: {best_result['win_rate']:.1f}%")
        print(f"Total Trades: {best_result['total_trades']}")
        print(f"Sharpe Ratio: {best_result['sharpe_ratio']:.2f}")
        
        print(f"\n⚙️ Working Parameters:")
        for key, value in best_params.items():
            print(f"   {key}: {value}")
    else:
        print("❌ Still no trades found even with relaxed parameters")
        print("\n💡 Possible issues:")
        print("   - ML model is too conservative")
        print("   - Breakout detection logic is flawed")
        print("   - Data doesn't contain suitable patterns")
        print("   - Need different strategy approach")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    print("=" * 40)
    
    if breakout_df is not None:
        avg_range = breakout_df['range_pips'].mean()
        breakout_rate = breakout_df['any_breakout'].mean()
        
        print(f"1. Average range is {avg_range:.1f} pips")
        print(f"2. Breakout rate is {breakout_rate*100:.1f}%")
        
        if avg_range < 8:
            print("   → Consider lowering min_range_pips to 3-5")
        if breakout_rate < 0.3:
            print("   → Market may not be suitable for breakout strategy")
    
    if probabilities is not None:
        high_conf_rate = (probabilities > 0.6).mean()
        print(f"3. Only {high_conf_rate*100:.1f}% of predictions have >60% confidence")
        if high_conf_rate < 0.1:
            print("   → Consider lowering ML threshold to 0.4-0.5")
    
    return True

if __name__ == "__main__":
    main()
