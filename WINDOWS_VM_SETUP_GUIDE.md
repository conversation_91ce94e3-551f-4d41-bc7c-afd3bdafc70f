# Windows VM Setup Guide for EA Optimization

This guide will walk you through setting up your Windows virtual machine (VM) in Parallels to run the Python-based optimization for your MetaTrader 5 Expert Advisor.

## Step 1: Set Up Your Windows VM

1.  **Install Windows:** If you haven't already, create a new virtual machine in Parallels and install a modern version of Windows (Windows 10 or 11).
2.  **Install Parallels Tools:** After Windows is installed, make sure to install Parallels Tools. This is crucial for seamless integration, including file sharing and copy-pasting between macOS and Windows. You can usually do this from the Parallels menu: `Actions > Install Parallels Tools`.

## Step 2: Share Project Files

The easiest way to get your project files (`ultra.mq5`, `optimization_manager.py`, etc.) into your Windows VM is through Parallels' shared folders feature.

1.  **Configure Shared Folders:** In your VM's configuration (`Actions > Configure > Options > Sharing`), you can share your Mac's home folder or a specific project folder with Windows.
2.  **Access Files in Windows:** Once shared, your Mac folders will appear as a network drive in the Windows File Explorer (e.g., `Z:\home`). Navigate to your project directory from within Windows.

## Step 3: Install Software in Windows

Now, inside your Windows VM, you need to install the necessary software.

1.  **Install MetaTrader 5:**
    *   Open a web browser in Windows and download the MetaTrader 5 terminal from your broker or the official MetaQuotes website.
    *   Install it to the default location (`C:\Program Files\MetaTrader 5`).

2.  **Install Python:**
    *   Visit the official Python website (`python.org`) from within your Windows VM.
    *   Download and install the latest stable version of Python for Windows.
    *   **IMPORTANT:** During installation, make sure to check the box that says **"Add Python to PATH"**.

## Step 4: Set Up the Project in Windows

1.  **Copy Project Files:** Copy your entire project folder from the shared Mac drive to a location inside Windows (e.g., `C:\Users\<USER>\Documents\day_trading`).
2.  **Update `optimization_manager.py`:**
    *   Open the `optimization_manager.py` script inside Windows.
    *   Verify that the `MT5_PATH` variable is correct for a standard Windows installation. It should be:
        ```python
        MT5_PATH = "C:/Program Files/MetaTrader 5/terminal64.exe"
        ```
3.  **Create Virtual Environment:**
    *   Open the Command Prompt (cmd) or PowerShell in Windows.
    *   Navigate to your project directory (e.g., `cd C:\Users\<USER>\Documents\day_trading`).
    *   Create the virtual environment:
        ```bash
        python -m venv .venv
        ```
4.  **Activate and Install Dependencies:**
    *   Activate the new environment:
        ```bash
        .venv\Scripts\activate
        ```
    *   Install the required libraries:
        ```bash
        pip install MetaTrader5 optuna pandas
        ```

## Step 5: Run the Optimization

You are now ready to run the script.

1.  Ensure your virtual environment is activated (you should see `(.venv)` in your command prompt).
2.  Run the script:
    ```bash
    python optimization_manager.py
    ```

The script will now launch the MetaTrader 5 terminal within your Windows VM and begin the optimization process as intended.