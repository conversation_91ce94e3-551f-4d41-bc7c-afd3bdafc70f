//+------------------------------------------------------------------+
//|                                        StochasticDayTrader.mq5 |
//|                                  Copyright 2025, Your Name |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Name"
#property link      "https://www.mql5.com"
#property version   "1.03"

//--- Include libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- EA Input Parameters
//--- Stochastic Settings
input int      Stoch_K_Period      = 8;   // Stochastic %K period
input int      Stoch_D_Period      = 3;   // Stochastic %D period
input int      Stoch_Slowing       = 3;   // Stochastic slowing
input int      Stoch_Overbought    = 80;  // Stochastic overbought level
input int      Stoch_Oversold      = 20;  // Stochastic oversold level

//--- ATR Settings
input int      ATR_Period          = 14;  // ATR period
input double   ATR_SL_Multiplier   = 2.0; // ATR multiplier for Stop Loss
input double   ATR_TP1_Multiplier  = 1.5; // ATR multiplier for Take Profit 1
input double   ATR_TP2_Multiplier  = 3.0; // ATR multiplier for Take Profit 2
input double   ATR_TP3_Multiplier  = 4.5; // ATR multiplier for Take Profit 3

//--- Trade Management
input double   LotSize             = 0.01; // Fixed lot size (used as maximum when dynamic sizing enabled)
input ulong    MagicNumber         = 12345; // EA's magic number
input int      Slippage            = 3;    // Slippage in points

//--- Dynamic Position Sizing
input group    "=== POSITION SIZING ==="
input bool     UseDynamicSizing    = true;  // Enable dynamic position sizing
input double   RiskPercentPerTrade = 2.0;   // Risk percentage per trade
input double   MaxLotSize          = 0.10;  // Maximum lot size allowed

//--- Volume & Volatility Filters
input group    "=== MARKET FILTERS ==="
input bool     UseVolumeFilter     = true;  // Enable volume filter
input int      VolumePeriod        = 20;    // Period for volume average
input double   VolumeMultiplier    = 1.5;   // Minimum volume multiplier vs average
input bool     UseVolatilityFilter = true;  // Enable volatility filter
input int      VolatilityPeriod    = 14;    // Period for volatility calculation
input double   MinVolatilityATR    = 0.0001; // Minimum ATR for trading

//--- Trailing Stop
input bool     UseTrailingStop     = true; // Use trailing stop
input double   Trail_ATR_Multiplier = 1.5; // ATR multiplier for trailing stop

//--- Breakeven
input bool     UseBreakeven        = true; // Use breakeven
input int      Breakeven_Pips      = 1;   // Pips to add to breakeven price

//--- Basic Risk Management
input group    "=== DAILY LIMITS ==="
input bool     UseDailyLossLimit   = true;  // Enable daily loss limit
input double   DailyLossLimit      = 100.0; // Daily loss limit in account currency
input int      MaxDailyTrades      = 10;    // Maximum trades per day

input group    "=== DRAWDOWN PROTECTION ==="
input bool     UseDrawdownLimit    = true;  // Enable maximum drawdown protection
input double   MaxDrawdownPercent  = 15.0;  // Maximum drawdown % from account high
input double   MinAccountBalance   = 1000.0; // Minimum account balance to continue trading

input group    "=== POSITION LIMITS ==="
input int      MaxConcurrentTrades = 9;     // Maximum concurrent positions (3 sets of 3)
input bool     UsePositionLimits   = true;  // Enable position limits
input int      MinTradeSpacingMin  = 5;     // Minimum minutes between trade sets

input group    "=== TRADING HOURS ==="
input bool     UseTradingHours     = false; // Enable trading hours restriction
input int      StartHour           = 8;     // Trading start hour (server time)
input int      EndHour             = 18;    // Trading end hour (server time)

//--- Global variables
CTrade         trade;
CPositionInfo  posInfo;
int            stoch_handle;
int            atr_handle;
int            volume_ma_handle;    // Moving average of volume

//--- Risk Management Variables
datetime       last_reset_date;        // Last date when daily stats were reset
double         daily_start_balance;    // Account balance at start of day
double         daily_profit_loss;      // Current daily P&L
int            daily_trade_count;      // Number of trades opened today
double         account_high_balance;   // Highest account balance reached
bool           trading_allowed;        // Master switch for trading
datetime       last_trade_time;        // Time of last trade (for spacing)
int            tick_counter;           // Counter for periodic status updates

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
//---
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetDeviationInPoints(Slippage);
   trade.SetTypeFillingBySymbol(_Symbol);
   posInfo.Select(_Symbol);

//--- Create indicator handles
   stoch_handle = iStochastic(_Symbol, _Period, Stoch_K_Period, Stoch_D_Period, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);
   if(stoch_handle == INVALID_HANDLE)
     {
      Print("Error creating Stochastic indicator handle");
      return(INIT_FAILED);
     }

   atr_handle = iATR(_Symbol, _Period, ATR_Period);
   if(atr_handle == INVALID_HANDLE)
     {
      Print("Error creating ATR indicator handle");
      return(INIT_FAILED);
     }

   if(UseVolumeFilter)
     {
      volume_ma_handle = iMA(_Symbol, _Period, VolumePeriod, 0, MODE_SMA, VOLUME_TICK);
      if(volume_ma_handle == INVALID_HANDLE)
        {
         Print("Error creating Volume MA indicator handle");
         return(INIT_FAILED);
        }
     }

//--- Initialize risk management
   datetime current_time = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_time, dt);
   dt.hour = 0;
   dt.min = 0;
   dt.sec = 0;
   last_reset_date = StructToTime(dt);

   daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   account_high_balance = daily_start_balance;
   daily_profit_loss = 0.0;
   daily_trade_count = 0;
   trading_allowed = true;
   last_trade_time = 0;
   tick_counter = 0;

   PrintFormat("Risk Management Initialized - Start Balance: %.2f", daily_start_balance);

//--- Print broker specifications for debugging
   PrintBrokerSpecs();

//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
//---
   IndicatorRelease(stoch_handle);
   IndicatorRelease(atr_handle);
   if(UseVolumeFilter && volume_ma_handle != INVALID_HANDLE)
      IndicatorRelease(volume_ma_handle);
  }

//+------------------------------------------------------------------+
//| Print broker specifications for debugging                        |
//+------------------------------------------------------------------+
void PrintBrokerSpecs()
  {
   long stops_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
   long freeze_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL);
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);

   PrintFormat("=== Broker Specifications for %s ===", _Symbol);
   PrintFormat("Digits: %d", _Digits);
   PrintFormat("Point: %.5f", _Point);
   PrintFormat("Stops Level: %d points (%.5f)", stops_level, stops_level * _Point);
   PrintFormat("Freeze Level: %d points (%.5f)", freeze_level, freeze_level * _Point);
   PrintFormat("Min Lot: %.2f", min_lot);
   PrintFormat("Max Lot: %.2f", max_lot);
   PrintFormat("Lot Step: %.2f", lot_step);
   PrintFormat("Tick Size: %.5f", tick_size);
   PrintFormat("Tick Value: %.2f", tick_value);
   PrintFormat("=====================================");
  }

//+------------------------------------------------------------------+
//| Reset daily statistics at start of new trading day              |
//+------------------------------------------------------------------+
void ResetDailyStats()
  {
   datetime current_time = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_time, dt);

   // Check if it's a new day
   dt.hour = 0;
   dt.min = 0;
   dt.sec = 0;
   datetime today_start = StructToTime(dt);

   if(today_start > last_reset_date)
     {
      last_reset_date = today_start;
      daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      daily_profit_loss = 0.0;
      daily_trade_count = 0;
      trading_allowed = true;

      // Update account high if balance increased
      double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      if(current_balance > account_high_balance)
         account_high_balance = current_balance;

      PrintFormat("Daily stats reset - New day started. Balance: %.2f", daily_start_balance);
     }
  }

//+------------------------------------------------------------------+
//| Check if daily loss limit is exceeded                            |
//+------------------------------------------------------------------+
bool IsDailyLossLimitExceeded()
  {
   if(!UseDailyLossLimit)
      return false;

   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double daily_loss = daily_start_balance - current_balance;

   if(daily_loss >= DailyLossLimit)
     {
      PrintFormat("Daily loss limit exceeded: %.2f >= %.2f", daily_loss, DailyLossLimit);
      return true;
     }

   return false;
  }

//+------------------------------------------------------------------+
//| Check if maximum drawdown limit is exceeded                      |
//+------------------------------------------------------------------+
bool IsDrawdownLimitExceeded()
  {
   if(!UseDrawdownLimit)
      return false;

   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double drawdown_pct = ((account_high_balance - current_balance) / account_high_balance) * 100.0;

   if(drawdown_pct >= MaxDrawdownPercent)
     {
      PrintFormat("Maximum drawdown exceeded: %.2f%% >= %.2f%%", drawdown_pct, MaxDrawdownPercent);
      return true;
     }

   return false;
  }

//+------------------------------------------------------------------+
//| Check if position limits are exceeded                            |
//+------------------------------------------------------------------+
bool IsPositionLimitExceeded()
  {
   if(!UsePositionLimits)
      return false;

   int current_positions = 0;
   for(int i = 0; i < PositionsTotal(); i++)
     {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         current_positions++;
     }

   if(current_positions >= MaxConcurrentTrades)
     {
      PrintFormat("Position limit exceeded: %d >= %d", current_positions, MaxConcurrentTrades);
      return true;
     }

   if(daily_trade_count >= MaxDailyTrades)
     {
      PrintFormat("Daily trade limit exceeded: %d >= %d", daily_trade_count, MaxDailyTrades);
      return true;
     }

   // Check minimum time spacing between trades
   if(last_trade_time > 0)
     {
      datetime current_time = TimeCurrent();
      int minutes_since_last = (int)((current_time - last_trade_time) / 60);
      if(minutes_since_last < MinTradeSpacingMin)
        {
         PrintFormat("Trade spacing too close: %d min < %d min required", minutes_since_last, MinTradeSpacingMin);
         return true;
        }
     }

   return false;
  }

//+------------------------------------------------------------------+
//| Master risk validation function                                  |
//+------------------------------------------------------------------+
bool IsRiskManagementValid()
  {
   // Reset daily stats if new day
   ResetDailyStats();

   // Update account high
   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   if(current_balance > account_high_balance)
      account_high_balance = current_balance;

   // Check all risk parameters
   if(IsDailyLossLimitExceeded())
     {
      trading_allowed = false;
      return false;
     }

   if(IsDrawdownLimitExceeded())
     {
      trading_allowed = false;
      return false;
     }

   if(current_balance <= MinAccountBalance)
     {
      PrintFormat("Minimum account balance reached: %.2f <= %.2f", current_balance, MinAccountBalance);
      trading_allowed = false;
      return false;
     }

   if(IsPositionLimitExceeded())
     {
      return false;
     }

   // Check trading hours
   if(UseTradingHours)
     {
      MqlDateTime dt;
      TimeToStruct(TimeCurrent(), dt);
      int current_hour = dt.hour;

      if(StartHour <= EndHour)
        {
         if(current_hour < StartHour || current_hour >= EndHour)
            return false;
        }
      else
        {
         if(current_hour < StartHour && current_hour >= EndHour)
            return false;
        }
     }

   return true;
  }

//+------------------------------------------------------------------+
//| Calculate dynamic lot size based on risk percentage              |
//+------------------------------------------------------------------+
double CalculateDynamicLotSize(double sl_distance)
  {
   if(!UseDynamicSizing)
      return LotSize;

   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = account_balance * (RiskPercentPerTrade / 100.0);

   // Calculate pip value for current symbol
   double pip_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   if(_Digits == 5 || _Digits == 3)
      pip_value *= 10; // Adjust for 5-digit brokers

   // Calculate lot size based on risk
   double sl_distance_pips = sl_distance / _Point;
   if(_Digits == 5 || _Digits == 3)
      sl_distance_pips /= 10;

   double calculated_lot = 0.0;
   if(sl_distance_pips > 0 && pip_value > 0)
      calculated_lot = risk_amount / (sl_distance_pips * pip_value);

   // Get symbol lot constraints
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   // Apply our maximum limit
   if(calculated_lot > MaxLotSize)
      calculated_lot = MaxLotSize;

   // Don't exceed the original fixed lot size unless specifically allowed
   if(calculated_lot > LotSize)
      calculated_lot = LotSize;

   // Normalize to lot step
   calculated_lot = MathFloor(calculated_lot / lot_step) * lot_step;

   // Apply broker constraints
   if(calculated_lot < min_lot)
      calculated_lot = min_lot;
   if(calculated_lot > max_lot)
      calculated_lot = max_lot;

   PrintFormat("Dynamic sizing: Risk=%.2f%%, Amount=%.2f, SL_pips=%.1f, Lot=%.2f",
               RiskPercentPerTrade, risk_amount, sl_distance_pips, calculated_lot);

   return calculated_lot;
  }

//+------------------------------------------------------------------+
//| Check if current volume meets minimum requirements               |
//+------------------------------------------------------------------+
bool IsVolumeFilterValid()
  {
   if(!UseVolumeFilter)
      return true;

   // Get current volume
   long current_volume[];
   if(CopyTickVolume(_Symbol, _Period, 0, 1, current_volume) < 1)
     {
      Print("Error getting current volume");
      return false;
     }

   // Get average volume
   double volume_ma[];
   ArraySetAsSeries(volume_ma, true);
   if(CopyBuffer(volume_ma_handle, 0, 0, 1, volume_ma) < 1)
     {
      Print("Error getting volume moving average");
      return false;
     }

   double current_vol = (double)current_volume[0];
   double average_vol = volume_ma[0];
   double required_vol = average_vol * VolumeMultiplier;

   bool volume_ok = current_vol >= required_vol;

   PrintFormat("Volume Filter: Current=%.0f, Average=%.0f, Required=%.0f, Valid=%s",
               current_vol, average_vol, required_vol, volume_ok ? "YES" : "NO");

   return volume_ok;
  }

//+------------------------------------------------------------------+
//| Check if current volatility meets minimum requirements           |
//+------------------------------------------------------------------+
bool IsVolatilityFilterValid()
  {
   if(!UseVolatilityFilter)
      return true;

   // Get current ATR value
   double atr_buffer[];
   ArraySetAsSeries(atr_buffer, true);
   if(CopyBuffer(atr_handle, 0, 0, 1, atr_buffer) < 1)
     {
      Print("Error getting ATR for volatility filter");
      return false;
     }

   double current_atr = atr_buffer[0];
   bool volatility_ok = current_atr >= MinVolatilityATR;

   PrintFormat("Volatility Filter: Current ATR=%.5f, Min Required=%.5f, Valid=%s",
               current_atr, MinVolatilityATR, volatility_ok ? "YES" : "NO");

   return volatility_ok;
  }

//+------------------------------------------------------------------+
//| Display current concurrent trade status                          |
//+------------------------------------------------------------------+
void PrintConcurrentTradeStatus()
  {
   int current_positions = 0;
   for(int i = 0; i < PositionsTotal(); i++)
     {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         current_positions++;
     }

   PrintFormat("Concurrent Trades: %d/%d positions open", current_positions, MaxConcurrentTrades);
  }
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
//---
   //--- Manage open positions on every tick
   ManageTrades();

   //--- Periodic status update (every 100 ticks)
   tick_counter++;
   if(tick_counter >= 100)
     {
      PrintConcurrentTradeStatus();
      tick_counter = 0;
     }

   //--- Check for new bar before running trading logic
   if(!IsNewBar())
      return;

   //--- Trading logic
   CheckSignals();
  }
//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckSignals()
  {
//--- First check risk management
   if(!IsRiskManagementValid())
     {
      return;
     }

//--- Check volume filter
   if(!IsVolumeFilterValid())
     {
      return;
     }

//--- Check volatility filter
   if(!IsVolatilityFilterValid())
     {
      return;
     }

//--- Concurrent trade management is handled by IsPositionLimitExceeded() in risk management
//--- This allows multiple trade sets up to MaxConcurrentTrades limit

//--- Get Stochastic values
   double stoch_main_buffer[], stoch_signal_buffer[];
   ArraySetAsSeries(stoch_main_buffer, true);
   ArraySetAsSeries(stoch_signal_buffer, true);

   if(CopyBuffer(stoch_handle, 0, 0, 3, stoch_main_buffer) < 3 || CopyBuffer(stoch_handle, 1, 0, 3, stoch_signal_buffer) < 3)
     {
      Print("Error copying stochastic buffers");
      return;
     }

   double stoch_main_1 = stoch_main_buffer[1];
   double stoch_signal_1 = stoch_signal_buffer[1];
   double stoch_main_2 = stoch_main_buffer[2];
   double stoch_signal_2 = stoch_signal_buffer[2];

//--- Buy Signal: Main line crosses above signal line in the oversold zone
   if(stoch_main_2 < stoch_signal_2 && stoch_main_1 > stoch_signal_1 && stoch_main_1 < Stoch_Oversold)
     {
      OpenTrade(ORDER_TYPE_BUY);
     }

//--- Sell Signal: Main line crosses below signal line in the overbought zone
   if(stoch_main_2 > stoch_signal_2 && stoch_main_1 < stoch_signal_1 && stoch_main_1 > Stoch_Overbought)
     {
      OpenTrade(ORDER_TYPE_SELL);
     }
  }

//+------------------------------------------------------------------+
//| Calculate valid stop level based on broker requirements          |
//+------------------------------------------------------------------+
double CalculateValidStopLevel(double entry_price, double desired_distance, bool is_stop_loss, ENUM_ORDER_TYPE order_type)
  {
   // Get broker minimum stop level
   long stops_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
   long freeze_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL);
   
   // Use the larger of the two as minimum distance in points
   long min_distance_points = MathMax(stops_level, freeze_level);
   
   // Convert to price units
   double min_distance_price = min_distance_points * _Point;
   
   // If broker doesn't specify minimum, use reasonable defaults
   if(min_distance_price <= 0)
     {
      if(_Digits == 5 || _Digits == 3) 
         min_distance_price = 30 * _Point;  // 3 pips for 5-digit
      else if(_Digits == 4 || _Digits == 2) 
         min_distance_price = 3 * _Point;   // 3 pips for 4-digit
      else
         min_distance_price = 20 * _Point;  // Default fallback
     }
   
   // Ensure desired distance meets minimum requirements
   double validated_distance = MathMax(desired_distance, min_distance_price);
   
   // Add buffer to avoid rejection (20% extra)
   validated_distance *= 1.2;
   
   // Calculate final price level
   double final_level = 0.0;
   
   if(order_type == ORDER_TYPE_BUY)
     {
      if(is_stop_loss)
         final_level = entry_price - validated_distance;  // SL below entry for BUY
      else
         final_level = entry_price + validated_distance;  // TP above entry for BUY
     }
   else // ORDER_TYPE_SELL
     {
      if(is_stop_loss)
         final_level = entry_price + validated_distance;  // SL above entry for SELL
      else
         final_level = entry_price - validated_distance;  // TP below entry for SELL
     }
   
   // Normalize and return
   return NormalizeDouble(final_level, _Digits);
  }

//+------------------------------------------------------------------+
//| Open a new trade with proper price level calculations            |
//+------------------------------------------------------------------+
void OpenTrade(ENUM_ORDER_TYPE type)
  {
   PrintFormat("=== OPENING %s TRADE ===", type == ORDER_TYPE_BUY ? "BUY" : "SELL");
   
   // Get current market prices
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double entry_price = (type == ORDER_TYPE_BUY) ? ask : bid;
   
   PrintFormat("Market prices: ASK=%.5f, BID=%.5f, Entry=%.5f", ask, bid, entry_price);
   
   // Get ATR for distance calculations
   double atr_buffer[];
   ArraySetAsSeries(atr_buffer, true);
   if(CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
     {
      Print("ERROR: Cannot get ATR values");
      return;
     }
   double atr = atr_buffer[1];
   PrintFormat("ATR: %.5f", atr);
   
   // Calculate desired distances based on ATR
   double sl_distance = atr * ATR_SL_Multiplier;
   double tp1_distance = atr * ATR_TP1_Multiplier;
   double tp2_distance = atr * ATR_TP2_Multiplier;
   double tp3_distance = atr * ATR_TP3_Multiplier;
   
   PrintFormat("ATR-based distances: SL=%.5f, TP1=%.5f, TP2=%.5f, TP3=%.5f",
               sl_distance, tp1_distance, tp2_distance, tp3_distance);
   
   // Calculate valid price levels using the helper function
   double sl_level = CalculateValidStopLevel(entry_price, sl_distance, true, type);
   double tp1_level = CalculateValidStopLevel(entry_price, tp1_distance, false, type);
   double tp2_level = CalculateValidStopLevel(entry_price, tp2_distance * 1.5, false, type);
   double tp3_level = CalculateValidStopLevel(entry_price, tp3_distance * 2.0, false, type);
   
   PrintFormat("Final price levels: Entry=%.5f, SL=%.5f, TP1=%.5f, TP2=%.5f, TP3=%.5f",
               entry_price, sl_level, tp1_level, tp2_level, tp3_level);
   
   // Validate price levels
   bool levels_valid = true;
   
   if(sl_level <= 0 || tp1_level <= 0 || tp2_level <= 0 || tp3_level <= 0)
     {
      Print("ERROR: Invalid price levels (negative or zero)");
      levels_valid = false;
     }
   
   if(type == ORDER_TYPE_BUY)
     {
      if(sl_level >= entry_price)
        {
         PrintFormat("ERROR: BUY SL (%.5f) >= Entry (%.5f)", sl_level, entry_price);
         levels_valid = false;
        }
      if(tp1_level <= entry_price)
        {
         PrintFormat("ERROR: BUY TP1 (%.5f) <= Entry (%.5f)", tp1_level, entry_price);
         levels_valid = false;
        }
     }
   else // SELL
     {
      if(sl_level <= entry_price)
        {
         PrintFormat("ERROR: SELL SL (%.5f) <= Entry (%.5f)", sl_level, entry_price);
         levels_valid = false;
        }
      if(tp1_level >= entry_price)
        {
         PrintFormat("ERROR: SELL TP1 (%.5f) >= Entry (%.5f)", tp1_level, entry_price);
         levels_valid = false;
        }
     }
   
   if(!levels_valid)
     {
      Print("Trade cancelled due to invalid levels");
      return;
     }
   
   // Calculate lot size
   double total_lot = CalculateDynamicLotSize(MathAbs(entry_price - sl_level));
   double partial_lot = NormalizeDouble(total_lot / 3.0, 2);
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   
   if(partial_lot < min_lot)
      partial_lot = min_lot;
   
   PrintFormat("Lot sizes: Total=%.2f, Partial=%.2f", total_lot, partial_lot);
   
   // Execute the trades
   PrintFormat("Executing 3 trades...");
   
   // Use 0.0 for SL/TP initially, then modify positions
   bool result1 = trade.PositionOpen(_Symbol, type, partial_lot, entry_price, 0.0, 0.0, "Opening_1");
   PrintFormat("Trade 1 result: %s (Code: %d)", result1 ? "SUCCESS" : "FAILED", trade.ResultRetcode());
   
   if(result1)
     {
      // Get the position ticket and modify it with proper SL/TP
      ulong ticket1 = trade.ResultOrder();
      if(PositionSelectByTicket(ticket1))
        {
         bool mod1 = trade.PositionModify(ticket1, sl_level, tp1_level);
         PrintFormat("Trade 1 modification: %s", mod1 ? "SUCCESS" : "FAILED");
        }
     }
   
   bool result2 = trade.PositionOpen(_Symbol, type, partial_lot, entry_price, 0.0, 0.0, "Opening_2");
   PrintFormat("Trade 2 result: %s (Code: %d)", result2 ? "SUCCESS" : "FAILED", trade.ResultRetcode());
   
   if(result2)
     {
      ulong ticket2 = trade.ResultOrder();
      if(PositionSelectByTicket(ticket2))
        {
         bool mod2 = trade.PositionModify(ticket2, sl_level, tp2_level);
         PrintFormat("Trade 2 modification: %s", mod2 ? "SUCCESS" : "FAILED");
        }
     }
   
   bool result3 = trade.PositionOpen(_Symbol, type, partial_lot, entry_price, 0.0, 0.0, "Opening_3");
   PrintFormat("Trade 3 result: %s (Code: %d)", result3 ? "SUCCESS" : "FAILED", trade.ResultRetcode());
   
   if(result3)
     {
      ulong ticket3 = trade.ResultOrder();
      if(PositionSelectByTicket(ticket3))
        {
         bool mod3 = trade.PositionModify(ticket3, sl_level, tp3_level);
         PrintFormat("Trade 3 modification: %s", mod3 ? "SUCCESS" : "FAILED");
        }
     }
   
   // Update counters if any trade succeeded
   if(result1 || result2 || result3)
     {
      daily_trade_count++;
      last_trade_time = TimeCurrent();
      PrintFormat("Trade set completed. Daily count: %d", daily_trade_count);
     }
   
   PrintFormat("=== END TRADE EXECUTION ===");
  }

//+------------------------------------------------------------------+
//| Manage open trades                                               |
//+------------------------------------------------------------------+
void ManageTrades()
  {
//--- Breakeven logic
   if(UseBreakeven && PositionsTotal() == 2)
     {
      for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
         if(posInfo.SelectByIndex(i) && posInfo.Magic() == MagicNumber && posInfo.Symbol() == _Symbol)
           {
            double open_price = posInfo.PriceOpen();
            double sl = posInfo.StopLoss();
            ENUM_POSITION_TYPE type = posInfo.PositionType();

            if(type == POSITION_TYPE_BUY && sl < open_price)
              {
               double new_sl = NormalizeDouble(open_price + Breakeven_Pips * _Point, _Digits);
               trade.PositionModify(posInfo.Ticket(), new_sl, posInfo.TakeProfit());
              }
            else if(type == POSITION_TYPE_SELL && sl > open_price)
              {
               double new_sl = NormalizeDouble(open_price - Breakeven_Pips * _Point, _Digits);
               trade.PositionModify(posInfo.Ticket(), new_sl, posInfo.TakeProfit());
              }
           }
        }
     }

//--- Trailing stop logic
   if(UseTrailingStop)
     {
      double atr_buffer[];
      ArraySetAsSeries(atr_buffer, true);
      if(CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
        {
         Print("Error copying ATR buffer for trailing stop");
         return;
        }
      double atr = atr_buffer[1];

      for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
         if(posInfo.SelectByIndex(i) && posInfo.Magic() == MagicNumber && posInfo.Symbol() == _Symbol)
           {
            double current_price = SymbolInfoDouble(_Symbol, posInfo.PositionType() == POSITION_TYPE_BUY ? SYMBOL_BID : SYMBOL_ASK);
            double open_price = posInfo.PriceOpen();
            double current_sl = posInfo.StopLoss();

            if(posInfo.PositionType() == POSITION_TYPE_BUY)
              {
               double trail_distance = atr * Trail_ATR_Multiplier;
               double new_sl = CalculateValidStopLevel(current_price, trail_distance, true, ORDER_TYPE_BUY);
               if(new_sl > open_price && new_sl > current_sl)
                 {
                  trade.PositionModify(posInfo.Ticket(), new_sl, posInfo.TakeProfit());
                 }
              }
            else // POSITION_TYPE_SELL
              {
               double trail_distance = atr * Trail_ATR_Multiplier;
               double new_sl = CalculateValidStopLevel(current_price, trail_distance, true, ORDER_TYPE_SELL);
               if(new_sl < open_price && (current_sl == 0 || new_sl < current_sl))
                 {
                  trade.PositionModify(posInfo.Ticket(), new_sl, posInfo.TakeProfit());
                 }
              }
           }
        }
     }
  }
//+------------------------------------------------------------------+
//| Check for a new bar                                              |
//+------------------------------------------------------------------+
bool IsNewBar()
  {
   static datetime last_bar_time=0;
   datetime current_bar_time=iTime(_Symbol,_Period,0);
   if(last_bar_time!=current_bar_time)
     {
      last_bar_time=current_bar_time;
      return(true);
     }
   return(false);
  }
//+------------------------------------------------------------------+