# First Candle EA Backtesting System for Mac

This Python-based backtesting system replicates your MT5 First Candle EA functionality and provides walk-forward optimization capabilities, specifically designed to work on Mac (and other platforms).

## 🚀 Quick Setup

### 1. Install Python Dependencies

```bash
# Create virtual environment (recommended)
python3 -m venv first_candle_env
source first_candle_env/bin/activate  # On Mac/Linux
# first_candle_env\Scripts\activate  # On Windows

# Install required packages
pip install -r requirements.txt
```

### 2. Run the Backtester

```bash
python first_candle_backtester.py
```

## 📊 Data Sources (Mac-Compatible)

### Option 1: Yahoo Finance (Default - Free)
- **Pros**: Free, reliable, works on Mac
- **Cons**: Limited to daily data for forex, may need resampling
- **Usage**: Already implemented in the code

### Option 2: Alpha Vantage (Recommended for Forex)
```python
# Add to requirements.txt: alpha_vantage>=2.3.1
from alpha_vantage.foreignexchange import ForeignExchange

# Get free API key from: https://www.alphavantage.co/support/#api-key
api_key = 'YOUR_API_KEY'
fx = ForeignExchange(key=api_key, output_format='pandas')
data, meta_data = fx.get_currency_exchange_intraday('EUR', 'USD', interval='30min')
```

### Option 3: OANDA API (Professional)
```python
# Add to requirements.txt: oandapyV20>=0.6.3
import oandapyV20
import oandapyV20.endpoints.instruments as instruments

# Get API key from OANDA
api = oandapyV20.API(access_token="YOUR_TOKEN")
params = {"granularity": "M30", "count": 5000}
r = instruments.InstrumentsCandles(instrument="EUR_USD", params=params)
```

### Option 4: MetaTrader 5 Data Export (Workaround)
Since MT5 Python library doesn't work on Mac, export data from MT5 on Windows:

```python
# On Windows machine with MT5:
import MetaTrader5 as mt5
import pandas as pd

mt5.initialize()
rates = mt5.copy_rates_range("EURUSD", mt5.TIMEFRAME_M30, 
                            datetime(2023,1,1), datetime(2024,1,1))
df = pd.DataFrame(rates)
df.to_csv('eurusd_m30_data.csv')
mt5.shutdown()

# Then use the CSV on Mac:
data = pd.read_csv('eurusd_m30_data.csv', index_col=0, parse_dates=True)
```

## 🔧 Alternative Backtesting Frameworks

### Option 1: Vectorbt (High Performance)
```bash
pip install vectorbt
```

```python
import vectorbt as vbt
import pandas as pd

# Load data
data = vbt.YFData.download('EURUSD=X', start='2023-01-01', end='2024-01-01')

# Define signals
entries = your_entry_logic()
exits = your_exit_logic()

# Run backtest
portfolio = vbt.Portfolio.from_signals(data.get('Close'), entries, exits)
print(portfolio.stats())
```

### Option 2: Zipline (Quantopian-style)
```bash
pip install zipline-reloaded
```

### Option 3: PyAlgoTrade
```bash
pip install pyalgotrade
```

## 📈 Advanced Features Implementation

### 1. Real-time Data Integration
```python
# Using WebSocket for real-time data
import websocket
import json

def on_message(ws, message):
    data = json.loads(message)
    # Process real-time tick data
    
def on_error(ws, error):
    print(f"WebSocket error: {error}")

# Connect to broker's WebSocket feed
ws = websocket.WebSocketApp("wss://your-broker-websocket-url")
```

### 2. Machine Learning Integration
```python
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import TimeSeriesSplit

# Feature engineering
def create_features(data):
    features = pd.DataFrame()
    features['atr'] = calculate_atr(data)
    features['volume_ratio'] = data['volume'] / data['volume'].rolling(20).mean()
    features['range_percentile'] = calculate_range_percentile(data)
    return features

# Train model
X = create_features(historical_data)
y = calculate_future_returns(historical_data)

model = RandomForestClassifier()
tscv = TimeSeriesSplit(n_splits=5)
model.fit(X, y)
```

### 3. Portfolio Management
```python
class PortfolioManager:
    def __init__(self, initial_capital=10000):
        self.capital = initial_capital
        self.positions = {}
        self.equity_curve = []
    
    def calculate_position_size(self, risk_percent, stop_distance):
        risk_amount = self.capital * (risk_percent / 100)
        return risk_amount / stop_distance
    
    def update_equity(self):
        total_value = self.capital
        for symbol, position in self.positions.items():
            total_value += position.unrealized_pnl
        self.equity_curve.append(total_value)
```

## 🔄 Walk-Forward Optimization

The system includes sophisticated walk-forward analysis:

1. **Rolling Window Optimization**: Optimizes parameters on historical data
2. **Out-of-Sample Testing**: Tests optimized parameters on unseen data
3. **Performance Tracking**: Monitors degradation over time
4. **Parameter Stability**: Identifies robust parameter sets

### Customizing Walk-Forward Analysis

```python
# Custom optimization metrics
def custom_fitness_function(results):
    return (results['total_return'] * results['win_rate'] / 100) / max(results['max_drawdown'], 1)

# Multi-objective optimization
from scipy.optimize import differential_evolution

def optimize_multi_objective(param_ranges):
    def objective(params):
        # Convert params to dict
        param_dict = dict(zip(param_ranges.keys(), params))
        result = run_backtest(param_dict)
        
        # Multi-objective: maximize return, minimize drawdown
        return -(result['total_return'] / max(result['max_drawdown'], 1))
    
    bounds = [(min(v), max(v)) for v in param_ranges.values()]
    result = differential_evolution(objective, bounds)
    return result
```

## 📊 Visualization and Reporting

### Enhanced Plotting
```python
import plotly.graph_objects as go
from plotly.subplots import make_subplots

def create_interactive_report(results):
    fig = make_subplots(
        rows=3, cols=2,
        subplot_titles=['Equity Curve', 'Drawdown', 'Monthly Returns', 
                       'Trade Distribution', 'Parameter Stability', 'Risk Metrics']
    )
    
    # Add equity curve
    fig.add_trace(go.Scatter(y=results['equity_curve'], name='Equity'), row=1, col=1)
    
    # Add drawdown
    fig.add_trace(go.Scatter(y=results['drawdown'], name='Drawdown'), row=1, col=2)
    
    # Save as HTML
    fig.write_html("backtest_report.html")
```

### Performance Metrics
```python
def calculate_advanced_metrics(returns):
    metrics = {
        'sharpe_ratio': returns.mean() / returns.std() * np.sqrt(252),
        'sortino_ratio': returns.mean() / returns[returns < 0].std() * np.sqrt(252),
        'calmar_ratio': returns.mean() * 252 / abs(returns.cumsum().min()),
        'max_consecutive_losses': calculate_max_consecutive_losses(returns),
        'profit_factor': returns[returns > 0].sum() / abs(returns[returns < 0].sum()),
        'recovery_factor': returns.sum() / abs(returns.cumsum().min())
    }
    return metrics
```

## 🚀 Running the System

1. **Single Backtest**: Test with default parameters
2. **Parameter Optimization**: Find best parameter combinations
3. **Walk-Forward Analysis**: Validate robustness over time
4. **Live Trading Simulation**: Paper trade with optimized parameters

## 📝 Next Steps

1. **Data Integration**: Set up your preferred data source
2. **Strategy Refinement**: Add your specific EA logic
3. **Parameter Tuning**: Run optimization on your data
4. **Live Testing**: Deploy on paper trading account
5. **Production**: Move to live trading with risk management

## 🔧 Troubleshooting

### Common Issues on Mac:

1. **Python Version**: Ensure Python 3.8+ is installed
2. **Virtual Environment**: Always use virtual environments
3. **Data Access**: Some APIs may require VPN for certain regions
4. **Memory Usage**: Large optimizations may require more RAM

### Performance Tips:

1. **Parallel Processing**: Use multiprocessing for optimization
2. **Data Caching**: Cache downloaded data locally
3. **Vectorization**: Use NumPy operations instead of loops
4. **Memory Management**: Process data in chunks for large datasets

This system provides a complete alternative to MT5's Python integration while offering enhanced features and Mac compatibility.
