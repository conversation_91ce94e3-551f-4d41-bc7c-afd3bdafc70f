#!/usr/bin/env python3
"""
Performance Analyzer
====================

Advanced performance analysis system for backtesting results.
Calculates comprehensive metrics, risk analysis, and statistical validation.

Features:
- Risk-adjusted returns (Sharpe, Sortino, Calmar ratios)
- Drawdown analysis (maximum, average, recovery time)
- Trade analysis (win rate, profit factor, expectancy)
- Monthly/yearly performance breakdown
- Statistical significance testing
- Benchmark comparison
- Risk metrics (VaR, CVaR, volatility)

This module provides institutional-grade performance analysis
suitable for professional trading strategy evaluation.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
from scipy import stats
from dataclasses import dataclass

@dataclass
class PerformanceMetrics:
    """Container for comprehensive performance metrics"""
    # Return metrics
    total_return: float
    annual_return: float
    monthly_return: float
    daily_return: float
    
    # Risk metrics
    volatility: float
    max_drawdown: float
    var_95: float
    cvar_95: float
    
    # Risk-adjusted returns
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    
    # Trading metrics
    total_trades: int
    win_rate: float
    profit_factor: float
    expectancy: float
    
    # Statistical metrics
    skewness: float
    kurtosis: float
    best_month: float
    worst_month: float

class PerformanceAnalyzer:
    """
    Comprehensive performance analysis for trading strategies
    
    Analyzes backtest results and calculates institutional-grade
    performance metrics with statistical validation.
    """
    
    def __init__(self, config: dict = None):
        """
        Initialize performance analyzer
        
        Args:
            config: Analysis configuration
        """
        self.config = config or self._get_default_config()
        self.results = {}
        self.trade_analysis = {}
        self.risk_analysis = {}
        
        print(f"📊 Performance Analyzer initialized")
    
    def _get_default_config(self) -> dict:
        """Get default analyzer configuration"""
        return {
            # Risk-free rate for Sharpe ratio calculation
            'risk_free_rate': 0.02,  # 2% annual
            
            # Confidence levels for risk metrics
            'var_confidence': 0.95,
            'cvar_confidence': 0.95,
            
            # Analysis settings
            'trading_days_per_year': 252,
            'monthly_trading_days': 21,
            'benchmark_return': 0.05,  # 5% annual benchmark
            
            # Statistical settings
            'min_trades_for_stats': 30,
            'significance_level': 0.05,
            
            # Output settings
            'include_monthly_breakdown': True,
            'include_trade_analysis': True,
            'include_risk_analysis': True,
        }
    
    # =========================================================================
    # MAIN ANALYSIS INTERFACE
    # =========================================================================
    
    def analyze_performance(self, backtest_results: Dict, 
                          trade_history: List[Dict] = None) -> PerformanceMetrics:
        """
        Perform comprehensive performance analysis
        
        Args:
            backtest_results: Results from backtesting engine
            trade_history: Optional detailed trade history
        
        Returns:
            PerformanceMetrics: Comprehensive performance metrics
        """
        print(f"📈 Analyzing performance...")
        
        # Extract basic metrics
        basic_metrics = self._extract_basic_metrics(backtest_results)
        
        # Analyze trades
        if trade_history or 'trade_history' in backtest_results:
            trades = trade_history or backtest_results['trade_history']
            self.trade_analysis = self._analyze_trades(trades)
        
        # Calculate risk metrics
        self.risk_analysis = self._calculate_risk_metrics(backtest_results, trades if 'trades' in locals() else [])
        
        # Calculate risk-adjusted returns
        risk_adjusted_metrics = self._calculate_risk_adjusted_returns(basic_metrics, self.risk_analysis)
        
        # Statistical analysis
        statistical_metrics = self._calculate_statistical_metrics(trades if 'trades' in locals() else [])
        
        # Create comprehensive metrics object
        metrics = PerformanceMetrics(
            # Return metrics
            total_return=basic_metrics['total_return'],
            annual_return=basic_metrics['annual_return'],
            monthly_return=basic_metrics['monthly_return'],
            daily_return=basic_metrics['daily_return'],
            
            # Risk metrics
            volatility=self.risk_analysis['volatility'],
            max_drawdown=self.risk_analysis['max_drawdown'],
            var_95=self.risk_analysis['var_95'],
            cvar_95=self.risk_analysis['cvar_95'],
            
            # Risk-adjusted returns
            sharpe_ratio=risk_adjusted_metrics['sharpe_ratio'],
            sortino_ratio=risk_adjusted_metrics['sortino_ratio'],
            calmar_ratio=risk_adjusted_metrics['calmar_ratio'],
            
            # Trading metrics
            total_trades=self.trade_analysis.get('total_trades', 0),
            win_rate=self.trade_analysis.get('win_rate', 0),
            profit_factor=self.trade_analysis.get('profit_factor', 0),
            expectancy=self.trade_analysis.get('expectancy', 0),
            
            # Statistical metrics
            skewness=statistical_metrics.get('skewness', 0),
            kurtosis=statistical_metrics.get('kurtosis', 0),
            best_month=statistical_metrics.get('best_month', 0),
            worst_month=statistical_metrics.get('worst_month', 0)
        )
        
        # Store detailed results
        self.results = {
            'performance_metrics': metrics,
            'basic_metrics': basic_metrics,
            'trade_analysis': self.trade_analysis,
            'risk_analysis': self.risk_analysis,
            'risk_adjusted_metrics': risk_adjusted_metrics,
            'statistical_metrics': statistical_metrics
        }
        
        print(f"✅ Performance analysis completed")
        print(f"📊 Total return: {metrics.total_return:.2f}%")
        print(f"📈 Sharpe ratio: {metrics.sharpe_ratio:.2f}")
        print(f"📉 Max drawdown: {metrics.max_drawdown:.2f}%")
        
        return metrics
    
    # =========================================================================
    # BASIC METRICS CALCULATION
    # =========================================================================
    
    def _extract_basic_metrics(self, results: Dict) -> Dict:
        """Extract basic performance metrics from backtest results"""
        initial_balance = 1000.0  # Default assumption
        final_balance = results.get('final_balance', initial_balance)
        
        # Calculate total return
        total_return = (final_balance - initial_balance) / initial_balance * 100
        
        # Estimate time period (simplified)
        total_trades = results.get('total_trades', 0)
        trading_days = max(30, total_trades * 2)  # Rough estimate
        trading_years = trading_days / self.config['trading_days_per_year']
        
        # Annualized return
        if trading_years > 0:
            annual_return = ((final_balance / initial_balance) ** (1 / trading_years) - 1) * 100
        else:
            annual_return = 0
        
        # Monthly and daily returns
        monthly_return = annual_return / 12 if annual_return != 0 else 0
        daily_return = annual_return / self.config['trading_days_per_year'] if annual_return != 0 else 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'daily_return': daily_return,
            'initial_balance': initial_balance,
            'final_balance': final_balance,
            'estimated_trading_days': trading_days,
            'estimated_trading_years': trading_years
        }
    
    # =========================================================================
    # TRADE ANALYSIS
    # =========================================================================
    
    def _analyze_trades(self, trades: List[Dict]) -> Dict:
        """Analyze individual trades for detailed statistics"""
        if not trades:
            return {}
        
        trade_df = pd.DataFrame(trades)
        
        # Basic trade statistics
        total_trades = len(trade_df)
        winning_trades = len(trade_df[trade_df['realized_pnl'] > 0])
        losing_trades = len(trade_df[trade_df['realized_pnl'] < 0])
        breakeven_trades = len(trade_df[trade_df['realized_pnl'] == 0])
        
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        # Profit/Loss analysis
        gross_profit = trade_df[trade_df['realized_pnl'] > 0]['realized_pnl'].sum()
        gross_loss = abs(trade_df[trade_df['realized_pnl'] < 0]['realized_pnl'].sum())
        net_profit = trade_df['realized_pnl'].sum()
        
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Average wins/losses
        avg_win = trade_df[trade_df['realized_pnl'] > 0]['realized_pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trade_df[trade_df['realized_pnl'] < 0]['realized_pnl'].mean() if losing_trades > 0 else 0
        
        # Expectancy
        win_prob = win_rate / 100
        loss_prob = 1 - win_prob
        expectancy = (win_prob * avg_win) + (loss_prob * avg_loss)
        
        # Largest win/loss
        largest_win = trade_df['realized_pnl'].max()
        largest_loss = trade_df['realized_pnl'].min()
        
        # Trade duration analysis
        if 'entry_time' in trade_df.columns and 'close_time' in trade_df.columns:
            trade_df['duration'] = pd.to_datetime(trade_df['close_time']) - pd.to_datetime(trade_df['entry_time'])
            avg_duration = trade_df['duration'].mean()
            max_duration = trade_df['duration'].max()
            min_duration = trade_df['duration'].min()
        else:
            avg_duration = max_duration = min_duration = timedelta(0)
        
        # Consecutive analysis
        consecutive_stats = self._analyze_consecutive_trades(trade_df)
        
        # Monthly performance
        monthly_stats = self._analyze_monthly_performance(trade_df)
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'breakeven_trades': breakeven_trades,
            'win_rate': win_rate,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,
            'net_profit': net_profit,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'expectancy': expectancy,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'avg_duration': avg_duration,
            'max_duration': max_duration,
            'min_duration': min_duration,
            'consecutive_stats': consecutive_stats,
            'monthly_stats': monthly_stats
        }
    
    def _analyze_consecutive_trades(self, trade_df: pd.DataFrame) -> Dict:
        """Analyze consecutive wins and losses"""
        if trade_df.empty:
            return {}
        
        # Create win/loss sequence
        trade_df['result'] = trade_df['realized_pnl'].apply(lambda x: 'W' if x > 0 else 'L' if x < 0 else 'B')
        
        # Find consecutive runs
        consecutive_wins = []
        consecutive_losses = []
        current_run = 1
        current_type = trade_df.iloc[0]['result']
        
        for i in range(1, len(trade_df)):
            if trade_df.iloc[i]['result'] == current_type:
                current_run += 1
            else:
                if current_type == 'W':
                    consecutive_wins.append(current_run)
                elif current_type == 'L':
                    consecutive_losses.append(current_run)
                
                current_type = trade_df.iloc[i]['result']
                current_run = 1
        
        # Add final run
        if current_type == 'W':
            consecutive_wins.append(current_run)
        elif current_type == 'L':
            consecutive_losses.append(current_run)
        
        return {
            'max_consecutive_wins': max(consecutive_wins) if consecutive_wins else 0,
            'max_consecutive_losses': max(consecutive_losses) if consecutive_losses else 0,
            'avg_consecutive_wins': np.mean(consecutive_wins) if consecutive_wins else 0,
            'avg_consecutive_losses': np.mean(consecutive_losses) if consecutive_losses else 0,
            'total_win_streaks': len(consecutive_wins),
            'total_loss_streaks': len(consecutive_losses)
        }
    
    def _analyze_monthly_performance(self, trade_df: pd.DataFrame) -> Dict:
        """Analyze performance by month"""
        if trade_df.empty or 'close_time' not in trade_df.columns:
            return {}
        
        try:
            trade_df['close_month'] = pd.to_datetime(trade_df['close_time']).dt.to_period('M')
            monthly_pnl = trade_df.groupby('close_month')['realized_pnl'].sum()
            
            return {
                'monthly_returns': monthly_pnl.to_dict(),
                'best_month': monthly_pnl.max(),
                'worst_month': monthly_pnl.min(),
                'avg_monthly_return': monthly_pnl.mean(),
                'monthly_volatility': monthly_pnl.std(),
                'positive_months': len(monthly_pnl[monthly_pnl > 0]),
                'negative_months': len(monthly_pnl[monthly_pnl < 0]),
                'total_months': len(monthly_pnl)
            }
        except:
            return {}
    
    # =========================================================================
    # RISK ANALYSIS
    # =========================================================================
    
    def _calculate_risk_metrics(self, results: Dict, trades: List[Dict]) -> Dict:
        """Calculate comprehensive risk metrics"""
        if not trades:
            return {
                'volatility': 0,
                'max_drawdown': results.get('max_drawdown', 0),
                'var_95': 0,
                'cvar_95': 0,
                'downside_deviation': 0
            }
        
        trade_df = pd.DataFrame(trades)
        returns = trade_df['realized_pnl'].values
        
        # Volatility (standard deviation of returns)
        volatility = np.std(returns) if len(returns) > 1 else 0
        
        # Value at Risk (VaR) and Conditional VaR (CVaR)
        var_95 = np.percentile(returns, (1 - self.config['var_confidence']) * 100)
        cvar_95 = returns[returns <= var_95].mean() if len(returns[returns <= var_95]) > 0 else var_95
        
        # Downside deviation (volatility of negative returns)
        negative_returns = returns[returns < 0]
        downside_deviation = np.std(negative_returns) if len(negative_returns) > 1 else 0
        
        # Maximum drawdown (from results or calculate from equity curve)
        max_drawdown = results.get('max_drawdown', 0)
        
        # Drawdown analysis
        drawdown_analysis = self._analyze_drawdowns(trade_df)
        
        return {
            'volatility': volatility,
            'max_drawdown': max_drawdown,
            'var_95': var_95,
            'cvar_95': cvar_95,
            'downside_deviation': downside_deviation,
            'drawdown_analysis': drawdown_analysis
        }
    
    def _analyze_drawdowns(self, trade_df: pd.DataFrame) -> Dict:
        """Analyze drawdown periods and recovery times"""
        if trade_df.empty:
            return {}
        
        # Calculate cumulative P&L to create equity curve
        cumulative_pnl = trade_df['realized_pnl'].cumsum()
        running_max = cumulative_pnl.expanding().max()
        drawdown = cumulative_pnl - running_max
        
        # Find drawdown periods
        drawdown_periods = []
        in_drawdown = False
        start_idx = 0
        
        for i, dd in enumerate(drawdown):
            if dd < 0 and not in_drawdown:
                # Start of drawdown
                in_drawdown = True
                start_idx = i
            elif dd >= 0 and in_drawdown:
                # End of drawdown
                in_drawdown = False
                duration = i - start_idx
                max_dd = drawdown[start_idx:i].min()
                drawdown_periods.append({'duration': duration, 'max_drawdown': max_dd})
        
        # Handle ongoing drawdown
        if in_drawdown:
            duration = len(drawdown) - start_idx
            max_dd = drawdown[start_idx:].min()
            drawdown_periods.append({'duration': duration, 'max_drawdown': max_dd})
        
        if drawdown_periods:
            avg_drawdown_duration = np.mean([dd['duration'] for dd in drawdown_periods])
            max_drawdown_duration = max([dd['duration'] for dd in drawdown_periods])
            avg_drawdown_depth = np.mean([dd['max_drawdown'] for dd in drawdown_periods])
        else:
            avg_drawdown_duration = max_drawdown_duration = avg_drawdown_depth = 0
        
        return {
            'total_drawdown_periods': len(drawdown_periods),
            'avg_drawdown_duration': avg_drawdown_duration,
            'max_drawdown_duration': max_drawdown_duration,
            'avg_drawdown_depth': avg_drawdown_depth,
            'drawdown_periods': drawdown_periods
        }
    
    # =========================================================================
    # RISK-ADJUSTED RETURNS
    # =========================================================================
    
    def _calculate_risk_adjusted_returns(self, basic_metrics: Dict, risk_metrics: Dict) -> Dict:
        """Calculate risk-adjusted return metrics"""
        annual_return = basic_metrics['annual_return'] / 100  # Convert to decimal
        risk_free_rate = self.config['risk_free_rate']
        
        # Sharpe Ratio
        excess_return = annual_return - risk_free_rate
        annual_volatility = risk_metrics['volatility'] * np.sqrt(self.config['trading_days_per_year']) / 100
        
        if annual_volatility > 0:
            sharpe_ratio = excess_return / annual_volatility
        else:
            sharpe_ratio = 0
        
        # Sortino Ratio (uses downside deviation instead of total volatility)
        annual_downside_dev = risk_metrics['downside_deviation'] * np.sqrt(self.config['trading_days_per_year']) / 100
        
        if annual_downside_dev > 0:
            sortino_ratio = excess_return / annual_downside_dev
        else:
            sortino_ratio = 0
        
        # Calmar Ratio (annual return / max drawdown)
        max_drawdown_decimal = risk_metrics['max_drawdown'] / 100
        
        if max_drawdown_decimal > 0:
            calmar_ratio = annual_return / max_drawdown_decimal
        else:
            calmar_ratio = float('inf') if annual_return > 0 else 0
        
        return {
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'excess_return': excess_return,
            'annual_volatility': annual_volatility,
            'annual_downside_deviation': annual_downside_dev
        }
    
    # =========================================================================
    # STATISTICAL ANALYSIS
    # =========================================================================
    
    def _calculate_statistical_metrics(self, trades: List[Dict]) -> Dict:
        """Calculate statistical properties of returns"""
        if not trades or len(trades) < self.config['min_trades_for_stats']:
            return {
                'skewness': 0,
                'kurtosis': 0,
                'normality_test_p_value': 1.0,
                'is_normally_distributed': False,
                'statistical_significance': False
            }
        
        trade_df = pd.DataFrame(trades)
        returns = trade_df['realized_pnl'].values
        
        # Skewness and Kurtosis
        skewness = stats.skew(returns)
        kurtosis = stats.kurtosis(returns)
        
        # Normality test (Shapiro-Wilk for small samples, Kolmogorov-Smirnov for large)
        if len(returns) <= 5000:
            _, normality_p_value = stats.shapiro(returns)
        else:
            _, normality_p_value = stats.kstest(returns, 'norm')
        
        is_normally_distributed = normality_p_value > self.config['significance_level']
        
        # Test if mean return is significantly different from zero
        t_stat, t_test_p_value = stats.ttest_1samp(returns, 0)
        statistical_significance = t_test_p_value < self.config['significance_level']
        
        # Monthly analysis for best/worst months
        monthly_stats = self.trade_analysis.get('monthly_stats', {})
        best_month = monthly_stats.get('best_month', 0)
        worst_month = monthly_stats.get('worst_month', 0)
        
        return {
            'skewness': skewness,
            'kurtosis': kurtosis,
            'normality_test_p_value': normality_p_value,
            'is_normally_distributed': is_normally_distributed,
            'statistical_significance': statistical_significance,
            't_statistic': t_stat,
            't_test_p_value': t_test_p_value,
            'best_month': best_month,
            'worst_month': worst_month
        }
    
    # =========================================================================
    # UTILITY METHODS
    # =========================================================================
    
    def get_summary_report(self) -> str:
        """Generate a text summary of performance analysis"""
        if not self.results:
            return "No analysis results available"
        
        metrics = self.results['performance_metrics']
        
        report = []
        report.append("PERFORMANCE ANALYSIS SUMMARY")
        report.append("=" * 40)
        report.append("")
        
        # Return metrics
        report.append("RETURN METRICS")
        report.append("-" * 15)
        report.append(f"Total Return: {metrics.total_return:.2f}%")
        report.append(f"Annual Return: {metrics.annual_return:.2f}%")
        report.append(f"Monthly Return: {metrics.monthly_return:.2f}%")
        report.append("")
        
        # Risk metrics
        report.append("RISK METRICS")
        report.append("-" * 12)
        report.append(f"Max Drawdown: {metrics.max_drawdown:.2f}%")
        report.append(f"Volatility: {metrics.volatility:.2f}")
        report.append(f"VaR (95%): {metrics.var_95:.2f}")
        report.append("")
        
        # Risk-adjusted returns
        report.append("RISK-ADJUSTED RETURNS")
        report.append("-" * 20)
        report.append(f"Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
        report.append(f"Sortino Ratio: {metrics.sortino_ratio:.2f}")
        report.append(f"Calmar Ratio: {metrics.calmar_ratio:.2f}")
        report.append("")
        
        # Trading metrics
        report.append("TRADING METRICS")
        report.append("-" * 15)
        report.append(f"Total Trades: {metrics.total_trades}")
        report.append(f"Win Rate: {metrics.win_rate:.1f}%")
        report.append(f"Profit Factor: {metrics.profit_factor:.2f}")
        report.append(f"Expectancy: {metrics.expectancy:.2f}")
        
        return "\n".join(report)


def main():
    """Example usage of PerformanceAnalyzer"""
    print("📊 Performance Analyzer Example")
    print("===============================")
    
    # Create analyzer
    analyzer = PerformanceAnalyzer()
    
    # Example backtest results
    example_results = {
        'total_return': 25.0,
        'final_balance': 1250.0,
        'max_drawdown': 8.5,
        'total_trades': 45,
        'win_rate': 62.2,
        'profit_factor': 1.85
    }
    
    # Example trade history
    example_trades = [
        {'realized_pnl': 50.0, 'entry_time': '2024-01-01', 'close_time': '2024-01-02'},
        {'realized_pnl': -25.0, 'entry_time': '2024-01-03', 'close_time': '2024-01-04'},
        {'realized_pnl': 75.0, 'entry_time': '2024-01-05', 'close_time': '2024-01-06'},
    ]
    
    print("✅ This is an example of the PerformanceAnalyzer")
    print("🔄 To use in practice:")
    print("   1. Pass backtest results to analyze_performance()")
    print("   2. Get comprehensive metrics and analysis")
    print("   3. Generate reports with get_summary_report()")
    
    print(f"\n📊 Example analysis would calculate:")
    print(f"   - Risk-adjusted returns (Sharpe, Sortino, Calmar)")
    print(f"   - Risk metrics (VaR, drawdown analysis)")
    print(f"   - Trade statistics and consecutive analysis")
    print(f"   - Statistical validation and significance tests")


if __name__ == "__main__":
    main()