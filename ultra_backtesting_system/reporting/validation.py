#!/usr/bin/env python3
"""
Results Validator
=================

Comprehensive validation system for backtesting results.
Ensures data quality, detects anomalies, and validates strategy performance.

Features:
- Data quality validation
- Performance anomaly detection
- Statistical validation tests
- Overfitting detection
- Robustness checks
- Reality checks and sanity tests

This module provides critical validation to ensure backtesting results
are reliable and not artifacts of data issues or overfitting.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import warnings
from scipy import stats
from dataclasses import dataclass

@dataclass
class ValidationResult:
    """Container for validation results"""
    passed: bool
    warnings: List[str]
    errors: List[str]
    score: float  # 0-100 validation score
    details: Dict[str, Any]

class ResultsValidator:
    """
    Comprehensive validation system for backtesting results
    
    Performs multiple validation checks to ensure results are
    reliable and not due to data artifacts or overfitting.
    """
    
    def __init__(self, config: dict = None):
        """
        Initialize results validator
        
        Args:
            config: Validation configuration
        """
        self.config = config or self._get_default_config()
        self.validation_results = {}
        
        print(f"✅ Results Validator initialized")
    
    def _get_default_config(self) -> dict:
        """Get default validation configuration"""
        return {
            # Data quality thresholds
            'min_trades': 20,                    # Minimum trades for validation
            'max_missing_data_pct': 5.0,        # Maximum missing data percentage
            'min_data_points': 1000,             # Minimum data points
            
            # Performance thresholds
            'max_reasonable_return': 200.0,      # Maximum reasonable annual return %
            'max_reasonable_sharpe': 5.0,        # Maximum reasonable Sharpe ratio
            'max_reasonable_drawdown': 50.0,     # Maximum reasonable drawdown %
            'min_reasonable_trades_per_year': 10, # Minimum trades per year
            
            # Statistical validation
            'significance_level': 0.05,          # Statistical significance level
            'min_t_statistic': 1.96,            # Minimum t-statistic for significance
            'overfitting_threshold': 0.3,        # Overfitting detection threshold
            
            # Robustness checks
            'outlier_threshold': 3.0,            # Standard deviations for outlier detection
            'consistency_threshold': 0.7,        # Consistency score threshold
            'stability_threshold': 0.5,          # Parameter stability threshold
            
            # Reality checks
            'max_win_rate': 95.0,               # Maximum realistic win rate
            'min_win_rate': 30.0,               # Minimum reasonable win rate
            'max_profit_factor': 10.0,          # Maximum reasonable profit factor
            'min_avg_trade_duration_hours': 1,   # Minimum average trade duration
        }
    
    # =========================================================================
    # MAIN VALIDATION INTERFACE
    # =========================================================================
    
    def validate_results(self, backtest_results: Dict, 
                        data: pd.DataFrame = None,
                        trade_history: List[Dict] = None,
                        strategy_params: Dict = None) -> ValidationResult:
        """
        Perform comprehensive validation of backtesting results
        
        Args:
            backtest_results: Results from backtesting engine
            data: Original price data used for backtesting
            trade_history: Detailed trade history
            strategy_params: Strategy parameters used
        
        Returns:
            ValidationResult: Comprehensive validation results
        """
        print(f"🔍 Validating backtesting results...")
        
        warnings_list = []
        errors_list = []
        validation_scores = []
        
        # Data quality validation
        if data is not None:
            data_validation = self._validate_data_quality(data)
            warnings_list.extend(data_validation.warnings)
            errors_list.extend(data_validation.errors)
            validation_scores.append(data_validation.score)
        
        # Performance validation
        performance_validation = self._validate_performance_metrics(backtest_results)
        warnings_list.extend(performance_validation.warnings)
        errors_list.extend(performance_validation.errors)
        validation_scores.append(performance_validation.score)
        
        # Trade validation
        if trade_history or 'trade_history' in backtest_results:
            trades = trade_history or backtest_results['trade_history']
            trade_validation = self._validate_trades(trades)
            warnings_list.extend(trade_validation.warnings)
            errors_list.extend(trade_validation.errors)
            validation_scores.append(trade_validation.score)
        
        # Statistical validation
        statistical_validation = self._validate_statistical_properties(backtest_results, trades if 'trades' in locals() else [])
        warnings_list.extend(statistical_validation.warnings)
        errors_list.extend(statistical_validation.errors)
        validation_scores.append(statistical_validation.score)
        
        # Robustness validation
        robustness_validation = self._validate_robustness(backtest_results, strategy_params)
        warnings_list.extend(robustness_validation.warnings)
        errors_list.extend(robustness_validation.errors)
        validation_scores.append(robustness_validation.score)
        
        # Reality checks
        reality_validation = self._validate_reality_checks(backtest_results)
        warnings_list.extend(reality_validation.warnings)
        errors_list.extend(reality_validation.errors)
        validation_scores.append(reality_validation.score)
        
        # Calculate overall validation score
        overall_score = np.mean([score for score in validation_scores if score is not None])
        passed = len(errors_list) == 0 and overall_score >= 70.0
        
        # Store detailed results
        self.validation_results = {
            'data_validation': data_validation if data is not None else None,
            'performance_validation': performance_validation,
            'trade_validation': trade_validation if 'trade_validation' in locals() else None,
            'statistical_validation': statistical_validation,
            'robustness_validation': robustness_validation,
            'reality_validation': reality_validation
        }
        
        result = ValidationResult(
            passed=passed,
            warnings=warnings_list,
            errors=errors_list,
            score=overall_score,
            details=self.validation_results
        )
        
        print(f"{'✅' if passed else '❌'} Validation {'PASSED' if passed else 'FAILED'}")
        print(f"📊 Validation score: {overall_score:.1f}/100")
        print(f"⚠️ Warnings: {len(warnings_list)}")
        print(f"❌ Errors: {len(errors_list)}")
        
        return result
    
    # =========================================================================
    # DATA QUALITY VALIDATION
    # =========================================================================
    
    def _validate_data_quality(self, data: pd.DataFrame) -> ValidationResult:
        """Validate data quality and completeness"""
        warnings_list = []
        errors_list = []
        score = 100.0
        
        # Check minimum data points
        if len(data) < self.config['min_data_points']:
            errors_list.append(f"Insufficient data: {len(data)} points (minimum {self.config['min_data_points']})")
            score -= 30
        
        # Check for missing data
        required_columns = ['open', 'high', 'low', 'close', 'datetime']
        missing_data_pct = data[required_columns].isnull().sum().sum() / (len(data) * len(required_columns)) * 100
        
        if missing_data_pct > self.config['max_missing_data_pct']:
            errors_list.append(f"Too much missing data: {missing_data_pct:.1f}% (maximum {self.config['max_missing_data_pct']}%)")
            score -= 25
        elif missing_data_pct > 1.0:
            warnings_list.append(f"Some missing data detected: {missing_data_pct:.1f}%")
            score -= 5
        
        # Check for data anomalies
        ohlc_columns = ['open', 'high', 'low', 'close']
        for col in ohlc_columns:
            if col in data.columns:
                # Check for zero or negative prices
                zero_negative = (data[col] <= 0).sum()
                if zero_negative > 0:
                    errors_list.append(f"Invalid prices in {col}: {zero_negative} zero/negative values")
                    score -= 15
                
                # Check for extreme price movements
                price_changes = data[col].pct_change().abs()
                extreme_moves = (price_changes > 0.1).sum()  # >10% moves
                if extreme_moves > len(data) * 0.01:  # More than 1% of data
                    warnings_list.append(f"Many extreme price movements in {col}: {extreme_moves} cases")
                    score -= 5
        
        # Check High >= Low for all bars
        if 'high' in data.columns and 'low' in data.columns:
            invalid_bars = (data['high'] < data['low']).sum()
            if invalid_bars > 0:
                errors_list.append(f"Invalid OHLC bars: {invalid_bars} bars with high < low")
                score -= 20
        
        # Check for time series continuity
        if 'datetime' in data.columns:
            time_gaps = self._check_time_gaps(data['datetime'])
            if time_gaps['large_gaps'] > 0:
                warnings_list.append(f"Time series gaps detected: {time_gaps['large_gaps']} large gaps")
                score -= 10
        
        # Check indicator data quality
        indicator_columns = ['macd_main', 'bb_upper', 'bb_lower', 'rsi', 'adx']
        for col in indicator_columns:
            if col in data.columns:
                # Check for too many NaN values
                nan_pct = data[col].isnull().sum() / len(data) * 100
                if nan_pct > 20:
                    warnings_list.append(f"High NaN percentage in {col}: {nan_pct:.1f}%")
                    score -= 5
                
                # Check for constant values (indicator not working)
                if data[col].nunique() < 10:
                    warnings_list.append(f"Indicator {col} has very few unique values")
                    score -= 3
        
        return ValidationResult(
            passed=len(errors_list) == 0,
            warnings=warnings_list,
            errors=errors_list,
            score=max(0, score),
            details={'missing_data_pct': missing_data_pct}
        )
    
    def _check_time_gaps(self, datetime_series: pd.Series) -> Dict:
        """Check for gaps in time series"""
        time_diffs = datetime_series.diff().dropna()
        
        # Determine expected frequency
        median_diff = time_diffs.median()
        large_gap_threshold = median_diff * 3
        
        large_gaps = (time_diffs > large_gap_threshold).sum()
        
        return {
            'median_interval': median_diff,
            'large_gaps': large_gaps,
            'max_gap': time_diffs.max()
        }
    
    # =========================================================================
    # PERFORMANCE VALIDATION
    # =========================================================================
    
    def _validate_performance_metrics(self, results: Dict) -> ValidationResult:
        """Validate performance metrics for reasonableness"""
        warnings_list = []
        errors_list = []
        score = 100.0
        
        # Check total return
        total_return = results.get('total_return', 0)
        if abs(total_return) > self.config['max_reasonable_return']:
            errors_list.append(f"Unrealistic total return: {total_return:.1f}% (max reasonable: {self.config['max_reasonable_return']}%)")
            score -= 30
        elif abs(total_return) > self.config['max_reasonable_return'] * 0.7:
            warnings_list.append(f"Very high return: {total_return:.1f}% - verify results")
            score -= 10
        
        # Check Sharpe ratio
        sharpe_ratio = results.get('sharpe_ratio', 0)
        if abs(sharpe_ratio) > self.config['max_reasonable_sharpe']:
            errors_list.append(f"Unrealistic Sharpe ratio: {sharpe_ratio:.2f} (max reasonable: {self.config['max_reasonable_sharpe']})")
            score -= 25
        elif abs(sharpe_ratio) > self.config['max_reasonable_sharpe'] * 0.8:
            warnings_list.append(f"Very high Sharpe ratio: {sharpe_ratio:.2f} - verify results")
            score -= 5
        
        # Check drawdown
        max_drawdown = results.get('max_drawdown', 0)
        if max_drawdown > self.config['max_reasonable_drawdown']:
            errors_list.append(f"Excessive drawdown: {max_drawdown:.1f}% (max reasonable: {self.config['max_reasonable_drawdown']}%)")
            score -= 20
        elif max_drawdown > self.config['max_reasonable_drawdown'] * 0.8:
            warnings_list.append(f"High drawdown: {max_drawdown:.1f}% - high risk strategy")
            score -= 5
        
        # Check win rate
        win_rate = results.get('win_rate', 50)
        if win_rate > self.config['max_win_rate']:
            errors_list.append(f"Unrealistic win rate: {win_rate:.1f}% (max reasonable: {self.config['max_win_rate']}%)")
            score -= 25
        elif win_rate < self.config['min_win_rate']:
            warnings_list.append(f"Low win rate: {win_rate:.1f}% - high risk strategy")
            score -= 10
        
        # Check profit factor
        profit_factor = results.get('profit_factor', 1)
        if profit_factor > self.config['max_profit_factor']:
            errors_list.append(f"Unrealistic profit factor: {profit_factor:.2f} (max reasonable: {self.config['max_profit_factor']})")
            score -= 20
        elif profit_factor < 1.0:
            errors_list.append(f"Strategy loses money: profit factor {profit_factor:.2f}")
            score -= 30
        
        # Check trade frequency
        total_trades = results.get('total_trades', 0)
        if total_trades < self.config['min_trades']:
            errors_list.append(f"Insufficient trades for validation: {total_trades} (minimum {self.config['min_trades']})")
            score -= 40
        
        # Estimate trades per year and validate
        backtest_duration = results.get('backtest_duration', 0)
        if backtest_duration > 0:
            years = backtest_duration / (365.25 * 24 * 3600)  # Convert seconds to years
            trades_per_year = total_trades / years if years > 0 else 0
            
            if trades_per_year < self.config['min_reasonable_trades_per_year']:
                warnings_list.append(f"Low trading frequency: {trades_per_year:.1f} trades/year")
                score -= 5
            elif trades_per_year > 1000:
                warnings_list.append(f"Very high trading frequency: {trades_per_year:.1f} trades/year - verify transaction costs")
                score -= 10
        
        return ValidationResult(
            passed=len(errors_list) == 0,
            warnings=warnings_list,
            errors=errors_list,
            score=max(0, score),
            details={
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'profit_factor': profit_factor
            }
        )
    
    # =========================================================================
    # TRADE VALIDATION
    # =========================================================================
    
    def _validate_trades(self, trades: List[Dict]) -> ValidationResult:
        """Validate individual trades for anomalies"""
        warnings_list = []
        errors_list = []
        score = 100.0
        
        if not trades:
            errors_list.append("No trades to validate")
            return ValidationResult(False, warnings_list, errors_list, 0, {})
        
        trade_df = pd.DataFrame(trades)
        
        # Check for outlier trades
        returns = trade_df['realized_pnl'].values
        outlier_threshold = self.config['outlier_threshold']
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        outliers = np.abs(returns - mean_return) > outlier_threshold * std_return
        outlier_count = outliers.sum()
        
        if outlier_count > len(trades) * 0.05:  # More than 5% outliers
            warnings_list.append(f"Many outlier trades detected: {outlier_count} outliers ({outlier_count/len(trades)*100:.1f}%)")
            score -= 15
        
        # Check for impossible trade durations
        if 'entry_time' in trade_df.columns and 'close_time' in trade_df.columns:
            trade_df['entry_time'] = pd.to_datetime(trade_df['entry_time'])
            trade_df['close_time'] = pd.to_datetime(trade_df['close_time'])
            trade_df['duration'] = trade_df['close_time'] - trade_df['entry_time']
            
            # Check for negative durations
            negative_durations = (trade_df['duration'] < timedelta(0)).sum()
            if negative_durations > 0:
                errors_list.append(f"Invalid trade durations: {negative_durations} trades with negative duration")
                score -= 25
            
            # Check for extremely short durations
            min_duration = timedelta(hours=self.config['min_avg_trade_duration_hours'])
            very_short_trades = (trade_df['duration'] < min_duration).sum()
            if very_short_trades > len(trades) * 0.1:  # More than 10% very short
                warnings_list.append(f"Many very short trades: {very_short_trades} trades < {min_duration}")
                score -= 10
            
            # Check for extremely long durations (more than 30 days)
            very_long_trades = (trade_df['duration'] > timedelta(days=30)).sum()
            if very_long_trades > len(trades) * 0.1:
                warnings_list.append(f"Many very long trades: {very_long_trades} trades > 30 days")
                score -= 5
        
        # Check for impossible profit amounts
        if 'size' in trade_df.columns and 'entry_price' in trade_df.columns:
            # Check if any single trade made more than 10% of account
            max_reasonable_profit = 1000 * 0.1  # 10% of $1000 account
            large_profits = (trade_df['realized_pnl'] > max_reasonable_profit).sum()
            
            if large_profits > 0:
                warnings_list.append(f"Very large profits: {large_profits} trades > {max_reasonable_profit}")
                score -= 5
        
        # Check for consistent profit patterns (possible curve fitting)
        consecutive_wins = self._count_consecutive_outcomes(returns, positive=True)
        consecutive_losses = self._count_consecutive_outcomes(returns, positive=False)
        
        if consecutive_wins.get('max_consecutive', 0) > 20:
            warnings_list.append(f"Unusually long winning streak: {consecutive_wins['max_consecutive']} consecutive wins")
            score -= 10
        
        if consecutive_losses.get('max_consecutive', 0) > 15:
            warnings_list.append(f"Long losing streak: {consecutive_losses['max_consecutive']} consecutive losses")
            score -= 5
        
        return ValidationResult(
            passed=len(errors_list) == 0,
            warnings=warnings_list,
            errors=errors_list,
            score=max(0, score),
            details={
                'outlier_count': outlier_count,
                'consecutive_analysis': {
                    'wins': consecutive_wins,
                    'losses': consecutive_losses
                }
            }
        )
    
    def _count_consecutive_outcomes(self, returns: np.ndarray, positive: bool = True) -> Dict:
        """Count consecutive wins or losses"""
        outcomes = returns > 0 if positive else returns < 0
        
        consecutive_counts = []
        current_count = 0
        
        for outcome in outcomes:
            if outcome:
                current_count += 1
            else:
                if current_count > 0:
                    consecutive_counts.append(current_count)
                current_count = 0
        
        # Add final count if ended on a streak
        if current_count > 0:
            consecutive_counts.append(current_count)
        
        return {
            'max_consecutive': max(consecutive_counts) if consecutive_counts else 0,
            'avg_consecutive': np.mean(consecutive_counts) if consecutive_counts else 0,
            'total_streaks': len(consecutive_counts)
        }
    
    # =========================================================================
    # STATISTICAL VALIDATION
    # =========================================================================
    
    def _validate_statistical_properties(self, results: Dict, trades: List[Dict]) -> ValidationResult:
        """Validate statistical properties and significance"""
        warnings_list = []
        errors_list = []
        score = 100.0
        
        if not trades or len(trades) < 30:
            warnings_list.append("Insufficient trades for statistical validation")
            return ValidationResult(True, warnings_list, errors_list, 70, {})
        
        trade_df = pd.DataFrame(trades)
        returns = trade_df['realized_pnl'].values
        
        # Test statistical significance of returns
        t_stat, p_value = stats.ttest_1samp(returns, 0)
        
        if p_value > self.config['significance_level']:
            warnings_list.append(f"Returns not statistically significant: p-value = {p_value:.4f}")
            score -= 20
        
        if abs(t_stat) < self.config['min_t_statistic']:
            warnings_list.append(f"Low t-statistic: {t_stat:.2f} (minimum {self.config['min_t_statistic']})")
            score -= 15
        
        # Test for normality of returns
        if len(returns) >= 8:
            try:
                shapiro_stat, shapiro_p = stats.shapiro(returns)
                if shapiro_p < 0.01:  # Very non-normal
                    warnings_list.append(f"Returns highly non-normal: Shapiro p-value = {shapiro_p:.4f}")
                    score -= 5
            except:
                pass
        
        # Test for autocorrelation (should be minimal for good strategy)
        if len(returns) > 10:
            autocorr = np.corrcoef(returns[:-1], returns[1:])[0, 1]
            if abs(autocorr) > 0.3:
                warnings_list.append(f"High autocorrelation in returns: {autocorr:.3f}")
                score -= 10
        
        # Check for too perfect results (potential overfitting indicators)
        perfect_indicators = 0
        
        # Perfect win rate
        win_rate = results.get('win_rate', 50)
        if win_rate > 90:
            perfect_indicators += 1
            warnings_list.append(f"Very high win rate may indicate overfitting: {win_rate:.1f}%")
        
        # Perfect Sharpe ratio
        sharpe_ratio = results.get('sharpe_ratio', 0)
        if sharpe_ratio > 3:
            perfect_indicators += 1
            warnings_list.append(f"Very high Sharpe ratio may indicate overfitting: {sharpe_ratio:.2f}")
        
        # Too few losing trades
        losing_trades = len([r for r in returns if r < 0])
        if losing_trades < len(returns) * 0.1:  # Less than 10% losing
            perfect_indicators += 1
            warnings_list.append(f"Very few losing trades may indicate overfitting: {losing_trades}/{len(returns)}")
        
        if perfect_indicators >= 2:
            errors_list.append("Multiple overfitting indicators detected")
            score -= 30
        
        return ValidationResult(
            passed=len(errors_list) == 0,
            warnings=warnings_list,
            errors=errors_list,
            score=max(0, score),
            details={
                't_statistic': t_stat,
                'p_value': p_value,
                'autocorrelation': autocorr if 'autocorr' in locals() else None,
                'perfect_indicators': perfect_indicators
            }
        )
    
    # =========================================================================
    # ROBUSTNESS VALIDATION
    # =========================================================================
    
    def _validate_robustness(self, results: Dict, strategy_params: Dict) -> ValidationResult:
        """Validate strategy robustness and parameter sensitivity"""
        warnings_list = []
        errors_list = []
        score = 100.0
        
        # Check for parameter extremes (indication of overfitting)
        if strategy_params:
            extreme_params = self._check_parameter_extremes(strategy_params)
            if extreme_params:
                warnings_list.append(f"Parameters at extremes may indicate overfitting: {extreme_params}")
                score -= 15
        
        # Check consistency across different periods (if available)
        # This would require period-by-period results, which we don't have here
        # In a real implementation, you'd check monthly/quarterly consistency
        
        # Check for strategy over-optimization indicators
        total_trades = results.get('total_trades', 0)
        win_rate = results.get('win_rate', 50)
        
        # Too few trades with high win rate indicates potential overfitting
        if total_trades < 50 and win_rate > 80:
            warnings_list.append("High win rate with few trades may indicate overfitting")
            score -= 20
        
        # Check profit factor vs. win rate relationship
        profit_factor = results.get('profit_factor', 1)
        if profit_factor > 3 and win_rate > 70:
            warnings_list.append("Very high profit factor and win rate combination is suspicious")
            score -= 15
        
        return ValidationResult(
            passed=len(errors_list) == 0,
            warnings=warnings_list,
            errors=errors_list,
            score=max(0, score),
            details={'extreme_params': extreme_params if strategy_params else None}
        )
    
    def _check_parameter_extremes(self, params: Dict) -> List[str]:
        """Check if parameters are at extreme values (boundary optimization)"""
        extreme_params = []
        
        # Define parameter ranges (these should match optimization ranges)
        param_ranges = {
            'macd_fast': (8, 18),
            'macd_slow': (20, 35),
            'bb_period': (15, 30),
            'max_risk_percent': (0.5, 2.0),
            'take_profit_ratio': (1.5, 3.0),
            'adx_trend': (20.0, 35.0),
        }
        
        for param_name, (min_val, max_val) in param_ranges.items():
            if param_name in params:
                value = params[param_name]
                range_size = max_val - min_val
                tolerance = range_size * 0.05  # 5% tolerance from boundaries
                
                if value <= min_val + tolerance or value >= max_val - tolerance:
                    extreme_params.append(f"{param_name}={value}")
        
        return extreme_params
    
    # =========================================================================
    # REALITY CHECKS
    # =========================================================================
    
    def _validate_reality_checks(self, results: Dict) -> ValidationResult:
        """Perform reality checks on results"""
        warnings_list = []
        errors_list = []
        score = 100.0
        
        # Check if results are too good to be true
        total_return = results.get('total_return', 0)
        max_drawdown = results.get('max_drawdown', 100)
        sharpe_ratio = results.get('sharpe_ratio', 0)
        win_rate = results.get('win_rate', 50)
        
        # Reality check: High return with low drawdown is suspicious
        if total_return > 50 and max_drawdown < 5:
            errors_list.append(f"Unrealistic combination: {total_return:.1f}% return with only {max_drawdown:.1f}% drawdown")
            score -= 40
        
        # Reality check: Perfect or near-perfect performance
        perfect_score = 0
        if win_rate > 95:
            perfect_score += 1
        if sharpe_ratio > 4:
            perfect_score += 1
        if max_drawdown < 3:
            perfect_score += 1
        if total_return > 100 and max_drawdown < 10:
            perfect_score += 1
        
        if perfect_score >= 3:
            errors_list.append("Results too perfect - likely overfitting or data snooping")
            score -= 50
        elif perfect_score >= 2:
            warnings_list.append("Results suspiciously good - verify methodology")
            score -= 25
        
        # Check transaction cost considerations
        total_trades = results.get('total_trades', 0)
        if total_trades > 500:
            warnings_list.append(f"High trade frequency ({total_trades} trades) - ensure transaction costs are included")
            score -= 5
        
        # Check market regime dependency
        # In real implementation, you'd check if strategy only works in specific market conditions
        
        return ValidationResult(
            passed=len(errors_list) == 0,
            warnings=warnings_list,
            errors=errors_list,
            score=max(0, score),
            details={
                'perfect_score': perfect_score,
                'reality_flags': len(errors_list) + len(warnings_list)
            }
        )
    
    # =========================================================================
    # UTILITY METHODS
    # =========================================================================
    
    def get_validation_report(self) -> str:
        """Generate comprehensive validation report"""
        if not self.validation_results:
            return "No validation results available"
        
        report = []
        report.append("BACKTESTING VALIDATION REPORT")
        report.append("=" * 40)
        report.append("")
        
        for validation_name, validation_result in self.validation_results.items():
            if validation_result:
                report.append(f"{validation_name.upper().replace('_', ' ')}")
                report.append("-" * len(validation_name))
                report.append(f"Status: {'PASSED' if validation_result.passed else 'FAILED'}")
                report.append(f"Score: {validation_result.score:.1f}/100")
                
                if validation_result.errors:
                    report.append("Errors:")
                    for error in validation_result.errors:
                        report.append(f"  - {error}")
                
                if validation_result.warnings:
                    report.append("Warnings:")
                    for warning in validation_result.warnings:
                        report.append(f"  - {warning}")
                
                report.append("")
        
        return "\n".join(report)


def main():
    """Example usage of ResultsValidator"""
    print("✅ Results Validator Example")
    print("============================")
    
    # Create validator
    validator = ResultsValidator()
    
    # Example results
    example_results = {
        'total_return': 25.0,
        'max_drawdown': 8.5,
        'sharpe_ratio': 1.8,
        'win_rate': 65.0,
        'profit_factor': 2.1,
        'total_trades': 45
    }
    
    print("✅ This is an example of the ResultsValidator")
    print("🔄 To use in practice:")
    print("   1. Pass backtest results to validate_results()")
    print("   2. Include trade history and original data for full validation")
    print("   3. Review validation warnings and errors")
    print("   4. Generate validation report")
    
    print(f"\n🔍 Validation checks include:")
    print(f"   - Data quality validation")
    print(f"   - Performance reality checks")
    print(f"   - Statistical significance testing")
    print(f"   - Overfitting detection")
    print(f"   - Trade anomaly detection")
    print(f"   - Parameter robustness validation")


if __name__ == "__main__":
    main()