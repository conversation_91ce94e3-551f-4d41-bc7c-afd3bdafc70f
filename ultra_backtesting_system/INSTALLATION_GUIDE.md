# Installation Guide - Ultra Backtesting System

This guide provides step-by-step instructions for setting up the Ultra Backtesting System on your machine.

## 📋 Prerequisites

### System Requirements
- **Operating System**: macOS 10.14+, Windows 10+, or Linux Ubuntu 18.04+
- **Python**: Version 3.8 or higher
- **Memory**: 4GB RAM minimum (8GB recommended for optimization)
- **Storage**: 2GB free space for system and data files
- **MetaTrader 5**: Required only for initial data export

### Python Environment
We recommend using a virtual environment to avoid conflicts with other Python packages.

## 🚀 Installation Steps

### Step 1: Python Environment Setup

#### Option A: Using Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv ultra_backtest_env

# Activate virtual environment
# On macOS/Linux:
source ultra_backtest_env/bin/activate
# On Windows:
ultra_backtest_env\Scripts\activate

# Verify Python version
python --version  # Should be 3.8+
```

#### Option B: Using Conda
```bash
# Create conda environment
conda create -n ultra_backtest python=3.9
conda activate ultra_backtest
```

### Step 2: Download the System

#### Option A: Download ZIP
1. Download the system files to your preferred directory
2. Extract the ZIP file
3. Navigate to the extracted directory

#### Option B: Git Clone (if available)
```bash
git clone <repository-url>
cd ultra_backtesting_system
```

### Step 3: Install Dependencies

```bash
# Navigate to the system directory
cd ultra_backtesting_system

# Install required packages
pip install -r requirements.txt

# Verify installation
pip list
```

### Step 4: Verify Installation

Run the example script to verify everything is working:

```bash
python example_usage.py
```

You should see output showing all system components being initialized without errors.

## 📦 Dependencies Explained

### Core Dependencies
- **pandas**: Data manipulation and analysis
- **numpy**: Numerical computing
- **scipy**: Scientific computing and statistics
- **optuna**: Bayesian optimization framework
- **sqlite3**: Database operations (built-in)

### Optional Dependencies
- **matplotlib**: Plotting and visualization (for advanced analysis)
- **seaborn**: Statistical visualization
- **jupyter**: Notebook interface for analysis

## 🔧 Configuration

### Create Configuration Directory
```bash
mkdir -p config
mkdir -p data
mkdir -p results
mkdir -p reports
mkdir -p optimization_results
mkdir -p mt5_parameters
```

### Environment Variables (Optional)
Create a `.env` file in the system directory:

```bash
# Data paths
ULTRA_DATA_PATH=./data
ULTRA_RESULTS_PATH=./results
ULTRA_REPORTS_PATH=./reports

# Optimization settings
ULTRA_DEFAULT_TRIALS=200
ULTRA_DEFAULT_TIMEOUT=14400  # 4 hours in seconds

# MT5 settings (for data export)
MT5_DATA_PATH=/path/to/mt5/data
MT5_TERMINAL_PATH=/path/to/mt5/terminal.exe
```

## 🏗️ Directory Structure Setup

After installation, your directory should look like this:

```
ultra_backtesting_system/
├── README.md
├── INSTALLATION_GUIDE.md
├── requirements.txt
├── example_usage.py
├── __init__.py
├── data/
│   ├── __init__.py
│   ├── mt5_data_exporter.py
│   └── data_loader.py
├── indicators/
│   ├── __init__.py
│   └── technical_indicators.py
├── strategy/
│   ├── __init__.py
│   └── ultra_strategy.py
├── backtesting/
│   ├── __init__.py
│   └── backtest_engine.py
├── optimization/
│   ├── __init__.py
│   ├── optimizer.py
│   └── walk_forward.py
├── reporting/
│   ├── __init__.py
│   ├── performance_analyzer.py
│   ├── validation.py
│   └── report_generator.py
├── mt5_export/
│   ├── __init__.py
│   ├── parameter_exporter.py
│   └── set_file_generator.py
└── config/         # Configuration files
└── data/           # Historical data files
└── results/        # Backtest results
└── reports/        # Generated reports
└── optimization_results/  # Optimization outputs
└── mt5_parameters/ # MT5 parameter files
```

## 🧪 Testing Installation

### Basic Functionality Test

Create a test script `test_installation.py`:

```python
#!/usr/bin/env python3
"""Test Ultra Backtesting System Installation"""

def test_imports():
    """Test that all modules can be imported"""
    try:
        from ultra_backtesting_system import (
            DataLoader, TechnicalIndicators, UltraStrategy,
            BacktestEngine, ParameterOptimizer, WalkForwardAnalyzer,
            PerformanceAnalyzer, ResultsValidator, ReportGenerator,
            ParameterExporter, SetFileGenerator
        )
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_components():
    """Test basic component initialization"""
    try:
        from ultra_backtesting_system import UltraStrategy, BacktestEngine
        
        # Test strategy initialization
        strategy = UltraStrategy()
        print(f"✅ Strategy initialized with {len(strategy.config)} parameters")
        
        # Test backtesting engine
        engine = BacktestEngine(initial_balance=1000.0)
        print(f"✅ Backtesting engine initialized with ${engine.balance:,.2f} balance")
        
        return True
    except Exception as e:
        print(f"❌ Component test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Ultra Backtesting System Installation")
    print("=" * 50)
    
    import_test = test_imports()
    component_test = test_components()
    
    if import_test and component_test:
        print("\n🎉 Installation test PASSED!")
        print("✅ System is ready for use")
    else:
        print("\n❌ Installation test FAILED!")
        print("Please check the installation steps")
```

Run the test:
```bash
python test_installation.py
```

## 🔧 Troubleshooting

### Common Installation Issues

#### 1. Python Version Issues
**Problem**: "Python 3.8+ required"
**Solution**:
```bash
# Check Python version
python --version
python3 --version

# If using wrong version, create virtual env with correct version
python3.9 -m venv ultra_backtest_env
```

#### 2. Package Installation Errors
**Problem**: "Could not install packages"
**Solution**:
```bash
# Upgrade pip
pip install --upgrade pip

# Try installing packages one by one
pip install pandas
pip install numpy
pip install scipy
pip install optuna

# If still failing, try with --user flag
pip install --user -r requirements.txt
```

#### 3. Permission Errors (macOS/Linux)
**Problem**: "Permission denied"
**Solution**:
```bash
# Use --user flag
pip install --user -r requirements.txt

# Or fix permissions
sudo chown -R $USER:$USER /path/to/ultra_backtesting_system
```

#### 4. Windows Path Issues
**Problem**: "Module not found"
**Solution**:
```bash
# Use forward slashes in paths
# Add system to Python path
set PYTHONPATH=%PYTHONPATH%;C:\path\to\ultra_backtesting_system
```

#### 5. Memory Issues During Testing
**Problem**: "MemoryError during optimization"
**Solution**:
- Reduce number of optimization trials
- Use smaller datasets for testing
- Close other applications to free memory

### Performance Optimization

#### 1. Speed Up Installation
```bash
# Use pip cache
pip install --cache-dir ~/.pip/cache -r requirements.txt

# Parallel installation
pip install --upgrade pip setuptools wheel
pip install -r requirements.txt --use-feature=2020-resolver
```

#### 2. Optimize for Development
```bash
# Install in development mode
pip install -e .

# Add to Python path permanently
echo 'export PYTHONPATH="${PYTHONPATH}:/path/to/ultra_backtesting_system"' >> ~/.bashrc
source ~/.bashrc
```

## 📊 Verification Checklist

After installation, verify the following:

- [ ] Python 3.8+ is installed and accessible
- [ ] Virtual environment is activated (if using)
- [ ] All required packages are installed (`pip list`)
- [ ] System modules import without errors
- [ ] Example script runs successfully
- [ ] Directory structure is correct
- [ ] Configuration directories exist
- [ ] Test script passes all checks

## 🔄 Updating the System

To update to a newer version:

1. **Backup your data and configuration:**
   ```bash
   cp -r data/ data_backup/
   cp -r config/ config_backup/
   ```

2. **Download new version:**
   ```bash
   # If using git
   git pull origin main
   
   # If using ZIP, extract to new directory
   ```

3. **Update dependencies:**
   ```bash
   pip install -r requirements.txt --upgrade
   ```

4. **Test the update:**
   ```bash
   python test_installation.py
   python example_usage.py
   ```

## 🆘 Getting Help

If you encounter issues during installation:

1. **Check the troubleshooting section above**
2. **Verify system requirements**
3. **Try the common solutions**
4. **Create a minimal test case**
5. **Report the issue with:**
   - Operating system and version
   - Python version
   - Error messages
   - Steps to reproduce

## 🎯 Next Steps

After successful installation:

1. **Read the README.md** for system overview
2. **Run example_usage.py** to see the complete workflow
3. **Set up MT5 data export** for your trading data
4. **Configure the system** for your specific needs
5. **Start with a simple backtest** to familiarize yourself

---

**Installation complete! 🎉**

Your Ultra Backtesting System is now ready for professional trading strategy development and optimization.