#!/usr/bin/env python3
"""
MT5 Data Exporter
================
One-time script to export historical data from MetaTrader 5.
After running this once, the system becomes MetaTrader-free.

Usage:
    python mt5_data_exporter.py

Output:
    - CSV files with historical data
    - SQLite database for fast access
    - Data quality validation report
"""

import MetaTrader5 as mt5
import pandas as pd
import sqlite3
import os
import json
from datetime import datetime, timedelta
from pathlib import Path

class MT5DataExporter:
    def __init__(self, mt5_path=None):
        """
        Initialize MT5 Data Exporter
        
        Args:
            mt5_path: Path to MT5 terminal (auto-detect if None)
        """
        self.mt5_path = mt5_path
        self.data_folder = Path(__file__).parent.parent / "historical_data"
        self.data_folder.mkdir(exist_ok=True)
        
        # Create subfolders
        (self.data_folder / "raw").mkdir(exist_ok=True)
        (self.data_folder / "processed").mkdir(exist_ok=True)
        (self.data_folder / "database").mkdir(exist_ok=True)
    
    def initialize_mt5(self):
        """Initialize connection to MT5 terminal"""
        print("🔗 Initializing MetaTrader 5 connection...")
        
        if self.mt5_path:
            success = mt5.initialize(path=self.mt5_path)
        else:
            success = mt5.initialize()
        
        if not success:
            print(f"❌ MT5 initialization failed: {mt5.last_error()}")
            return False
        
        print("✅ MT5 connection established")
        
        # Display account info
        account_info = mt5.account_info()
        if account_info:
            print(f"📊 Account: {account_info.login}, Server: {account_info.server}")
        
        return True
    
    def export_symbol_data(self, symbol="EURUSD", timeframe=mt5.TIMEFRAME_M15, 
                          years_back=2, output_format="both"):
        """
        Export historical data for a symbol
        
        Args:
            symbol: Trading symbol (e.g., "EURUSD")
            timeframe: MT5 timeframe constant
            years_back: How many years of data to export
            output_format: "csv", "sqlite", or "both"
        
        Returns:
            dict: Export results with file paths and statistics
        """
        print(f"📈 Exporting {symbol} data...")
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=years_back * 365)
        
        print(f"📅 Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        # Get historical data from MT5
        print("📊 Fetching data from MT5...")
        rates = mt5.copy_rates_range(symbol, timeframe, start_date, end_date)
        
        if rates is None or len(rates) == 0:
            print(f"❌ No data received for {symbol}")
            return None
        
        print(f"✅ Retrieved {len(rates)} bars")
        
        # Convert to DataFrame
        df = pd.DataFrame(rates)
        
        # Convert time column to readable format
        df['time'] = pd.to_datetime(df['time'], unit='s')
        
        # Add calculated columns
        df['datetime'] = df['time']
        df['symbol'] = symbol
        df['timeframe'] = self._timeframe_to_string(timeframe)
        
        # Reorder columns for clarity
        columns_order = ['datetime', 'symbol', 'timeframe', 'open', 'high', 
                        'low', 'close', 'tick_volume', 'spread', 'real_volume']
        df = df[columns_order]
        
        # Generate output filenames
        timeframe_str = self._timeframe_to_string(timeframe)
        csv_filename = f"{symbol.lower()}_{timeframe_str}_{start_date.year}_{end_date.year}.csv"
        csv_path = self.data_folder / "raw" / csv_filename
        
        results = {
            'symbol': symbol,
            'timeframe': timeframe_str,
            'bars_count': len(df),
            'start_date': start_date,
            'end_date': end_date,
            'files_created': []
        }
        
        # Export to CSV
        if output_format in ["csv", "both"]:
            print(f"💾 Saving to CSV: {csv_path}")
            df.to_csv(csv_path, index=False)
            results['csv_file'] = str(csv_path)
            results['files_created'].append(str(csv_path))
        
        # Export to SQLite
        if output_format in ["sqlite", "both"]:
            db_path = self.data_folder / "database" / "historical_data.db"
            print(f"💾 Saving to SQLite: {db_path}")
            
            with sqlite3.connect(db_path) as conn:
                table_name = f"{symbol.lower()}_{timeframe_str}"
                df.to_sql(table_name, conn, if_exists='replace', index=False)
                
                # Create index for faster queries
                conn.execute(f"CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime ON {table_name}(datetime)")
            
            results['sqlite_file'] = str(db_path)
            results['sqlite_table'] = table_name
            if str(db_path) not in results['files_created']:
                results['files_created'].append(str(db_path))
        
        print(f"✅ Export completed: {len(df)} bars saved")
        return results
    
    def validate_data_quality(self, df, symbol):
        """
        Validate exported data quality
        
        Args:
            df: DataFrame with historical data
            symbol: Trading symbol
        
        Returns:
            dict: Validation report
        """
        print(f"🔍 Validating data quality for {symbol}...")
        
        validation_report = {
            'symbol': symbol,
            'total_bars': len(df),
            'date_range': {
                'start': df['datetime'].min().isoformat(),
                'end': df['datetime'].max().isoformat()
            },
            'issues': [],
            'statistics': {}
        }
        
        # Check for missing data (gaps)
        df_sorted = df.sort_values('datetime')
        time_diffs = df_sorted['datetime'].diff().dropna()
        expected_interval = pd.Timedelta(minutes=15)  # For M15 data
        
        large_gaps = time_diffs[time_diffs > expected_interval * 2]
        if len(large_gaps) > 0:
            validation_report['issues'].append({
                'type': 'time_gaps',
                'count': len(large_gaps),
                'description': f'Found {len(large_gaps)} time gaps larger than expected'
            })
        
        # Check for invalid OHLC relationships
        invalid_ohlc = df[(df['high'] < df['open']) | 
                         (df['high'] < df['close']) | 
                         (df['low'] > df['open']) | 
                         (df['low'] > df['close']) |
                         (df['high'] < df['low'])]
        
        if len(invalid_ohlc) > 0:
            validation_report['issues'].append({
                'type': 'invalid_ohlc',
                'count': len(invalid_ohlc),
                'description': f'Found {len(invalid_ohlc)} bars with invalid OHLC relationships'
            })
        
        # Check for zero/negative prices
        zero_prices = df[(df['open'] <= 0) | (df['high'] <= 0) | 
                        (df['low'] <= 0) | (df['close'] <= 0)]
        
        if len(zero_prices) > 0:
            validation_report['issues'].append({
                'type': 'zero_prices',
                'count': len(zero_prices),
                'description': f'Found {len(zero_prices)} bars with zero or negative prices'
            })
        
        # Calculate statistics
        validation_report['statistics'] = {
            'price_range': {
                'min': float(df[['open', 'high', 'low', 'close']].min().min()),
                'max': float(df[['open', 'high', 'low', 'close']].max().max())
            },
            'volume_stats': {
                'min_tick_volume': int(df['tick_volume'].min()),
                'max_tick_volume': int(df['tick_volume'].max()),
                'avg_tick_volume': float(df['tick_volume'].mean())
            },
            'spread_stats': {
                'min_spread': int(df['spread'].min()),
                'max_spread': int(df['spread'].max()),
                'avg_spread': float(df['spread'].mean())
            }
        }
        
        # Overall quality score
        issues_count = sum(issue['count'] for issue in validation_report['issues'])
        quality_score = max(0, 100 - (issues_count / len(df) * 100))
        validation_report['quality_score'] = round(quality_score, 2)
        
        if quality_score >= 95:
            validation_report['quality_status'] = 'EXCELLENT'
        elif quality_score >= 85:
            validation_report['quality_status'] = 'GOOD'
        elif quality_score >= 70:
            validation_report['quality_status'] = 'ACCEPTABLE'
        else:
            validation_report['quality_status'] = 'POOR'
        
        print(f"📊 Data quality: {validation_report['quality_status']} ({quality_score}%)")
        
        return validation_report
    
    def export_multiple_symbols(self, symbols=None, timeframes=None):
        """
        Export data for multiple symbols and timeframes
        
        Args:
            symbols: List of symbols (default: major forex pairs)
            timeframes: List of MT5 timeframe constants
        
        Returns:
            dict: Complete export results
        """
        if symbols is None:
            symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD"]
        
        if timeframes is None:
            timeframes = [mt5.TIMEFRAME_M15, mt5.TIMEFRAME_H1]
        
        print(f"🚀 Starting bulk export: {len(symbols)} symbols × {len(timeframes)} timeframes")
        
        all_results = {
            'export_timestamp': datetime.now().isoformat(),
            'symbols_exported': [],
            'validation_reports': [],
            'summary': {
                'total_symbols': len(symbols),
                'total_timeframes': len(timeframes),
                'successful_exports': 0,
                'failed_exports': 0
            }
        }
        
        for symbol in symbols:
            for timeframe in timeframes:
                try:
                    print(f"\n📈 Processing {symbol} {self._timeframe_to_string(timeframe)}...")
                    
                    # Export data
                    export_result = self.export_symbol_data(symbol, timeframe)
                    
                    if export_result:
                        all_results['symbols_exported'].append(export_result)
                        all_results['summary']['successful_exports'] += 1
                        
                        # Load data for validation
                        csv_file = export_result.get('csv_file')
                        if csv_file and os.path.exists(csv_file):
                            df = pd.read_csv(csv_file)
                            df['datetime'] = pd.to_datetime(df['datetime'])
                            
                            # Validate data quality
                            validation = self.validate_data_quality(df, symbol)
                            all_results['validation_reports'].append(validation)
                        
                    else:
                        all_results['summary']['failed_exports'] += 1
                        print(f"❌ Failed to export {symbol} {self._timeframe_to_string(timeframe)}")
                
                except Exception as e:
                    print(f"❌ Error exporting {symbol} {self._timeframe_to_string(timeframe)}: {e}")
                    all_results['summary']['failed_exports'] += 1
        
        # Save complete report
        report_path = self.data_folder / "export_report.json"
        with open(report_path, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        print(f"\n✅ Bulk export completed!")
        print(f"📊 Success: {all_results['summary']['successful_exports']}")
        print(f"❌ Failed: {all_results['summary']['failed_exports']}")
        print(f"📄 Report saved: {report_path}")
        
        return all_results
    
    def _timeframe_to_string(self, timeframe):
        """Convert MT5 timeframe constant to string"""
        timeframe_map = {
            mt5.TIMEFRAME_M1: "m1",
            mt5.TIMEFRAME_M5: "m5",
            mt5.TIMEFRAME_M15: "m15",
            mt5.TIMEFRAME_M30: "m30",
            mt5.TIMEFRAME_H1: "h1",
            mt5.TIMEFRAME_H4: "h4",
            mt5.TIMEFRAME_D1: "d1"
        }
        return timeframe_map.get(timeframe, f"tf_{timeframe}")
    
    def shutdown(self):
        """Shutdown MT5 connection"""
        mt5.shutdown()
        print("🔌 MT5 connection closed")


def main():
    """Main function to run data export"""
    print("🚀 MT5 Data Exporter")
    print("===================")
    
    # Initialize exporter
    exporter = MT5DataExporter()
    
    try:
        # Connect to MT5
        if not exporter.initialize_mt5():
            return
        
        # Export primary data (EURUSD M15)
        print("\n📊 Exporting primary data (EURUSD M15)...")
        primary_result = exporter.export_symbol_data(
            symbol="EURUSD",
            timeframe=mt5.TIMEFRAME_M15,
            years_back=2,
            output_format="both"
        )
        
        if primary_result:
            print(f"✅ Primary export successful: {primary_result['bars_count']} bars")
            
            # Load and validate primary data
            csv_file = primary_result['csv_file']
            df = pd.read_csv(csv_file)
            df['datetime'] = pd.to_datetime(df['datetime'])
            
            validation = exporter.validate_data_quality(df, "EURUSD")
            
            # Save validation report
            validation_path = exporter.data_folder / "processed" / "data_quality_report.json"
            with open(validation_path, 'w') as f:
                json.dump(validation, f, indent=2, default=str)
            
            print(f"📊 Validation report saved: {validation_path}")
        
        # Optional: Export additional symbols
        export_additional = input("\n🤔 Export additional symbols? (y/n): ").strip().lower()
        if export_additional in ['y', 'yes']:
            print("\n📈 Exporting additional symbols...")
            bulk_results = exporter.export_multiple_symbols()
            print("✅ Bulk export completed!")
        
        print("\n🎉 Data export completed successfully!")
        print("\n📁 Files created:")
        print(f"   📊 Raw data: {exporter.data_folder / 'raw'}")
        print(f"   🗄️ Database: {exporter.data_folder / 'database'}")
        print(f"   📋 Reports: {exporter.data_folder}")
        
        print("\n🔥 System is now MetaTrader-free!")
        print("   You can now run backtesting and optimization without MT5.")
    
    except Exception as e:
        print(f"❌ Error during export: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        exporter.shutdown()


if __name__ == "__main__":
    main()