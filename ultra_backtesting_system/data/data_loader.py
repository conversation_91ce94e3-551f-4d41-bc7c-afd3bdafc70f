#!/usr/bin/env python3
"""
Data Loader
===========
Pure Python data loading system for backtesting.
Works with exported MT5 data - no MetaTrader dependencies.

Features:
- Load CSV and SQLite data
- Data validation and cleaning
- Multiple timeframe support
- Fast data access with caching
"""

import pandas as pd
import sqlite3
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import json
from typing import Optional, Dict, List, Tuple
import warnings

class DataLoader:
    def __init__(self, data_folder=None):
        """
        Initialize Data Loader
        
        Args:
            data_folder: Path to historical data folder
        """
        if data_folder is None:
            self.data_folder = Path(__file__).parent.parent / "historical_data"
        else:
            self.data_folder = Path(data_folder)
        
        self.cache = {}  # In-memory cache for loaded data
        self.db_path = self.data_folder / "database" / "historical_data.db"
    
    def load_csv_data(self, symbol="EURUSD", timeframe="m15", 
                      start_date=None, end_date=None):
        """
        Load data from CSV file
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe (m1, m5, m15, m30, h1, h4, d1)
            start_date: Start date (YYYY-MM-DD or datetime)
            end_date: End date (YYYY-MM-DD or datetime)
        
        Returns:
            pd.DataFrame: Historical data
        """
        print(f"📊 Loading {symbol} {timeframe.upper()} data from CSV...")
        
        # Find CSV file
        csv_pattern = f"{symbol.lower()}_{timeframe.lower()}_*.csv"
        csv_files = list((self.data_folder / "raw").glob(csv_pattern))
        
        if not csv_files:
            raise FileNotFoundError(f"No CSV file found for {symbol} {timeframe}")
        
        # Use the most recent file if multiple exist
        csv_file = max(csv_files, key=lambda x: x.stat().st_mtime)
        print(f"📁 Using file: {csv_file.name}")
        
        # Load data
        df = pd.read_csv(csv_file)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime').reset_index(drop=True)
        
        # Filter by date range if specified
        if start_date or end_date:
            df = self._filter_by_date_range(df, start_date, end_date)
        
        print(f"✅ Loaded {len(df)} bars ({df['datetime'].min()} to {df['datetime'].max()})")
        return df
    
    def load_sqlite_data(self, symbol="EURUSD", timeframe="m15", 
                        start_date=None, end_date=None):
        """
        Load data from SQLite database (faster for large datasets)
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            start_date: Start date
            end_date: End date
        
        Returns:
            pd.DataFrame: Historical data
        """
        print(f"🗄️ Loading {symbol} {timeframe.upper()} data from SQLite...")
        
        if not self.db_path.exists():
            raise FileNotFoundError(f"SQLite database not found: {self.db_path}")
        
        table_name = f"{symbol.lower()}_{timeframe.lower()}"
        
        # Build SQL query
        query = f"SELECT * FROM {table_name}"
        params = []
        
        if start_date or end_date:
            conditions = []
            if start_date:
                conditions.append("datetime >= ?")
                params.append(self._parse_date(start_date))
            if end_date:
                conditions.append("datetime <= ?")
                params.append(self._parse_date(end_date))
            
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY datetime"
        
        # Load data
        with sqlite3.connect(self.db_path) as conn:
            df = pd.read_sql_query(query, conn, params=params)
        
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        print(f"✅ Loaded {len(df)} bars from database")
        return df
    
    def load_data(self, symbol="EURUSD", timeframe="m15", 
                  start_date=None, end_date=None, source="auto"):
        """
        Load data with automatic source selection
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            start_date: Start date
            end_date: End date
            source: "auto", "csv", or "sqlite"
        
        Returns:
            pd.DataFrame: Historical data
        """
        cache_key = f"{symbol}_{timeframe}_{start_date}_{end_date}"
        
        # Check cache first
        if cache_key in self.cache:
            print(f"💾 Using cached data for {symbol} {timeframe}")
            return self.cache[cache_key].copy()
        
        # Auto-select source
        if source == "auto":
            if self.db_path.exists():
                source = "sqlite"
            else:
                source = "csv"
        
        # Load data
        if source == "sqlite":
            df = self.load_sqlite_data(symbol, timeframe, start_date, end_date)
        else:
            df = self.load_csv_data(symbol, timeframe, start_date, end_date)
        
        # Validate and clean data
        df = self.validate_and_clean_data(df)
        
        # Cache the result
        self.cache[cache_key] = df.copy()
        
        return df
    
    def validate_and_clean_data(self, df):
        """
        Validate and clean loaded data
        
        Args:
            df: Raw DataFrame
        
        Returns:
            pd.DataFrame: Cleaned data
        """
        print("🔍 Validating and cleaning data...")
        
        original_length = len(df)
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['datetime']).reset_index(drop=True)
        
        # Sort by datetime
        df = df.sort_values('datetime').reset_index(drop=True)
        
        # Validate OHLC relationships
        invalid_mask = (
            (df['high'] < df['open']) |
            (df['high'] < df['close']) |
            (df['low'] > df['open']) |
            (df['low'] > df['close']) |
            (df['high'] < df['low']) |
            (df['open'] <= 0) |
            (df['high'] <= 0) |
            (df['low'] <= 0) |
            (df['close'] <= 0)
        )
        
        if invalid_mask.any():
            invalid_count = invalid_mask.sum()
            print(f"⚠️ Removing {invalid_count} bars with invalid OHLC data")
            df = df[~invalid_mask].reset_index(drop=True)
        
        # Fill missing values for volume and spread
        df['tick_volume'] = df['tick_volume'].fillna(1)
        df['spread'] = df['spread'].fillna(df['spread'].median())
        df['real_volume'] = df['real_volume'].fillna(0)
        
        # Add derived columns for backtesting
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['hl_avg'] = (df['high'] + df['low']) / 2
        df['range'] = df['high'] - df['low']
        
        # Add bar index for faster lookups
        df['bar_index'] = range(len(df))
        
        cleaned_length = len(df)
        if cleaned_length != original_length:
            print(f"📊 Data cleaning: {original_length} → {cleaned_length} bars")
        
        print(f"✅ Data validation completed: {len(df)} clean bars")
        return df
    
    def get_data_info(self, symbol="EURUSD", timeframe="m15"):
        """
        Get information about available data
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe
        
        Returns:
            dict: Data information
        """
        try:
            df = self.load_data(symbol, timeframe)
            
            info = {
                'symbol': symbol,
                'timeframe': timeframe,
                'total_bars': len(df),
                'date_range': {
                    'start': df['datetime'].min().isoformat(),
                    'end': df['datetime'].max().isoformat(),
                    'days': (df['datetime'].max() - df['datetime'].min()).days
                },
                'price_range': {
                    'min': float(df[['open', 'high', 'low', 'close']].min().min()),
                    'max': float(df[['open', 'high', 'low', 'close']].max().max())
                },
                'volume_stats': {
                    'min_tick_volume': int(df['tick_volume'].min()),
                    'max_tick_volume': int(df['tick_volume'].max()),
                    'avg_tick_volume': float(df['tick_volume'].mean())
                },
                'data_quality': {
                    'missing_bars': 0,  # Would need expected bar count calculation
                    'invalid_bars': 0,  # Cleaned in validation
                    'quality_score': 100.0  # Post-cleaning score
                }
            }
            
            return info
            
        except Exception as e:
            return {'error': str(e)}
    
    def list_available_data(self):
        """
        List all available data files
        
        Returns:
            list: Available datasets
        """
        datasets = []
        
        # Check CSV files
        csv_files = list((self.data_folder / "raw").glob("*.csv"))
        for csv_file in csv_files:
            parts = csv_file.stem.split('_')
            if len(parts) >= 2:
                symbol = parts[0].upper()
                timeframe = parts[1].upper()
                datasets.append({
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'source': 'csv',
                    'file': str(csv_file)
                })
        
        # Check SQLite tables
        if self.db_path.exists():
            with sqlite3.connect(self.db_path) as conn:
                tables = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table'"
                ).fetchall()
                
                for (table_name,) in tables:
                    parts = table_name.split('_')
                    if len(parts) >= 2:
                        symbol = parts[0].upper()
                        timeframe = parts[1].upper()
                        datasets.append({
                            'symbol': symbol,
                            'timeframe': timeframe,
                            'source': 'sqlite',
                            'table': table_name
                        })
        
        return datasets
    
    def _filter_by_date_range(self, df, start_date, end_date):
        """Filter DataFrame by date range"""
        if start_date:
            start_date = self._parse_date(start_date)
            df = df[df['datetime'] >= start_date]
        
        if end_date:
            end_date = self._parse_date(end_date)
            df = df[df['datetime'] <= end_date]
        
        return df.reset_index(drop=True)
    
    def _parse_date(self, date_input):
        """Parse date from various input formats"""
        if isinstance(date_input, str):
            try:
                return pd.to_datetime(date_input)
            except:
                return datetime.strptime(date_input, '%Y-%m-%d')
        elif isinstance(date_input, datetime):
            return date_input
        else:
            return pd.to_datetime(date_input)
    
    def clear_cache(self):
        """Clear the data cache"""
        self.cache.clear()
        print("💾 Data cache cleared")


# Utility functions for common data operations
def resample_data(df, target_timeframe):
    """
    Resample data to different timeframe
    
    Args:
        df: DataFrame with OHLCV data
        target_timeframe: Target timeframe ('5min', '30min', '1H', etc.)
    
    Returns:
        pd.DataFrame: Resampled data
    """
    df = df.set_index('datetime')
    
    resampled = df.resample(target_timeframe).agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'tick_volume': 'sum',
        'spread': 'mean',
        'real_volume': 'sum'
    }).dropna()
    
    resampled = resampled.reset_index()
    return resampled


def split_train_test(df, train_ratio=0.8, method='sequential'):
    """
    Split data into training and testing sets
    
    Args:
        df: DataFrame with historical data
        train_ratio: Ratio for training data (0.0 to 1.0)
        method: 'sequential' or 'random'
    
    Returns:
        tuple: (train_df, test_df)
    """
    if method == 'sequential':
        split_idx = int(len(df) * train_ratio)
        train_df = df.iloc[:split_idx].copy()
        test_df = df.iloc[split_idx:].copy()
    else:  # random
        train_df = df.sample(frac=train_ratio, random_state=42)
        test_df = df.drop(train_df.index)
        train_df = train_df.sort_values('datetime').reset_index(drop=True)
        test_df = test_df.sort_values('datetime').reset_index(drop=True)
    
    return train_df, test_df


def get_trading_sessions(df, timezone='GMT'):
    """
    Add trading session information to data
    
    Args:
        df: DataFrame with datetime column
        timezone: Timezone for session detection
    
    Returns:
        pd.DataFrame: Data with session columns
    """
    df = df.copy()
    
    # Convert to GMT hour if needed
    df['hour'] = df['datetime'].dt.hour
    
    # Define session hours (GMT)
    df['asian_session'] = ((df['hour'] >= 22) | (df['hour'] < 8))
    df['london_session'] = ((df['hour'] >= 7) & (df['hour'] < 16))
    df['ny_session'] = ((df['hour'] >= 12) & (df['hour'] < 21))
    df['overlap_session'] = ((df['hour'] >= 12) & (df['hour'] < 16))
    
    return df


def main():
    """Example usage of DataLoader"""
    print("📊 Data Loader Example")
    print("=====================")
    
    loader = DataLoader()
    
    # List available data
    print("\n📁 Available datasets:")
    datasets = loader.list_available_data()
    for dataset in datasets:
        print(f"   {dataset['symbol']} {dataset['timeframe']} ({dataset['source']})")
    
    if datasets:
        # Load primary dataset
        try:
            print(f"\n📈 Loading EURUSD M15 data...")
            df = loader.load_data("EURUSD", "m15")
            
            print(f"✅ Loaded {len(df)} bars")
            print(f"📅 Date range: {df['datetime'].min()} to {df['datetime'].max()}")
            print(f"💰 Price range: {df['close'].min():.5f} to {df['close'].max():.5f}")
            
            # Show data info
            info = loader.get_data_info("EURUSD", "m15")
            print(f"\n📊 Data Info:")
            print(f"   Total bars: {info['total_bars']:,}")
            print(f"   Date range: {info['date_range']['days']} days")
            print(f"   Quality score: {info['data_quality']['quality_score']}%")
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
    
    else:
        print("❌ No data found. Please run mt5_data_exporter.py first.")


if __name__ == "__main__":
    main()