# Ultra Backtesting System

A comprehensive Python backtesting and optimization system for the Ultra Conservative EA trading strategy. This system enables MetaTrader-free backtesting, optimization, and validation on Mac and other platforms.

## 🚀 Key Features

- **MetaTrader-Free Operation**: Run complete backtesting and optimization without MT5 runtime dependencies
- **Professional Backtesting Engine**: Event-driven simulation with realistic execution and slippage
- **Advanced Parameter Optimization**: Bayesian optimization using Optuna with overfitting protection
- **Walk-Forward Analysis**: Robust out-of-sample validation and parameter stability testing
- **Comprehensive Reporting**: Institutional-grade performance analysis and risk assessment
- **MT5 Integration**: Seamless export of optimized parameters back to MetaTrader 5
- **Cross-Platform**: Native operation on Mac, Windows, and Linux

## 📋 System Requirements

- Python 3.8 or higher
- 4GB RAM minimum (8GB recommended for optimization)
- MetaTrader 5 (for initial data export only)

## 🔧 Installation

1. **Clone or download the system:**
   ```bash
   git clone <repository-url>
   cd ultra_backtesting_system
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation:**
   ```bash
   python example_usage.py
   ```

## 📊 Quick Start Guide

### Step 1: Data Export (One-time Setup)

First, export historical data from MetaTrader 5:

```python
from ultra_backtesting_system.data.mt5_data_exporter import MT5DataExporter

# Export EURUSD M15 data
exporter = MT5DataExporter()
exporter.export_symbol_data(
    symbol='EURUSD',
    timeframe='M15',
    start_date='2024-01-01',
    end_date='2024-12-31',
    output_path='data/EURUSD_M15.csv'
)
```

### Step 2: Load Data and Run Backtest

```python
from ultra_backtesting_system import (
    DataLoader, TechnicalIndicators, UltraStrategy, BacktestEngine
)

# Load data
data_loader = DataLoader()
data = data_loader.load_data('EURUSD', 'M15')

# Calculate indicators
indicators = TechnicalIndicators(data)
data_with_indicators = indicators.calculate_all_indicators()

# Run backtest
strategy = UltraStrategy()
engine = BacktestEngine(initial_balance=1000.0)
results = engine.run_backtest(data_with_indicators, strategy)

print(f"Total Return: {results['total_return']:.2f}%")
print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
print(f"Max Drawdown: {results['max_drawdown']:.2f}%")
```

### Step 3: Parameter Optimization

```python
from ultra_backtesting_system import ParameterOptimizer

# Optimize parameters
optimizer = ParameterOptimizer(data_loader, UltraStrategy, BacktestEngine)
opt_results = optimizer.optimize_parameters(
    symbol='EURUSD',
    timeframe='M15',
    start_date='2024-01-01',
    end_date='2024-06-30'
)

print(f"Best Parameters: {opt_results['best_params']}")
print(f"Best Value: {opt_results['best_value']:.4f}")
```

### Step 4: Export to MT5

```python
from ultra_backtesting_system import ParameterExporter

# Export optimized parameters
exporter = ParameterExporter()
files = exporter.export_parameters(
    python_params=opt_results['best_params'],
    optimization_info=opt_results,
    symbol='EURUSD',
    timeframe='M15'
)

print(f"MT5 .set file created: {files['set_file']}")
```

## 🏗️ System Architecture

```
Ultra Backtesting System
├── data/                   # Data management
│   ├── mt5_data_exporter.py   # Export from MT5
│   └── data_loader.py         # Load exported data
├── indicators/             # Technical indicators
│   └── technical_indicators.py # MT5-compatible calculations
├── strategy/              # Trading strategy
│   └── ultra_strategy.py     # Ultra Conservative EA logic
├── backtesting/           # Backtesting engine
│   └── backtest_engine.py    # Event-driven simulation
├── optimization/          # Parameter optimization
│   ├── optimizer.py          # Optuna-based optimization
│   └── walk_forward.py       # Walk-forward analysis
├── reporting/             # Analysis and reporting
│   ├── performance_analyzer.py # Performance metrics
│   ├── validation.py         # Results validation
│   └── report_generator.py   # Professional reports
└── mt5_export/           # MT5 integration
    ├── parameter_exporter.py # Parameter conversion
    └── set_file_generator.py # .set file creation
```

## 📈 Strategy Overview

The Ultra Conservative EA implements two complementary strategies:

### Slingshot Strategy (Trending Markets)
- **Trigger**: MACD crossover in trending conditions (ADX > 25)
- **Filters**: EMA trend filter, RSI momentum confirmation
- **Risk**: Conservative position sizing with adaptive scaling

### Divergence Strategy (Ranging Markets)  
- **Trigger**: MACD/Price divergence in ranging conditions (ADX < 25)
- **Filters**: Bollinger Bands volatility filter
- **Risk**: Enhanced position management with partial profits

### Risk Management Features
- **Portfolio Heat Management**: Maximum 6% total risk exposure
- **Adaptive Risk Scaling**: Increase/decrease risk based on recent performance
- **Enhanced Position Management**: Partial take profits and ATR-based trailing
- **Correlation Limits**: Prevent over-exposure to correlated trades
- **Time and News Filters**: Avoid high-risk trading periods

## 🔬 Optimization Features

### Bayesian Optimization
- **Algorithm**: Tree-structured Parzen Estimator (TPE)
- **Objectives**: Profit factor, Sharpe ratio, drawdown minimization
- **Constraints**: Minimum trades, maximum drawdown limits
- **Validation**: Out-of-sample testing and overfitting detection

### Walk-Forward Analysis
- **Methods**: Rolling window and anchored analysis
- **Validation**: Parameter stability and performance consistency
- **Periods**: Configurable training/testing windows
- **Reporting**: Comprehensive robustness analysis

## 📊 Performance Analysis

### Risk Metrics
- **Sharpe Ratio**: Risk-adjusted returns
- **Sortino Ratio**: Downside deviation focus  
- **Calmar Ratio**: Return to max drawdown
- **VaR/CVaR**: Value at Risk calculations
- **Maximum Drawdown**: Peak-to-trough analysis

### Trade Analysis
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross profit to gross loss ratio
- **Expectancy**: Expected value per trade
- **Consecutive Analysis**: Win/loss streaks
- **Duration Analysis**: Trade holding periods

### Statistical Validation
- **Significance Testing**: T-tests for statistical significance
- **Normality Tests**: Return distribution analysis
- **Overfitting Detection**: Multiple validation checks
- **Reality Checks**: Performance reasonableness validation

## 📁 File Formats

### Data Files
- **CSV**: Historical price data (OHLCV + indicators)
- **SQLite**: Alternative storage format
- **JSON**: Configuration and metadata

### Export Files
- **.set**: MT5 parameter files for direct import
- **JSON**: Structured parameter data
- **CSV**: Spreadsheet-compatible format
- **TXT**: Human-readable documentation

### Report Files
- **TXT**: Executive summaries and detailed analysis
- **JSON**: Structured results data
- **CSV**: Performance metrics tables

## ⚙️ Configuration

### Key Parameters

```python
# Strategy Parameters
'max_risk_percent': 1.0,        # Maximum risk per trade
'take_profit_ratio': 2.0,       # TP:SL ratio
'max_concurrent_trades': 3,     # Portfolio limit
'use_adaptive_risk': True,      # Dynamic risk scaling

# Optimization Settings
'n_trials': 200,                # Optimization trials
'timeout_hours': 4,             # Maximum time
'train_ratio': 0.6,             # Training data split
'validation_ratio': 0.2,        # Validation split

# Backtest Settings
'initial_balance': 1000.0,      # Starting capital
'spread_pips': 1.5,             # Bid-ask spread
'commission_per_lot': 7.0,      # Transaction costs
```

## 🚨 Important Notes

### Data Requirements
- **Minimum**: 1000 bars for backtesting
- **Recommended**: 5000+ bars for optimization
- **Quality**: Clean OHLCV data with minimal gaps

### Optimization Guidelines
- **Overfitting**: Use walk-forward analysis for validation
- **Parameters**: Limit optimization to key parameters only
- **Timeframes**: Test on multiple timeframes
- **Robustness**: Verify stability across market conditions

### MT5 Integration
- **One-time Export**: Data export from MT5 is one-time setup
- **Parameter Import**: Use generated .set files in MT5
- **Testing**: Always test on demo account first
- **Validation**: Compare Python and MT5 backtest results

## 🔍 Troubleshooting

### Common Issues

1. **Memory Errors During Optimization**
   - Reduce `n_trials` parameter
   - Use smaller datasets
   - Increase available RAM

2. **Slow Performance**
   - Enable parallel processing (`n_jobs > 1`)
   - Use SSD storage for data files
   - Optimize Python environment

3. **Parameter Export Issues**
   - Verify MT5 parameter names match EA
   - Check parameter constraints
   - Validate .set file format

4. **Data Quality Issues**
   - Check for missing values
   - Verify time series continuity
   - Ensure sufficient data range

## 📚 Advanced Usage

### Custom Optimization Objectives

```python
def custom_objective(trial, data):
    params = suggest_parameters(trial)
    strategy = UltraStrategy(params)
    engine = BacktestEngine()
    results = engine.run_backtest(data, strategy)
    
    # Custom objective combining return and risk
    return results['total_return'] / max(results['max_drawdown'], 1)

study.optimize(custom_objective, n_trials=100)
```

### Multi-Symbol Optimization

```python
symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
results = {}

for symbol in symbols:
    data = data_loader.load_data(symbol, 'M15')
    opt_result = optimizer.optimize_parameters(symbol=symbol)
    results[symbol] = opt_result
```

### Custom Indicators

```python
class CustomIndicators(TechnicalIndicators):
    def custom_ma_cross(self, fast=10, slow=20):
        fast_ma = self.ema(fast)
        slow_ma = self.ema(slow)
        return fast_ma > slow_ma
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Trading involves substantial risk of loss and is not suitable for all investors. Past performance is not indicative of future results. Always test strategies thoroughly on demo accounts before live trading.

## 📞 Support

For questions, issues, or feature requests:
- Create an issue on GitHub
- Review the troubleshooting section
- Check the example usage files
- Consult the comprehensive documentation

---

**Ultra Backtesting System v1.0.0**  
*Professional trading strategy backtesting and optimization*