#!/usr/bin/env python3
"""
Parameter Exporter
==================

Export optimized parameters from Python backtesting to MT5 format.
Handles parameter mapping, validation, and format conversion.

Features:
- Python to MT5 parameter mapping
- Parameter validation and constraints
- Multiple export formats (.set files, JSON, CSV)
- Parameter documentation generation
- Validation against MT5 EA parameter ranges

This module bridges the gap between Python optimization results
and MT5 EA parameters for seamless strategy deployment.
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import json
import warnings

class ParameterExporter:
    """
    Export optimized parameters to MT5 format
    
    Handles conversion of Python strategy parameters to
    MT5 EA parameter format with proper validation.
    """
    
    def __init__(self, config: dict = None):
        """
        Initialize parameter exporter
        
        Args:
            config: Export configuration
        """
        self.config = config or self._get_default_config()
        self.parameter_mapping = self._define_parameter_mapping()
        self.mt5_constraints = self._define_mt5_constraints()
        
        print(f"📤 Parameter Exporter initialized")
    
    def _get_default_config(self) -> dict:
        """Get default export configuration"""
        return {
            # Output settings
            'output_directory': 'mt5_parameters',
            'create_documentation': True,
            'validate_parameters': True,
            'round_precision': 6,
            
            # File formats
            'export_set_file': True,
            'export_json': True,
            'export_csv': True,
            'export_documentation': True,
            
            # MT5 settings
            'ea_name': 'UltraConservativeEA',
            'include_optimization_info': True,
            'include_performance_summary': True,
        }
    
    def _define_parameter_mapping(self) -> Dict[str, Dict]:
        """
        Define mapping between Python parameters and MT5 parameters
        
        Returns:
            dict: Parameter mapping configuration
        """
        return {
            # MACD parameters
            'macd_fast': {
                'mt5_name': 'MACD_Fast',
                'mt5_type': 'int',
                'description': 'MACD Fast EMA Period',
                'category': 'Indicators'
            },
            'macd_slow': {
                'mt5_name': 'MACD_Slow', 
                'mt5_type': 'int',
                'description': 'MACD Slow EMA Period',
                'category': 'Indicators'
            },
            'macd_signal': {
                'mt5_name': 'MACD_Signal',
                'mt5_type': 'int', 
                'description': 'MACD Signal Line Period',
                'category': 'Indicators'
            },
            
            # Bollinger Bands
            'bb_period': {
                'mt5_name': 'BB_Period',
                'mt5_type': 'int',
                'description': 'Bollinger Bands Period',
                'category': 'Indicators'
            },
            'bb_deviation': {
                'mt5_name': 'BB_Deviation',
                'mt5_type': 'double',
                'description': 'Bollinger Bands Standard Deviation',
                'category': 'Indicators'
            },
            'min_bb_width_pips': {
                'mt5_name': 'MinBBWidthPips',
                'mt5_type': 'double',
                'description': 'Minimum BB Width in Pips',
                'category': 'Filters'
            },
            
            # Risk Management
            'max_risk_percent': {
                'mt5_name': 'MaxRiskPercent',
                'mt5_type': 'double',
                'description': 'Maximum Risk Per Trade (%)',
                'category': 'Risk Management'
            },
            'take_profit_ratio': {
                'mt5_name': 'TakeProfitRatio',
                'mt5_type': 'double',
                'description': 'Take Profit to Stop Loss Ratio',
                'category': 'Risk Management'
            },
            'stop_loss_buffer': {
                'mt5_name': 'StopLossBuffer',
                'mt5_type': 'double',
                'description': 'Stop Loss Buffer in Pips',
                'category': 'Risk Management'
            },
            'trailing_stop': {
                'mt5_name': 'TrailingStop',
                'mt5_type': 'double',
                'description': 'Trailing Stop Distance in Pips',
                'category': 'Risk Management'
            },
            'break_even_level': {
                'mt5_name': 'BreakEvenLevel',
                'mt5_type': 'double',
                'description': 'Break Even Trigger Level (R:R)',
                'category': 'Risk Management'
            },
            
            # Trend Filters
            'ema_period': {
                'mt5_name': 'EMA_Period',
                'mt5_type': 'int',
                'description': 'EMA Trend Filter Period',
                'category': 'Filters'
            },
            'adx_trend': {
                'mt5_name': 'ADX_Trend',
                'mt5_type': 'double',
                'description': 'ADX Trend Threshold',
                'category': 'Filters'
            },
            'rsi_period': {
                'mt5_name': 'RSI_Period',
                'mt5_type': 'int',
                'description': 'RSI Period',
                'category': 'Indicators'
            },
            
            # Portfolio Management
            'max_concurrent_trades': {
                'mt5_name': 'MaxConcurrentTrades',
                'mt5_type': 'int',
                'description': 'Maximum Concurrent Trades',
                'category': 'Portfolio'
            },
            'max_portfolio_heat': {
                'mt5_name': 'MaxPortfolioHeat',
                'mt5_type': 'double',
                'description': 'Maximum Portfolio Heat (%)',
                'category': 'Portfolio'
            },
            'max_currency_exposure': {
                'mt5_name': 'MaxCurrencyExposure',
                'mt5_type': 'double',
                'description': 'Maximum Currency Exposure (%)',
                'category': 'Portfolio'
            },
            'correlation_limit': {
                'mt5_name': 'CorrelationLimit',
                'mt5_type': 'double',
                'description': 'Maximum Correlation Limit',
                'category': 'Portfolio'
            },
            
            # Adaptive Risk
            'use_adaptive_risk': {
                'mt5_name': 'UseAdaptiveRisk',
                'mt5_type': 'bool',
                'description': 'Enable Adaptive Risk Scaling',
                'category': 'Advanced'
            },
            'max_risk_multiplier': {
                'mt5_name': 'MaxRiskMultiplier',
                'mt5_type': 'double',
                'description': 'Maximum Risk Multiplier',
                'category': 'Advanced'
            },
            'min_risk_multiplier': {
                'mt5_name': 'MinRiskMultiplier',
                'mt5_type': 'double',
                'description': 'Minimum Risk Multiplier',
                'category': 'Advanced'
            },
            'consecutive_win_bonus': {
                'mt5_name': 'ConsecutiveWinBonus',
                'mt5_type': 'int',
                'description': 'Consecutive Wins for Bonus',
                'category': 'Advanced'
            },
            'consecutive_loss_reduction': {
                'mt5_name': 'ConsecutiveLossReduction',
                'mt5_type': 'int',
                'description': 'Consecutive Losses for Reduction',
                'category': 'Advanced'
            },
            
            # Enhanced Position Management
            'use_partial_tp': {
                'mt5_name': 'UsePartialTP',
                'mt5_type': 'bool',
                'description': 'Enable Partial Take Profits',
                'category': 'Advanced'
            },
            'partial_tp1_rr': {
                'mt5_name': 'PartialTP1_RR',
                'mt5_type': 'double',
                'description': 'First Partial TP R:R Ratio',
                'category': 'Advanced'
            },
            'partial_tp1_percent': {
                'mt5_name': 'PartialTP1_Percent',
                'mt5_type': 'double',
                'description': 'First Partial TP Percentage',
                'category': 'Advanced'
            },
            'partial_tp2_rr': {
                'mt5_name': 'PartialTP2_RR',
                'mt5_type': 'double',
                'description': 'Second Partial TP R:R Ratio',
                'category': 'Advanced'
            },
            'partial_tp2_percent': {
                'mt5_name': 'PartialTP2_Percent',
                'mt5_type': 'double',
                'description': 'Second Partial TP Percentage',
                'category': 'Advanced'
            },
            
            # ATR and Volatility
            'use_atr_trailing': {
                'mt5_name': 'UseATRTrailing',
                'mt5_type': 'bool',
                'description': 'Enable ATR-based Trailing Stop',
                'category': 'Advanced'
            },
            'atr_trailing_multiplier': {
                'mt5_name': 'ATRTrailingMultiplier',
                'mt5_type': 'double',
                'description': 'ATR Trailing Stop Multiplier',
                'category': 'Advanced'
            },
            'use_atr_filter': {
                'mt5_name': 'UseATRFilter',
                'mt5_type': 'bool',
                'description': 'Enable ATR Volatility Filter',
                'category': 'Filters'
            },
            'atr_period': {
                'mt5_name': 'ATR_Period',
                'mt5_type': 'int',
                'description': 'ATR Indicator Period',
                'category': 'Indicators'
            },
            'min_atr_multiplier': {
                'mt5_name': 'MinATRMultiplier',
                'mt5_type': 'double',
                'description': 'Minimum ATR Multiplier',
                'category': 'Filters'
            },
            'max_atr_multiplier': {
                'mt5_name': 'MaxATRMultiplier',
                'mt5_type': 'double',
                'description': 'Maximum ATR Multiplier',
                'category': 'Filters'
            },
            'use_volatility_adjustment': {
                'mt5_name': 'UseVolatilityAdjustment',
                'mt5_type': 'bool',
                'description': 'Enable Volatility Position Sizing',
                'category': 'Advanced'
            },
            
            # News and Time Filters
            'avoid_news': {
                'mt5_name': 'AvoidNews',
                'mt5_type': 'bool',
                'description': 'Avoid Trading During News',
                'category': 'Filters'
            },
            'news_buffer_minutes': {
                'mt5_name': 'NewsBufferMinutes',
                'mt5_type': 'int',
                'description': 'News Buffer Time (Minutes)',
                'category': 'Filters'
            },
            'start_hour': {
                'mt5_name': 'StartHour',
                'mt5_type': 'int',
                'description': 'Trading Start Hour (GMT)',
                'category': 'Time'
            },
            'end_hour': {
                'mt5_name': 'EndHour',
                'mt5_type': 'int',
                'description': 'Trading End Hour (GMT)',
                'category': 'Time'
            },
            'trade_only_overlap': {
                'mt5_name': 'TradeOnlyOverlap',
                'mt5_type': 'bool',
                'description': 'Trade Only During Session Overlap',
                'category': 'Time'
            },
            'reduce_risk_friday': {
                'mt5_name': 'ReduceRiskFriday',
                'mt5_type': 'bool',
                'description': 'Reduce Risk on Fridays',
                'category': 'Time'
            },
            'friday_risk_reduction': {
                'mt5_name': 'FridayRiskReduction',
                'mt5_type': 'double',
                'description': 'Friday Risk Reduction Factor',
                'category': 'Time'
            }
        }
    
    def _define_mt5_constraints(self) -> Dict[str, Dict]:
        """Define MT5 parameter constraints and validation rules"""
        return {
            # Integer constraints
            'MACD_Fast': {'min': 1, 'max': 50, 'step': 1},
            'MACD_Slow': {'min': 10, 'max': 100, 'step': 1},
            'MACD_Signal': {'min': 1, 'max': 50, 'step': 1},
            'BB_Period': {'min': 5, 'max': 100, 'step': 1},
            'EMA_Period': {'min': 50, 'max': 500, 'step': 1},
            'RSI_Period': {'min': 5, 'max': 50, 'step': 1},
            'ATR_Period': {'min': 5, 'max': 50, 'step': 1},
            'MaxConcurrentTrades': {'min': 1, 'max': 10, 'step': 1},
            'ConsecutiveWinBonus': {'min': 1, 'max': 10, 'step': 1},
            'ConsecutiveLossReduction': {'min': 1, 'max': 10, 'step': 1},
            'NewsBufferMinutes': {'min': 0, 'max': 120, 'step': 1},
            'StartHour': {'min': 0, 'max': 23, 'step': 1},
            'EndHour': {'min': 0, 'max': 23, 'step': 1},
            
            # Double constraints
            'BB_Deviation': {'min': 0.5, 'max': 5.0, 'step': 0.1},
            'MinBBWidthPips': {'min': 5.0, 'max': 50.0, 'step': 1.0},
            'MaxRiskPercent': {'min': 0.1, 'max': 5.0, 'step': 0.1},
            'TakeProfitRatio': {'min': 1.0, 'max': 5.0, 'step': 0.1},
            'StopLossBuffer': {'min': 0.0, 'max': 50.0, 'step': 1.0},
            'TrailingStop': {'min': 0.0, 'max': 100.0, 'step': 1.0},
            'BreakEvenLevel': {'min': 0.5, 'max': 3.0, 'step': 0.1},
            'ADX_Trend': {'min': 10.0, 'max': 50.0, 'step': 1.0},
            'MaxPortfolioHeat': {'min': 1.0, 'max': 20.0, 'step': 1.0},
            'MaxCurrencyExposure': {'min': 10.0, 'max': 100.0, 'step': 5.0},
            'CorrelationLimit': {'min': 0.1, 'max': 1.0, 'step': 0.1},
            'MaxRiskMultiplier': {'min': 1.0, 'max': 5.0, 'step': 0.1},
            'MinRiskMultiplier': {'min': 0.1, 'max': 1.0, 'step': 0.1},
            'PartialTP1_RR': {'min': 0.5, 'max': 3.0, 'step': 0.1},
            'PartialTP1_Percent': {'min': 10.0, 'max': 80.0, 'step': 5.0},
            'PartialTP2_RR': {'min': 1.0, 'max': 5.0, 'step': 0.1},
            'PartialTP2_Percent': {'min': 10.0, 'max': 80.0, 'step': 5.0},
            'ATRTrailingMultiplier': {'min': 0.5, 'max': 3.0, 'step': 0.1},
            'MinATRMultiplier': {'min': 0.1, 'max': 2.0, 'step': 0.1},
            'MaxATRMultiplier': {'min': 1.0, 'max': 10.0, 'step': 0.1},
            'FridayRiskReduction': {'min': 0.1, 'max': 1.0, 'step': 0.1},
        }
    
    # =========================================================================
    # MAIN EXPORT INTERFACE
    # =========================================================================
    
    def export_parameters(self, python_params: Dict, 
                         optimization_info: Dict = None,
                         performance_summary: Dict = None,
                         symbol: str = 'EURUSD',
                         timeframe: str = 'M15') -> Dict[str, str]:
        """
        Export Python parameters to MT5 format
        
        Args:
            python_params: Dictionary of Python strategy parameters
            optimization_info: Information about optimization process
            performance_summary: Performance metrics summary
            symbol: Trading symbol
            timeframe: Timeframe
        
        Returns:
            dict: Paths to generated files
        """
        print(f"📤 Exporting parameters to MT5 format...")
        
        # Validate parameters
        if self.config['validate_parameters']:
            validation_results = self._validate_parameters(python_params)
            if validation_results['errors']:
                print(f"❌ Parameter validation failed:")
                for error in validation_results['errors']:
                    print(f"   - {error}")
                return {}
        
        # Convert to MT5 parameters
        mt5_params = self._convert_to_mt5_parameters(python_params)
        
        # Create output directory
        output_dir = Path(self.config['output_directory'])
        output_dir.mkdir(exist_ok=True)
        
        # Generate timestamp for filenames
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Generate files
        generated_files = {}
        
        # Export .set file
        if self.config['export_set_file']:
            set_file_path = self._export_set_file(
                mt5_params, output_dir, symbol, timeframe, timestamp,
                optimization_info, performance_summary
            )
            generated_files['set_file'] = str(set_file_path)
        
        # Export JSON
        if self.config['export_json']:
            json_file_path = self._export_json(
                python_params, mt5_params, output_dir, symbol, timeframe, timestamp,
                optimization_info, performance_summary
            )
            generated_files['json_file'] = str(json_file_path)
        
        # Export CSV
        if self.config['export_csv']:
            csv_file_path = self._export_csv(
                mt5_params, output_dir, symbol, timeframe, timestamp
            )
            generated_files['csv_file'] = str(csv_file_path)
        
        # Export documentation
        if self.config['export_documentation']:
            doc_file_path = self._export_documentation(
                python_params, mt5_params, output_dir, symbol, timeframe, timestamp,
                optimization_info, performance_summary
            )
            generated_files['documentation'] = str(doc_file_path)
        
        print(f"✅ Parameter export completed")
        print(f"📁 Files saved to: {output_dir}")
        for file_type, file_path in generated_files.items():
            print(f"   {file_type}: {Path(file_path).name}")
        
        return generated_files
    
    # =========================================================================
    # PARAMETER CONVERSION
    # =========================================================================
    
    def _convert_to_mt5_parameters(self, python_params: Dict) -> Dict:
        """Convert Python parameters to MT5 format"""
        mt5_params = {}
        
        for python_name, value in python_params.items():
            if python_name in self.parameter_mapping:
                mapping = self.parameter_mapping[python_name]
                mt5_name = mapping['mt5_name']
                mt5_type = mapping['mt5_type']
                
                # Convert value based on type
                if mt5_type == 'int':
                    mt5_value = int(round(value))
                elif mt5_type == 'double':
                    mt5_value = round(float(value), self.config['round_precision'])
                elif mt5_type == 'bool':
                    mt5_value = bool(value)
                else:
                    mt5_value = value
                
                # Apply constraints if defined
                if mt5_name in self.mt5_constraints:
                    mt5_value = self._apply_constraints(mt5_value, self.mt5_constraints[mt5_name])
                
                mt5_params[mt5_name] = {
                    'value': mt5_value,
                    'type': mt5_type,
                    'description': mapping['description'],
                    'category': mapping['category'],
                    'python_name': python_name
                }
        
        return mt5_params
    
    def _apply_constraints(self, value: Any, constraints: Dict) -> Any:
        """Apply MT5 parameter constraints"""
        if 'min' in constraints and value < constraints['min']:
            value = constraints['min']
        
        if 'max' in constraints and value > constraints['max']:
            value = constraints['max']
        
        if 'step' in constraints and isinstance(value, (int, float)):
            # Round to nearest step
            step = constraints['step']
            value = round(value / step) * step
        
        return value
    
    # =========================================================================
    # PARAMETER VALIDATION
    # =========================================================================
    
    def _validate_parameters(self, params: Dict) -> Dict:
        """Validate parameters against MT5 constraints"""
        errors = []
        warnings = []
        
        for param_name, value in params.items():
            if param_name in self.parameter_mapping:
                mapping = self.parameter_mapping[param_name]
                mt5_name = mapping['mt5_name']
                mt5_type = mapping['mt5_type']
                
                # Type validation
                if mt5_type == 'int' and not isinstance(value, (int, float)):
                    errors.append(f"{param_name}: Expected numeric value, got {type(value)}")
                    continue
                
                if mt5_type == 'double' and not isinstance(value, (int, float)):
                    errors.append(f"{param_name}: Expected numeric value, got {type(value)}")
                    continue
                
                if mt5_type == 'bool' and not isinstance(value, bool):
                    warnings.append(f"{param_name}: Expected boolean, will convert {value}")
                
                # Constraint validation
                if mt5_name in self.mt5_constraints:
                    constraints = self.mt5_constraints[mt5_name]
                    
                    if 'min' in constraints and value < constraints['min']:
                        warnings.append(f"{param_name}: Value {value} below minimum {constraints['min']}")
                    
                    if 'max' in constraints and value > constraints['max']:
                        warnings.append(f"{param_name}: Value {value} above maximum {constraints['max']}")
        
        # Logical validations
        if 'macd_fast' in params and 'macd_slow' in params:
            if params['macd_fast'] >= params['macd_slow']:
                errors.append("MACD fast period must be less than slow period")
        
        if 'partial_tp1_percent' in params and 'partial_tp2_percent' in params:
            total_partial = params['partial_tp1_percent'] + params['partial_tp2_percent']
            if total_partial > 100:
                errors.append(f"Total partial TP percentage ({total_partial}%) exceeds 100%")
        
        if 'start_hour' in params and 'end_hour' in params:
            if params['start_hour'] >= params['end_hour']:
                warnings.append("Trading start hour should be before end hour")
        
        return {
            'errors': errors,
            'warnings': warnings,
            'valid': len(errors) == 0
        }
    
    # =========================================================================
    # FILE EXPORT METHODS
    # =========================================================================
    
    def _export_set_file(self, mt5_params: Dict, output_dir: Path,
                        symbol: str, timeframe: str, timestamp: str,
                        optimization_info: Dict = None,
                        performance_summary: Dict = None) -> Path:
        """Export parameters as MT5 .set file"""
        filename = f"{self.config['ea_name']}_{symbol}_{timeframe}_{timestamp}.set"
        file_path = output_dir / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            # Header
            f.write(";\\n")
            f.write(f"; {self.config['ea_name']} - Optimized Parameters\\n")
            f.write(f"; Symbol: {symbol}\\n")
            f.write(f"; Timeframe: {timeframe}\\n")
            f.write(f"; Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n")
            
            if optimization_info:
                f.write(f"; Optimization Method: {optimization_info.get('method', 'N/A')}\\n")
                f.write(f"; Optimization Trials: {optimization_info.get('trials', 'N/A')}\\n")
            
            if performance_summary:
                f.write(f"; Backtest Return: {performance_summary.get('total_return', 'N/A')}%\\n")
                f.write(f"; Sharpe Ratio: {performance_summary.get('sharpe_ratio', 'N/A')}\\n")
                f.write(f"; Max Drawdown: {performance_summary.get('max_drawdown', 'N/A')}%\\n")
            
            f.write(";\\n")
            f.write("\\n")
            
            # Parameters by category
            categories = {}
            for param_name, param_info in mt5_params.items():
                category = param_info['category']
                if category not in categories:
                    categories[category] = []
                categories[category].append((param_name, param_info))
            
            for category, params in categories.items():
                f.write(f"; {category} Parameters\\n")
                for param_name, param_info in params:
                    value = param_info['value']
                    description = param_info['description']
                    
                    # Format value based on type
                    if param_info['type'] == 'bool':
                        formatted_value = 'true' if value else 'false'
                    elif param_info['type'] == 'int':
                        formatted_value = str(int(value))
                    else:
                        formatted_value = f"{value:.{self.config['round_precision']}f}".rstrip('0').rstrip('.')
                    
                    f.write(f"{param_name}={formatted_value}\\n")
                
                f.write("\\n")
        
        return file_path
    
    def _export_json(self, python_params: Dict, mt5_params: Dict,
                    output_dir: Path, symbol: str, timeframe: str, timestamp: str,
                    optimization_info: Dict = None,
                    performance_summary: Dict = None) -> Path:
        """Export parameters as JSON file"""
        filename = f"{self.config['ea_name']}_{symbol}_{timeframe}_{timestamp}.json"
        file_path = output_dir / filename
        
        export_data = {
            'metadata': {
                'ea_name': self.config['ea_name'],
                'symbol': symbol,
                'timeframe': timeframe,
                'generated_at': datetime.now().isoformat(),
                'export_version': '1.0'
            },
            'python_parameters': python_params,
            'mt5_parameters': {
                name: {
                    'value': info['value'],
                    'type': info['type'],
                    'description': info['description'],
                    'category': info['category']
                }
                for name, info in mt5_params.items()
            }
        }
        
        if optimization_info:
            export_data['optimization_info'] = optimization_info
        
        if performance_summary:
            export_data['performance_summary'] = performance_summary
        
        with open(file_path, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        return file_path
    
    def _export_csv(self, mt5_params: Dict, output_dir: Path,
                   symbol: str, timeframe: str, timestamp: str) -> Path:
        """Export parameters as CSV file"""
        filename = f"{self.config['ea_name']}_{symbol}_{timeframe}_{timestamp}.csv"
        file_path = output_dir / filename
        
        # Create DataFrame
        data = []
        for param_name, param_info in mt5_params.items():
            data.append({
                'Parameter': param_name,
                'Value': param_info['value'],
                'Type': param_info['type'],
                'Category': param_info['category'],
                'Description': param_info['description']
            })
        
        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False)
        
        return file_path
    
    def _export_documentation(self, python_params: Dict, mt5_params: Dict,
                             output_dir: Path, symbol: str, timeframe: str, timestamp: str,
                             optimization_info: Dict = None,
                             performance_summary: Dict = None) -> Path:
        """Export parameter documentation"""
        filename = f"{self.config['ea_name']}_{symbol}_{timeframe}_{timestamp}_documentation.txt"
        file_path = output_dir / filename
        
        with open(file_path, 'w') as f:
            f.write(f"{self.config['ea_name']} - Parameter Documentation\\n")
            f.write("=" * 60 + "\\n\\n")
            
            f.write(f"Symbol: {symbol}\\n")
            f.write(f"Timeframe: {timeframe}\\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n")
            
            # Optimization Information
            if optimization_info:
                f.write("OPTIMIZATION INFORMATION\\n")
                f.write("-" * 30 + "\\n")
                for key, value in optimization_info.items():
                    f.write(f"{key}: {value}\\n")
                f.write("\\n")
            
            # Performance Summary
            if performance_summary:
                f.write("PERFORMANCE SUMMARY\\n")
                f.write("-" * 25 + "\\n")
                for key, value in performance_summary.items():
                    if isinstance(value, float):
                        f.write(f"{key}: {value:.2f}\\n")
                    else:
                        f.write(f"{key}: {value}\\n")
                f.write("\\n")
            
            # Parameters by category
            categories = {}
            for param_name, param_info in mt5_params.items():
                category = param_info['category']
                if category not in categories:
                    categories[category] = []
                categories[category].append((param_name, param_info))
            
            for category, params in categories.items():
                f.write(f"{category.upper()} PARAMETERS\\n")
                f.write("-" * (len(category) + 11) + "\\n")
                
                for param_name, param_info in params:
                    f.write(f"\\n{param_name}\\n")
                    f.write(f"  Value: {param_info['value']}\\n")
                    f.write(f"  Type: {param_info['type']}\\n")
                    f.write(f"  Description: {param_info['description']}\\n")
                    
                    # Add constraints if available
                    if param_name in self.mt5_constraints:
                        constraints = self.mt5_constraints[param_name]
                        f.write(f"  Constraints: ")
                        constraint_parts = []
                        if 'min' in constraints:
                            constraint_parts.append(f"min={constraints['min']}")
                        if 'max' in constraints:
                            constraint_parts.append(f"max={constraints['max']}")
                        if 'step' in constraints:
                            constraint_parts.append(f"step={constraints['step']}")
                        f.write(", ".join(constraint_parts) + "\\n")
                
                f.write("\\n")
            
            # Footer
            f.write("\\nNOTES:\\n")
            f.write("- Import this .set file into MT5 Expert Advisor tester\\n")
            f.write("- Verify all parameters match your EA input definitions\\n")
            f.write("- Test parameters on demo account before live trading\\n")
            f.write("- Monitor performance and adjust as needed\\n")
        
        return file_path


def main():
    """Example usage of ParameterExporter"""
    print("📤 Parameter Exporter Example")
    print("=============================")
    
    # Create exporter
    exporter = ParameterExporter()
    
    # Example Python parameters
    example_params = {
        'macd_fast': 12,
        'macd_slow': 26,
        'macd_signal': 9,
        'bb_period': 20,
        'bb_deviation': 2.0,
        'max_risk_percent': 1.0,
        'take_profit_ratio': 2.0,
        'ema_period': 200,
        'adx_trend': 25.0,
        'use_adaptive_risk': True,
        'max_concurrent_trades': 3
    }
    
    print("✅ This is an example of the ParameterExporter")
    print("🔄 To use in practice:")
    print("   1. Pass optimized Python parameters to export_parameters()")
    print("   2. Include optimization info and performance summary")
    print("   3. Files will be generated in multiple formats")
    
    print(f"\\n📊 Example parameter mapping:")
    for python_name, value in list(example_params.items())[:5]:
        if python_name in exporter.parameter_mapping:
            mt5_name = exporter.parameter_mapping[python_name]['mt5_name']
            print(f"   {python_name} -> {mt5_name}: {value}")
    print("   ... and more")
    
    print(f"\\n📁 Output formats:")
    print(f"   - .set file (for MT5 import)")
    print(f"   - JSON file (structured data)")
    print(f"   - CSV file (spreadsheet format)")
    print(f"   - Documentation (human readable)")


if __name__ == "__main__":
    main()