#!/usr/bin/env python3
"""
Set File Generator
==================

Specialized generator for MT5 .set files with advanced features.
Creates properly formatted .set files that can be imported directly into MT5.

Features:
- MT5 .set file format compliance
- Parameter grouping and organization
- Comment generation with optimization details
- Multiple .set file templates
- Validation against MT5 requirements
- Batch generation for multiple timeframes/symbols

This module ensures seamless integration between Python optimization
results and MT5 Expert Advisor parameters.
"""

import re
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import warnings

class SetFileGenerator:
    """
    Advanced .set file generator for MT5 Expert Advisors
    
    Creates properly formatted .set files with comprehensive
    parameter organization and documentation.
    """
    
    def __init__(self, config: dict = None):
        """
        Initialize .set file generator
        
        Args:
            config: Generator configuration
        """
        self.config = config or self._get_default_config()
        self.templates = self._load_templates()
        
        print(f"📝 Set File Generator initialized")
    
    def _get_default_config(self) -> dict:
        """Get default generator configuration"""
        return {
            # File settings
            'output_directory': 'mt5_sets',
            'file_encoding': 'utf-8',
            'line_ending': '\\n',
            
            # Format settings
            'include_header': True,
            'include_categories': True,
            'include_descriptions': True,
            'include_optimization_info': True,
            'include_performance_metrics': True,
            
            # Parameter formatting
            'bool_format': 'true/false',  # or '1/0'
            'decimal_places': 6,
            'scientific_notation': False,
            
            # Organization
            'group_by_category': True,
            'sort_within_category': True,
            'separate_advanced_params': True,
        }
    
    def _load_templates(self) -> Dict[str, str]:
        """Load .set file templates"""
        return {
            'standard': self._get_standard_template(),
            'minimal': self._get_minimal_template(),
            'detailed': self._get_detailed_template(),
            'optimization': self._get_optimization_template()
        }
    
    # =========================================================================
    # MAIN GENERATION INTERFACE
    # =========================================================================
    
    def generate_set_file(self, parameters: Dict[str, Any],
                         ea_name: str = 'UltraConservativeEA',
                         symbol: str = 'EURUSD',
                         timeframe: str = 'M15',
                         template: str = 'standard',
                         optimization_info: Dict = None,
                         performance_metrics: Dict = None,
                         custom_comments: List[str] = None) -> str:
        """
        Generate MT5 .set file
        
        Args:
            parameters: Dictionary of EA parameters
            ea_name: Expert Advisor name
            symbol: Trading symbol
            timeframe: Chart timeframe
            template: Template type ('standard', 'minimal', 'detailed', 'optimization')
            optimization_info: Optimization process information
            performance_metrics: Backtest performance metrics
            custom_comments: Additional custom comments
        
        Returns:
            str: Generated .set file content
        """
        print(f"📝 Generating .set file for {ea_name}...")
        
        # Validate parameters
        validation_result = self._validate_set_parameters(parameters)
        if not validation_result['valid']:
            print("⚠️ Parameter validation warnings:")
            for warning in validation_result['warnings']:
                print(f"   - {warning}")
        
        # Select template
        if template not in self.templates:
            print(f"⚠️ Template '{template}' not found, using 'standard'")
            template = 'standard'
        
        # Generate file content
        content_parts = []
        
        # Header section
        if self.config['include_header']:
            header = self._generate_header(
                ea_name, symbol, timeframe, optimization_info, 
                performance_metrics, custom_comments
            )
            content_parts.append(header)
        
        # Parameters section
        parameters_section = self._generate_parameters_section(
            parameters, template
        )
        content_parts.append(parameters_section)
        
        # Footer (if needed)
        footer = self._generate_footer()
        if footer:
            content_parts.append(footer)
        
        # Combine all parts
        content = self.config['line_ending'].join(content_parts)
        
        print(f"✅ .set file generated successfully")
        print(f"📊 Parameters: {len(parameters)}")
        
        return content
    
    def save_set_file(self, content: str, filename: str = None,
                     ea_name: str = 'UltraConservativeEA',
                     symbol: str = 'EURUSD',
                     timeframe: str = 'M15') -> Path:
        """
        Save .set file to disk
        
        Args:
            content: .set file content
            filename: Custom filename (optional)
            ea_name: EA name for auto-generated filename
            symbol: Symbol for auto-generated filename
            timeframe: Timeframe for auto-generated filename
        
        Returns:
            Path: Path to saved file
        """
        # Create output directory
        output_dir = Path(self.config['output_directory'])
        output_dir.mkdir(exist_ok=True)
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{ea_name}_{symbol}_{timeframe}_{timestamp}.set"
        
        # Ensure .set extension
        if not filename.endswith('.set'):
            filename += '.set'
        
        file_path = output_dir / filename
        
        # Save file
        with open(file_path, 'w', encoding=self.config['file_encoding']) as f:
            f.write(content)
        
        print(f"💾 .set file saved: {file_path}")
        return file_path
    
    def generate_multiple_set_files(self, parameters_list: List[Dict],
                                   symbols: List[str] = None,
                                   timeframes: List[str] = None,
                                   ea_name: str = 'UltraConservativeEA') -> List[Path]:
        """
        Generate multiple .set files for different configurations
        
        Args:
            parameters_list: List of parameter dictionaries
            symbols: List of symbols (optional)
            timeframes: List of timeframes (optional)
            ea_name: EA name
        
        Returns:
            List[Path]: Paths to generated files
        """
        generated_files = []
        
        symbols = symbols or ['EURUSD']
        timeframes = timeframes or ['M15']
        
        for i, params in enumerate(parameters_list):
            for symbol in symbols:
                for timeframe in timeframes:
                    # Generate content
                    content = self.generate_set_file(
                        parameters=params,
                        ea_name=ea_name,
                        symbol=symbol,
                        timeframe=timeframe,
                        template='standard'
                    )
                    
                    # Save file
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{ea_name}_{symbol}_{timeframe}_config{i+1}_{timestamp}.set"
                    file_path = self.save_set_file(content, filename)
                    generated_files.append(file_path)
        
        print(f"📁 Generated {len(generated_files)} .set files")
        return generated_files
    
    # =========================================================================
    # CONTENT GENERATION
    # =========================================================================
    
    def _generate_header(self, ea_name: str, symbol: str, timeframe: str,
                        optimization_info: Dict = None,
                        performance_metrics: Dict = None,
                        custom_comments: List[str] = None) -> str:
        """Generate file header with metadata"""
        header_lines = []
        
        # Main header
        header_lines.append(";")
        header_lines.append(f"; {ea_name} - Expert Advisor Parameters")
        header_lines.append(";")
        header_lines.append(f"; Symbol: {symbol}")
        header_lines.append(f"; Timeframe: {timeframe}")
        header_lines.append(f"; Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        header_lines.append(";")
        
        # Optimization information
        if self.config['include_optimization_info'] and optimization_info:
            header_lines.append("; OPTIMIZATION INFORMATION")
            header_lines.append("; " + "=" * 30)
            
            for key, value in optimization_info.items():
                formatted_key = key.replace('_', ' ').title()
                header_lines.append(f"; {formatted_key}: {value}")
            
            header_lines.append(";")
        
        # Performance metrics
        if self.config['include_performance_metrics'] and performance_metrics:
            header_lines.append("; PERFORMANCE METRICS")
            header_lines.append("; " + "=" * 25)
            
            # Format key metrics
            key_metrics = [
                'total_return', 'max_drawdown', 'sharpe_ratio', 
                'win_rate', 'profit_factor', 'total_trades'
            ]
            
            for metric in key_metrics:
                if metric in performance_metrics:
                    value = performance_metrics[metric]
                    formatted_key = metric.replace('_', ' ').title()
                    
                    if isinstance(value, float):
                        if 'percent' in metric or 'rate' in metric or 'return' in metric or 'drawdown' in metric:
                            header_lines.append(f"; {formatted_key}: {value:.2f}%")
                        else:
                            header_lines.append(f"; {formatted_key}: {value:.2f}")
                    else:
                        header_lines.append(f"; {formatted_key}: {value}")
            
            header_lines.append(";")
        
        # Custom comments
        if custom_comments:
            header_lines.append("; ADDITIONAL NOTES")
            header_lines.append("; " + "=" * 20)
            
            for comment in custom_comments:
                header_lines.append(f"; {comment}")
            
            header_lines.append(";")
        
        # Usage instructions
        header_lines.append("; USAGE INSTRUCTIONS")
        header_lines.append("; " + "=" * 22)
        header_lines.append("; 1. Copy this file to MT5/Profiles/Templates/")
        header_lines.append("; 2. In MT5 Strategy Tester, click 'Load' and select this file")
        header_lines.append("; 3. Verify all parameters match your EA")
        header_lines.append("; 4. Run backtest or start optimization")
        header_lines.append(";")
        header_lines.append("")
        
        return self.config['line_ending'].join(header_lines)
    
    def _generate_parameters_section(self, parameters: Dict[str, Any], 
                                   template: str) -> str:
        """Generate parameters section"""
        if self.config['group_by_category']:
            return self._generate_categorized_parameters(parameters, template)
        else:
            return self._generate_flat_parameters(parameters, template)
    
    def _generate_categorized_parameters(self, parameters: Dict[str, Any], 
                                       template: str) -> str:
        """Generate parameters grouped by category"""
        # Define parameter categories
        categories = {
            'Basic Settings': [
                'MACD_Fast', 'MACD_Slow', 'MACD_Signal', 'BB_Period', 'BB_Deviation',
                'EMA_Period', 'RSI_Period', 'ATR_Period', 'ADX_Trend'
            ],
            'Risk Management': [
                'MaxRiskPercent', 'TakeProfitRatio', 'StopLossBuffer', 'TrailingStop',
                'BreakEvenLevel', 'MaxConcurrentTrades', 'MaxPortfolioHeat'
            ],
            'Filters': [
                'MinBBWidthPips', 'UseATRFilter', 'MinATRMultiplier', 'MaxATRMultiplier',
                'AvoidNews', 'NewsBufferMinutes'
            ],
            'Time Settings': [
                'StartHour', 'EndHour', 'TradeOnlyOverlap', 'ReduceRiskFriday',
                'FridayRiskReduction'
            ],
            'Advanced Features': [
                'UseAdaptiveRisk', 'MaxRiskMultiplier', 'MinRiskMultiplier',
                'ConsecutiveWinBonus', 'ConsecutiveLossReduction', 'UsePartialTP',
                'PartialTP1_RR', 'PartialTP1_Percent', 'PartialTP2_RR', 'PartialTP2_Percent',
                'UseATRTrailing', 'ATRTrailingMultiplier', 'UseVolatilityAdjustment'
            ]
        }
        
        sections = []
        
        for category_name, param_names in categories.items():
            category_params = {}
            
            # Collect parameters for this category
            for param_name in param_names:
                if param_name in parameters:
                    category_params[param_name] = parameters[param_name]
            
            # Skip empty categories
            if not category_params:
                continue
            
            # Generate category section
            section_lines = []
            section_lines.append(f"; {category_name}")
            section_lines.append("; " + "=" * len(category_name))
            
            # Sort parameters within category if requested
            param_items = list(category_params.items())
            if self.config['sort_within_category']:
                param_items.sort(key=lambda x: x[0])
            
            for param_name, param_value in param_items:
                formatted_line = self._format_parameter_line(param_name, param_value)
                section_lines.append(formatted_line)
            
            section_lines.append("")
            sections.append(self.config['line_ending'].join(section_lines))
        
        # Add any uncategorized parameters
        uncategorized = {}
        all_categorized = set()
        for param_names in categories.values():
            all_categorized.update(param_names)
        
        for param_name, param_value in parameters.items():
            if param_name not in all_categorized:
                uncategorized[param_name] = param_value
        
        if uncategorized:
            section_lines = []
            section_lines.append("; Other Parameters")
            section_lines.append("; " + "=" * 17)
            
            for param_name, param_value in sorted(uncategorized.items()):
                formatted_line = self._format_parameter_line(param_name, param_value)
                section_lines.append(formatted_line)
            
            sections.append(self.config['line_ending'].join(section_lines))
        
        return self.config['line_ending'].join(sections)
    
    def _generate_flat_parameters(self, parameters: Dict[str, Any], 
                                 template: str) -> str:
        """Generate parameters in flat format"""
        lines = []
        
        # Sort parameters alphabetically
        sorted_params = sorted(parameters.items())
        
        for param_name, param_value in sorted_params:
            formatted_line = self._format_parameter_line(param_name, param_value)
            lines.append(formatted_line)
        
        return self.config['line_ending'].join(lines)
    
    def _format_parameter_line(self, param_name: str, param_value: Any) -> str:
        """Format a single parameter line"""
        # Format value based on type
        if isinstance(param_value, bool):
            if self.config['bool_format'] == 'true/false':
                formatted_value = 'true' if param_value else 'false'
            else:
                formatted_value = '1' if param_value else '0'
        elif isinstance(param_value, int):
            formatted_value = str(param_value)
        elif isinstance(param_value, float):
            if self.config['scientific_notation'] and abs(param_value) < 0.001:
                formatted_value = f"{param_value:.{self.config['decimal_places']}e}"
            else:
                formatted_value = f"{param_value:.{self.config['decimal_places']}f}".rstrip('0').rstrip('.')
        else:
            formatted_value = str(param_value)
        
        return f"{param_name}={formatted_value}"
    
    def _generate_footer(self) -> Optional[str]:
        """Generate file footer if needed"""
        # For now, no footer is needed for .set files
        return None
    
    # =========================================================================
    # VALIDATION
    # =========================================================================
    
    def _validate_set_parameters(self, parameters: Dict[str, Any]) -> Dict:
        """Validate parameters for .set file compatibility"""
        warnings_list = []
        valid = True
        
        # Check parameter names
        for param_name in parameters.keys():
            # Parameter names should not contain spaces or special characters
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', param_name):
                warnings_list.append(f"Parameter name '{param_name}' contains invalid characters")
            
            # Parameter names should not be too long
            if len(param_name) > 50:
                warnings_list.append(f"Parameter name '{param_name}' is very long ({len(param_name)} chars)")
        
        # Check for common MT5 parameter naming conventions
        expected_prefixes = ['MACD_', 'BB_', 'RSI_', 'ATR_', 'EMA_', 'ADX_', 'Max', 'Min', 'Use']
        unconventional_names = []
        
        for param_name in parameters.keys():
            has_expected_prefix = any(param_name.startswith(prefix) for prefix in expected_prefixes)
            if not has_expected_prefix and '_' not in param_name:
                unconventional_names.append(param_name)
        
        if unconventional_names:
            warnings_list.append(f"Unconventional parameter names: {', '.join(unconventional_names)}")
        
        return {
            'valid': valid,
            'warnings': warnings_list
        }
    
    # =========================================================================
    # TEMPLATES
    # =========================================================================
    
    def _get_standard_template(self) -> str:
        """Standard template with all sections"""
        return "standard"
    
    def _get_minimal_template(self) -> str:
        """Minimal template with just parameters"""
        return "minimal"
    
    def _get_detailed_template(self) -> str:
        """Detailed template with extensive documentation"""
        return "detailed"
    
    def _get_optimization_template(self) -> str:
        """Template optimized for optimization process"""
        return "optimization"
    
    # =========================================================================
    # UTILITY METHODS
    # =========================================================================
    
    def preview_set_file(self, parameters: Dict[str, Any], **kwargs) -> str:
        """Generate and return preview of .set file content"""
        content = self.generate_set_file(parameters, **kwargs)
        
        # Show first 20 lines as preview
        lines = content.split(self.config['line_ending'])
        preview_lines = lines[:20]
        
        if len(lines) > 20:
            preview_lines.append("...")
            preview_lines.append(f"[{len(lines)-20} more lines]")
        
        return self.config['line_ending'].join(preview_lines)
    
    def get_parameter_count(self, parameters: Dict[str, Any]) -> Dict[str, int]:
        """Get count of parameters by type"""
        counts = {
            'total': len(parameters),
            'boolean': 0,
            'integer': 0,
            'float': 0,
            'string': 0
        }
        
        for value in parameters.values():
            if isinstance(value, bool):
                counts['boolean'] += 1
            elif isinstance(value, int):
                counts['integer'] += 1
            elif isinstance(value, float):
                counts['float'] += 1
            else:
                counts['string'] += 1
        
        return counts


def main():
    """Example usage of SetFileGenerator"""
    print("📝 Set File Generator Example")
    print("=============================")
    
    # Create generator
    generator = SetFileGenerator()
    
    # Example parameters
    example_params = {
        'MACD_Fast': 12,
        'MACD_Slow': 26,
        'MACD_Signal': 9,
        'BB_Period': 20,
        'BB_Deviation': 2.0,
        'MaxRiskPercent': 1.0,
        'TakeProfitRatio': 2.0,
        'UseAdaptiveRisk': True,
        'MaxConcurrentTrades': 3
    }
    
    # Example optimization info
    opt_info = {
        'method': 'Bayesian Optimization',
        'trials': 200,
        'best_value': 2.45
    }
    
    # Example performance metrics
    performance = {
        'total_return': 25.5,
        'max_drawdown': 8.2,
        'sharpe_ratio': 1.85,
        'win_rate': 65.2,
        'profit_factor': 2.1,
        'total_trades': 48
    }
    
    print("✅ This is an example of the SetFileGenerator")
    print("🔄 To use in practice:")
    print("   1. Pass MT5 parameters to generate_set_file()")
    print("   2. Include optimization info and performance metrics")
    print("   3. Save the generated .set file")
    print("   4. Import into MT5 Strategy Tester")
    
    # Generate preview
    preview = generator.preview_set_file(
        parameters=example_params,
        optimization_info=opt_info,
        performance_metrics=performance
    )
    
    print(f"\\n📋 Generated .set file preview:")
    print("-" * 40)
    print(preview)


if __name__ == "__main__":
    main()