#!/usr/bin/env python3
"""
Test Ultra Backtesting System Installation
==========================================

Simple test script to verify that all components are working correctly.
"""

import sys
from pathlib import Path

# Add the current directory to the path for imports
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all modules can be imported"""
    print("🧪 Testing module imports...")
    
    try:
        from data.data_loader import DataLoader
        print("✅ DataLoader import successful")
    except ImportError as e:
        print(f"❌ DataLoader import failed: {e}")
        return False
    
    try:
        from indicators.technical_indicators import TechnicalIndicators
        print("✅ TechnicalIndicators import successful")
    except ImportError as e:
        print(f"❌ TechnicalIndicators import failed: {e}")
        return False
    
    try:
        from strategy.ultra_strategy import UltraStrategy
        print("✅ UltraStrategy import successful")
    except ImportError as e:
        print(f"❌ UltraStrategy import failed: {e}")
        return False
    
    try:
        from backtesting.backtest_engine import BacktestEngine
        print("✅ BacktestEngine import successful")
    except ImportError as e:
        print(f"❌ BacktestEngine import failed: {e}")
        return False
    
    try:
        from optimization.optimizer import ParameterOptimizer
        print("✅ ParameterOptimizer import successful")
    except ImportError as e:
        print(f"❌ ParameterOptimizer import failed: {e}")
        return False
    
    try:
        from reporting.performance_analyzer import PerformanceAnalyzer
        print("✅ PerformanceAnalyzer import successful")
    except ImportError as e:
        print(f"❌ PerformanceAnalyzer import failed: {e}")
        return False
    
    try:
        from mt5_export.parameter_exporter import ParameterExporter
        print("✅ ParameterExporter import successful")
    except ImportError as e:
        print(f"❌ ParameterExporter import failed: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic component initialization"""
    print("\n🔧 Testing basic functionality...")
    
    try:
        from strategy.ultra_strategy import UltraStrategy
        from backtesting.backtest_engine import BacktestEngine
        
        # Test strategy initialization
        strategy = UltraStrategy()
        print(f"✅ Strategy initialized with {len(strategy.config)} parameters")
        print(f"   - Max risk: {strategy.config['max_risk_percent']}%")
        print(f"   - Take profit ratio: {strategy.config['take_profit_ratio']}")
        print(f"   - Adaptive risk: {strategy.config['use_adaptive_risk']}")
        
        # Test backtesting engine
        engine = BacktestEngine(initial_balance=1000.0)
        print(f"✅ Backtesting engine initialized")
        print(f"   - Initial balance: ${engine.balance:,.2f}")
        print(f"   - Spread: {engine.config['spread_pips']} pips")
        print(f"   - Commission: ${engine.config['commission_per_lot']} per lot")
        
        return True
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def test_dependencies():
    """Test that required dependencies are available"""
    print("\n📦 Testing dependencies...")
    
    required_packages = [
        'pandas', 'numpy', 'scipy', 'optuna'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} available")
        except ImportError:
            print(f"❌ {package} missing - install with: pip install {package}")
            return False
    
    return True

def main():
    """Run all tests"""
    print("🚀 Ultra Backtesting System - Installation Test")
    print("=" * 60)
    
    # Test dependencies first
    deps_ok = test_dependencies()
    
    # Test imports
    imports_ok = test_imports()
    
    # Test basic functionality
    functionality_ok = test_basic_functionality()
    
    print("\n" + "=" * 60)
    
    if deps_ok and imports_ok and functionality_ok:
        print("🎉 Installation test PASSED!")
        print("✅ Ultra Backtesting System is ready for use")
        print("\n📋 Next steps:")
        print("   1. Run: python example_usage.py")
        print("   2. Review the README.md for detailed usage")
        print("   3. Set up MT5 data export for your trading data")
    else:
        print("❌ Installation test FAILED!")
        print("\n🔧 Troubleshooting:")
        
        if not deps_ok:
            print("   - Install missing dependencies: pip install -r requirements.txt")
        
        if not imports_ok:
            print("   - Check that all module files are present")
            print("   - Verify you're running from the correct directory")
        
        if not functionality_ok:
            print("   - Check for any syntax errors in the code")
            print("   - Ensure Python version is 3.8 or higher")

if __name__ == "__main__":
    main()