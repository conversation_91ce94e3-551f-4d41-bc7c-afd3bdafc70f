"""
Ultra Backtesting System
========================

A comprehensive Python backtesting and optimization system for the Ultra Conservative EA.
This system is designed to work with exported MT5 data and operates without any 
MetaTrader dependencies at runtime.

Features:
- Pure Python backtesting engine
- Advanced technical indicators
- Portfolio risk management
- Parameter optimization with Optuna
- Walk-forward analysis
- Professional reporting

Author: Claude
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Claude"

# Import main components for easy access
from .data.data_loader import DataLoader
from .indicators.technical_indicators import TechnicalIndicators
from .strategy.ultra_strategy import UltraStrategy
from .backtesting.backtest_engine import BacktestEngine
from .optimization.optimizer import ParameterOptimizer
from .optimization.walk_forward import WalkForwardAnalyzer
from .reporting.performance_analyzer import PerformanceAnalyzer
from .reporting.validation import ResultsValidator
from .reporting.report_generator import ReportGenerator
from .mt5_export.parameter_exporter import ParameterExporter
from .mt5_export.set_file_generator import SetFileGenerator

__all__ = [
    'DataLoader',
    'TechnicalIndicators', 
    'UltraStrategy',
    'BacktestEngine',
    'ParameterOptimizer',
    'WalkForwardAnalyzer',
    'PerformanceAnalyzer',
    'ResultsValidator',
    'ReportGenerator',
    'ParameterExporter',
    'SetFileGenerator'
]