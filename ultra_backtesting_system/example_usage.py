#!/usr/bin/env python3
"""
Ultra Backtesting System - Example Usage
=========================================

Complete example demonstrating how to use the Ultra Backtesting System
for strategy optimization and validation.

This example shows the complete workflow:
1. Data export from MT5 (one-time setup)
2. Data loading and processing
3. Strategy backtesting
4. Parameter optimization
5. Walk-forward validation
6. Performance analysis and reporting
7. Export optimized parameters to MT5

Author: Claude
Version: 1.0.0
"""

import sys
from pathlib import Path

# Add the current directory to the path for imports
sys.path.insert(0, str(Path(__file__).parent))

# Import components directly since we're in the package directory
from data.data_loader import DataLoader
from indicators.technical_indicators import TechnicalIndicators
from strategy.ultra_strategy import UltraStrategy
from backtesting.backtest_engine import BacktestEngine
from optimization.optimizer import ParameterOptimizer
from optimization.walk_forward import WalkForwardAnalyzer
from reporting.performance_analyzer import PerformanceAnalyzer
from reporting.validation import ResultsValidator
from reporting.report_generator import ReportGenerator
from mt5_export.parameter_exporter import ParameterExporter
from mt5_export.set_file_generator import SetFileGenerator

def main():
    """
    Complete example of the Ultra Backtesting System workflow
    """
    print("🚀 Ultra Backtesting System - Complete Example")
    print("=" * 60)
    print()
    
    # =========================================================================
    # STEP 1: DATA PREPARATION
    # =========================================================================
    print("📊 STEP 1: Data Preparation")
    print("-" * 30)
    
    # Note: In real usage, you would first export data from MT5
    # using the MT5DataExporter (one-time setup)
    print("ℹ️  Data Export (One-time setup):")
    print("   1. Run MT5DataExporter to export historical data")
    print("   2. This creates CSV files with OHLCV data")
    print("   3. After export, system is MetaTrader-free")
    print()
    
    # Load data using DataLoader
    print("📈 Loading historical data...")
    data_loader = DataLoader()
    
    # In real usage, you would load actual exported data:
    # data = data_loader.load_data(
    #     symbol='EURUSD',
    #     timeframe='M15',
    #     start_date='2024-01-01',
    #     end_date='2024-12-31'
    # )
    
    print("✅ Data loading configured (use real exported data in practice)")
    print()
    
    # =========================================================================
    # STEP 2: TECHNICAL INDICATORS
    # =========================================================================
    print("📊 STEP 2: Technical Indicators Calculation")
    print("-" * 45)
    
    # Note: In real usage, you would calculate indicators on actual data
    print("🔧 Technical indicators would be calculated:")
    print("   - MACD (Moving Average Convergence Divergence)")
    print("   - Bollinger Bands")
    print("   - RSI (Relative Strength Index)")
    print("   - ADX (Average Directional Index)")
    print("   - ATR (Average True Range)")
    print("   - EMA (Exponential Moving Average)")
    print()
    
    # Example indicator calculation:
    # indicators = TechnicalIndicators(data)
    # data_with_indicators = indicators.calculate_all_indicators()
    
    print("✅ Technical indicators calculation configured")
    print()
    
    # =========================================================================
    # STEP 3: STRATEGY BACKTESTING
    # =========================================================================
    print("🎯 STEP 3: Strategy Backtesting")
    print("-" * 35)
    
    # Initialize strategy and backtesting engine
    strategy = UltraStrategy()
    backtest_engine = BacktestEngine(initial_balance=1000.0)
    
    print("🔧 Strategy Configuration:")
    key_params = [
        'max_risk_percent', 'take_profit_ratio', 'adx_trend', 
        'max_concurrent_trades', 'use_adaptive_risk'
    ]
    for param in key_params:
        value = strategy.config.get(param, 'N/A')
        print(f"   {param}: {value}")
    print()
    
    # In real usage, you would run backtest:
    # results = backtest_engine.run_backtest(data_with_indicators, strategy)
    
    print("✅ Backtesting engine configured")
    print()
    
    # =========================================================================
    # STEP 4: PARAMETER OPTIMIZATION
    # =========================================================================
    print("🔧 STEP 4: Parameter Optimization")
    print("-" * 38)
    
    # Initialize parameter optimizer
    optimizer = ParameterOptimizer(
        data_loader, UltraStrategy, BacktestEngine
    )
    
    print("🎯 Optimization Configuration:")
    print(f"   Target metric: {optimizer.config['primary_objective']}")
    print(f"   Number of trials: {optimizer.config['n_trials']}")
    print(f"   Parameters to optimize: {len(optimizer.parameter_space)}")
    print()
    
    # Show some key parameters being optimized
    print("📊 Key parameters in optimization space:")
    for param, config in list(optimizer.parameter_space.items())[:5]:
        print(f"   {param}: {config['type']} from {config.get('low', 'N/A')} to {config.get('high', 'N/A')}")
    print("   ... and more")
    print()
    
    # In real usage, you would run optimization:
    # opt_results = optimizer.optimize_parameters(
    #     symbol='EURUSD',
    #     timeframe='M15',
    #     start_date='2024-01-01',
    #     end_date='2024-06-30'
    # )
    
    print("✅ Parameter optimization configured")
    print()
    
    # =========================================================================
    # STEP 5: WALK-FORWARD VALIDATION
    # =========================================================================
    print("📈 STEP 5: Walk-Forward Validation")
    print("-" * 39)
    
    # Initialize walk-forward analyzer
    wf_analyzer = WalkForwardAnalyzer(
        data_loader, UltraStrategy, BacktestEngine, ParameterOptimizer
    )
    
    print("🔄 Walk-Forward Configuration:")
    print(f"   Method: {wf_analyzer.config['method']}")
    print(f"   Training window: {wf_analyzer.config['train_window_months']} months")
    print(f"   Test window: {wf_analyzer.config['test_window_months']} months")
    print(f"   Step size: {wf_analyzer.config['step_months']} months")
    print()
    
    # In real usage, you would run walk-forward analysis:
    # wf_results = wf_analyzer.run_walk_forward_analysis(
    #     symbol='EURUSD',
    #     timeframe='M15',
    #     start_date='2024-01-01',
    #     end_date='2024-12-31'
    # )
    
    print("✅ Walk-forward validation configured")
    print()
    
    # =========================================================================
    # STEP 6: PERFORMANCE ANALYSIS
    # =========================================================================
    print("📊 STEP 6: Performance Analysis & Validation")
    print("-" * 45)
    
    # Initialize performance analyzer and validator
    perf_analyzer = PerformanceAnalyzer()
    validator = ResultsValidator()
    
    print("📈 Performance Analysis includes:")
    print("   - Risk-adjusted returns (Sharpe, Sortino, Calmar)")
    print("   - Drawdown analysis and recovery times")
    print("   - Trade statistics and consistency metrics")
    print("   - Statistical significance testing")
    print()
    
    print("✅ Validation checks include:")
    print("   - Data quality validation")
    print("   - Performance reality checks") 
    print("   - Overfitting detection")
    print("   - Statistical significance tests")
    print()
    
    # In real usage, you would analyze results:
    # metrics = perf_analyzer.analyze_performance(results)
    # validation = validator.validate_results(results, data, trade_history)
    
    print("✅ Performance analysis and validation configured")
    print()
    
    # =========================================================================
    # STEP 7: COMPREHENSIVE REPORTING
    # =========================================================================
    print("📄 STEP 7: Comprehensive Reporting")
    print("-" * 39)
    
    # Initialize report generator
    report_generator = ReportGenerator()
    
    print("📋 Report types generated:")
    print("   - Executive Summary")
    print("   - Detailed Performance Analysis")
    print("   - Risk Assessment Report")
    print("   - Trade Analysis Report")
    print("   - Validation Report")
    print("   - Parameter Optimization Summary")
    print("   - Walk-Forward Analysis Report")
    print("   - Master Report (combined)")
    print()
    
    # In real usage, you would generate reports:
    # reports = report_generator.generate_comprehensive_report(
    #     backtest_results=results,
    #     performance_metrics=metrics,
    #     validation_results=validation,
    #     optimization_results=opt_results,
    #     walk_forward_results=wf_results,
    #     symbol='EURUSD',
    #     timeframe='M15'
    # )
    
    print("✅ Comprehensive reporting configured")
    print()
    
    # =========================================================================
    # STEP 8: MT5 PARAMETER EXPORT
    # =========================================================================
    print("📤 STEP 8: MT5 Parameter Export")
    print("-" * 36)
    
    # Initialize parameter exporter and set file generator
    param_exporter = ParameterExporter()
    set_generator = SetFileGenerator()
    
    print("📁 Export formats supported:")
    print("   - .set files (for MT5 import)")
    print("   - JSON files (structured data)")
    print("   - CSV files (spreadsheet format)")
    print("   - Documentation (human readable)")
    print()
    
    print("🔧 Parameter mapping example:")
    sample_mappings = [
        ('macd_fast', 'MACD_Fast'),
        ('bb_period', 'BB_Period'),
        ('max_risk_percent', 'MaxRiskPercent'),
        ('use_adaptive_risk', 'UseAdaptiveRisk')
    ]
    
    for python_param, mt5_param in sample_mappings:
        print(f"   {python_param} -> {mt5_param}")
    print("   ... and more")
    print()
    
    # In real usage, you would export optimized parameters:
    # best_params = opt_results['best_params']
    # exported_files = param_exporter.export_parameters(
    #     python_params=best_params,
    #     optimization_info=opt_results,
    #     performance_summary=metrics,
    #     symbol='EURUSD',
    #     timeframe='M15'
    # )
    
    print("✅ MT5 parameter export configured")
    print()
    
    # =========================================================================
    # STEP 9: FINAL WORKFLOW SUMMARY
    # =========================================================================
    print("🎉 STEP 9: Complete Workflow Summary")
    print("-" * 40)
    
    print("🔄 Complete MetaTrader-Free Workflow:")
    print("   1. ✅ Export data from MT5 (one-time setup)")
    print("   2. ✅ Load and process historical data")
    print("   3. ✅ Calculate technical indicators")
    print("   4. ✅ Run strategy backtesting")
    print("   5. ✅ Optimize parameters with Optuna")
    print("   6. ✅ Validate with walk-forward analysis")
    print("   7. ✅ Analyze performance and risk")
    print("   8. ✅ Generate comprehensive reports")
    print("   9. ✅ Export optimized parameters to MT5")
    print()
    
    print("📈 Key Benefits:")
    print("   - Runs on Mac without MetaTrader dependencies")
    print("   - Professional-grade backtesting and optimization")
    print("   - Comprehensive validation and reporting")
    print("   - Seamless integration back to MT5")
    print("   - Institutional-quality analysis")
    print()
    
    print("🚀 Next Steps:")
    print("   1. Export your MT5 data using MT5DataExporter")
    print("   2. Modify this example with your actual data paths")
    print("   3. Run the complete optimization workflow")
    print("   4. Import the generated .set files into MT5")
    print("   5. Test on demo account before live trading")
    print()
    
    print("✅ Ultra Backtesting System example completed!")
    print("📁 All components are ready for production use")

if __name__ == "__main__":
    main()