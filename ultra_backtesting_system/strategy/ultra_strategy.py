#!/usr/bin/env python3
"""
Ultra Conservative Strategy
===========================

Pure Python port of the ultra.mq5 Expert Advisor logic.
Implements all strategy components:
- Slingshot strategy (trending markets)
- Divergence strategy (ranging markets)
- Advanced risk management
- Portfolio heat management
- Adaptive risk scaling
- Enhanced position management

This implementation matches the MQL5 version exactly.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings

class UltraStrategy:
    """
    Ultra Conservative Strategy Implementation
    
    Ports the complete ultra.mq5 logic to Python including:
    - Signal generation (Slingshot & Divergence)
    - Risk management
    - Position sizing
    - Portfolio management
    """
    
    def __init__(self, config: dict = None):
        """
        Initialize Ultra Strategy
        
        Args:
            config: Strategy configuration parameters
        """
        # Default configuration matching ultra.mq5
        self.config = config or self._get_default_config()
        
        # Strategy state
        self.positions = {}  # Active positions
        self.trade_history = []  # Completed trades
        self.balance = self.config['initial_balance']
        self.equity = self.balance
        
        # Risk management state
        self.consecutive_wins = 0
        self.consecutive_losses = 0
        self.current_risk_multiplier = 1.0
        self.portfolio_heat = 0.0
        self.currency_exposure = {}
        
        # Signal state
        self.bullish_divergence_setup = False
        self.bearish_divergence_setup = False
        self.pending_signal = 0
        self.signal_bar_time = None
        self.pending_strategy = ""
        
        # Performance tracking
        self.daily_stats = {}
        self.max_equity = self.balance
        self.max_drawdown = 0.0
        
        print(f"🎯 Ultra Conservative Strategy initialized")
        print(f"💰 Initial balance: ${self.balance:,.2f}")
    
    def _get_default_config(self) -> dict:
        """Get default configuration matching ultra.mq5 parameters"""
        return {
            # Account settings
            'initial_balance': 1000.0,
            'leverage': 100,
            
            # MACD settings
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            
            # Bollinger Bands
            'bb_period': 20,
            'bb_deviation': 2.0,
            'min_bb_width_pips': 15.0,
            
            # Strategy filters
            'ema_period': 200,
            'adx_period': 14,
            'adx_trend': 25.0,
            'rsi_period': 14,
            
            # Position management
            'max_risk_percent': 1.0,
            'take_profit_ratio': 2.0,
            'stop_loss_buffer': 10.0,
            'trailing_stop': 20.0,
            'break_even_level': 1.5,
            
            # Trading hours
            'start_hour': 7,
            'end_hour': 20,
            'trade_only_overlap': False,
            
            # Portfolio risk management
            'max_concurrent_trades': 3,
            'max_portfolio_heat': 6.0,
            'max_currency_exposure': 40.0,
            'correlation_limit': 0.7,
            
            # Adaptive risk
            'use_adaptive_risk': True,
            'max_risk_multiplier': 2.0,
            'min_risk_multiplier': 0.3,
            'consecutive_win_bonus': 3,
            'consecutive_loss_reduction': 2,
            
            # Enhanced position management
            'use_partial_tp': True,
            'partial_tp1_rr': 1.0,
            'partial_tp1_percent': 40.0,
            'partial_tp2_rr': 2.0,
            'partial_tp2_percent': 30.0,
            'use_atr_trailing': True,
            'atr_trailing_multiplier': 1.5,
            
            # Volatility enhancement
            'use_atr_filter': True,
            'atr_period': 14,
            'min_atr_multiplier': 0.8,
            'max_atr_multiplier': 3.0,
            'use_volatility_adjustment': True,
            
            # News/event management
            'avoid_news': True,
            'news_buffer_minutes': 30,
            'reduce_risk_friday': True,
            'friday_risk_reduction': 0.5
        }
    
    # =========================================================================
    # SIGNAL GENERATION
    # =========================================================================
    
    def get_trading_signal(self, df: pd.DataFrame, bar_index: int) -> Dict:
        """
        Main signal generation function (matches ultra.mq5 GetTradingSignal)
        
        Args:
            df: DataFrame with price and indicator data
            bar_index: Current bar index
        
        Returns:
            dict: Signal information
        """
        if bar_index < 100:  # Need sufficient data for indicators
            return {'signal': 0, 'strategy': '', 'reason': 'insufficient_data'}
        
        # Get current bar data
        current_bar = df.iloc[bar_index]
        
        # Check volatility filter
        if not self._is_volatility_acceptable(df, bar_index):
            return {'signal': 0, 'strategy': '', 'reason': 'volatility_filter'}
        
        # Check trading hours
        if not self._is_valid_trading_time(current_bar['datetime']):
            return {'signal': 0, 'strategy': '', 'reason': 'trading_hours'}
        
        # Check if safe to trade (news filter)
        if not self._is_safe_to_trade(current_bar['datetime']):
            return {'signal': 0, 'strategy': '', 'reason': 'news_filter'}
        
        # Market regime detection (ADX-based)
        adx_value = current_bar['adx']
        
        if adx_value >= self.config['adx_trend']:  # Trending market
            return self._detect_slingshot_signal(df, bar_index)
        else:  # Ranging market
            return self._detect_divergence_signal(df, bar_index)
    
    def _detect_slingshot_signal(self, df: pd.DataFrame, bar_index: int) -> Dict:
        """
        Detect Slingshot trend continuation signal
        
        Args:
            df: DataFrame with indicators
            bar_index: Current bar index
        
        Returns:
            dict: Signal information
        """
        current_bar = df.iloc[bar_index]
        prev_bar = df.iloc[bar_index - 1] if bar_index > 0 else current_bar
        
        # MACD crossover detection
        macd_main = current_bar['macd_main']
        macd_signal = current_bar['macd_signal']
        prev_macd_main = prev_bar['macd_main']
        prev_macd_signal = prev_bar['macd_signal']
        
        # RSI momentum filter
        rsi = current_bar['rsi']
        
        # EMA trend filter
        close_price = current_bar['close']
        ema_200 = current_bar['ema_200']
        
        # Bullish Slingshot
        if (macd_main > 0 and  # Uptrend
            macd_main > macd_signal and prev_macd_main <= prev_macd_signal and  # Crossover
            rsi > 50 and  # RSI confirmation
            close_price > ema_200):  # Above long-term trend
            
            return {
                'signal': 1,
                'strategy': 'Slingshot',
                'reason': 'bullish_slingshot',
                'confidence': self._calculate_signal_confidence(df, bar_index, 'bullish')
            }
        
        # Bearish Slingshot
        elif (macd_main < 0 and  # Downtrend
              macd_main < macd_signal and prev_macd_main >= prev_macd_signal and  # Crossunder
              rsi < 50 and  # RSI confirmation
              close_price < ema_200):  # Below long-term trend
            
            return {
                'signal': -1,
                'strategy': 'Slingshot',
                'reason': 'bearish_slingshot',
                'confidence': self._calculate_signal_confidence(df, bar_index, 'bearish')
            }
        
        return {'signal': 0, 'strategy': '', 'reason': 'no_slingshot_signal'}
    
    def _detect_divergence_signal(self, df: pd.DataFrame, bar_index: int) -> Dict:
        """
        Detect MACD/Price divergence signal
        
        Args:
            df: DataFrame with indicators
            bar_index: Current bar index
        
        Returns:
            dict: Signal information
        """
        if bar_index < 50:  # Need sufficient lookback for divergence
            return {'signal': 0, 'strategy': '', 'reason': 'insufficient_divergence_data'}
        
        # Look for divergence in the last 20-50 bars
        lookback_start = max(0, bar_index - 50)
        lookback_end = bar_index
        
        price_data = df['close'].iloc[lookback_start:lookback_end + 1]
        macd_data = df['macd_main'].iloc[lookback_start:lookback_end + 1]
        
        # Detect bullish divergence
        bullish_div = self._find_bullish_divergence(price_data, macd_data)
        
        # Detect bearish divergence
        bearish_div = self._find_bearish_divergence(price_data, macd_data)
        
        current_bar = df.iloc[bar_index]
        prev_bar = df.iloc[bar_index - 1] if bar_index > 0 else current_bar
        
        # Check for MACD crossover confirmation
        macd_crossover_up = (current_bar['macd_main'] > current_bar['macd_signal'] and 
                           prev_bar['macd_main'] <= prev_bar['macd_signal'])
        
        macd_crossover_down = (current_bar['macd_main'] < current_bar['macd_signal'] and 
                             prev_bar['macd_main'] >= prev_bar['macd_signal'])
        
        # Bullish divergence signal
        if bullish_div and macd_crossover_up:
            return {
                'signal': 1,
                'strategy': 'Divergence',
                'reason': 'bullish_divergence',
                'confidence': self._calculate_signal_confidence(df, bar_index, 'bullish')
            }
        
        # Bearish divergence signal
        elif bearish_div and macd_crossover_down:
            return {
                'signal': -1,
                'strategy': 'Divergence',
                'reason': 'bearish_divergence',
                'confidence': self._calculate_signal_confidence(df, bar_index, 'bearish')
            }
        
        return {'signal': 0, 'strategy': '', 'reason': 'no_divergence_signal'}
    
    def _find_bullish_divergence(self, price_data: pd.Series, macd_data: pd.Series) -> bool:\n        \"\"\"\n        Find bullish divergence (price lower lows, MACD higher lows)\n        \"\"\"\n        # Find the last two significant lows\n        price_lows = self._find_lows(price_data, min_separation=5)\n        macd_lows = self._find_lows(macd_data, min_separation=5)\n        \n        if len(price_lows) < 2 or len(macd_lows) < 2:\n            return False\n        \n        # Get the last two lows\n        price_low1, price_low2 = price_lows[-2:]\n        macd_low1, macd_low2 = macd_lows[-2:]\n        \n        # Check for divergence\n        price_declining = price_data.iloc[price_low2] < price_data.iloc[price_low1]\n        macd_rising = macd_data.iloc[macd_low2] > macd_data.iloc[macd_low1]\n        \n        return price_declining and macd_rising\n    \n    def _find_bearish_divergence(self, price_data: pd.Series, macd_data: pd.Series) -> bool:\n        \"\"\"\n        Find bearish divergence (price higher highs, MACD lower highs)\n        \"\"\"\n        # Find the last two significant highs\n        price_highs = self._find_highs(price_data, min_separation=5)\n        macd_highs = self._find_highs(macd_data, min_separation=5)\n        \n        if len(price_highs) < 2 or len(macd_highs) < 2:\n            return False\n        \n        # Get the last two highs\n        price_high1, price_high2 = price_highs[-2:]\n        macd_high1, macd_high2 = macd_highs[-2:]\n        \n        # Check for divergence\n        price_rising = price_data.iloc[price_high2] > price_data.iloc[price_high1]\n        macd_declining = macd_data.iloc[macd_high2] < macd_data.iloc[macd_high1]\n        \n        return price_rising and macd_declining\n    \n    def _find_lows(self, data: pd.Series, min_separation: int = 5) -> List[int]:\n        \"\"\"Find local lows in data series\"\"\"\n        lows = []\n        \n        for i in range(min_separation, len(data) - min_separation):\n            is_low = True\n            for j in range(i - min_separation, i + min_separation + 1):\n                if j != i and data.iloc[j] <= data.iloc[i]:\n                    is_low = False\n                    break\n            \n            if is_low:\n                lows.append(i)\n        \n        return lows\n    \n    def _find_highs(self, data: pd.Series, min_separation: int = 5) -> List[int]:\n        \"\"\"Find local highs in data series\"\"\"\n        highs = []\n        \n        for i in range(min_separation, len(data) - min_separation):\n            is_high = True\n            for j in range(i - min_separation, i + min_separation + 1):\n                if j != i and data.iloc[j] >= data.iloc[i]:\n                    is_high = False\n                    break\n            \n            if is_high:\n                highs.append(i)\n        \n        return highs\n    \n    # =========================================================================\n    # RISK MANAGEMENT\n    # =========================================================================\n    \n    def calculate_position_size(self, stop_loss_distance: float, entry_price: float) -> float:\n        \"\"\"\n        Calculate position size based on risk management rules\n        \n        Args:\n            stop_loss_distance: Distance to stop loss in price units\n            entry_price: Entry price\n        \n        Returns:\n            float: Position size in lots\n        \"\"\"\n        # Base risk amount\n        risk_percent = self.config['max_risk_percent']\n        \n        # Apply adaptive risk scaling\n        if self.config['use_adaptive_risk']:\n            risk_percent *= self._calculate_adaptive_risk_multiplier()\n        \n        # Apply volatility adjustment\n        if self.config['use_volatility_adjustment']:\n            risk_percent *= self._calculate_volatility_adjustment()\n        \n        # Calculate risk amount in dollars\n        risk_amount = self.balance * risk_percent / 100.0\n        \n        # Calculate position size\n        # Assuming 1 pip = 0.0001 for EURUSD and $10 per lot per pip\n        pip_value = 10.0  # Standard lot pip value for EURUSD\n        stop_loss_pips = stop_loss_distance / 0.0001\n        \n        if stop_loss_pips <= 0:\n            return 0.0\n        \n        position_size = risk_amount / (stop_loss_pips * pip_value)\n        \n        # Apply position size limits\n        min_lot = 0.01\n        max_lot = min(1.0, self.balance / 1000 * 0.1)  # Max 0.1 lot per $1000\n        \n        position_size = max(min_lot, min(position_size, max_lot))\n        \n        # Round to valid lot size\n        position_size = round(position_size, 2)\n        \n        return position_size\n    \n    def _calculate_adaptive_risk_multiplier(self) -> float:\n        \"\"\"\n        Calculate adaptive risk multiplier based on recent performance\n        \"\"\"\n        # Increase risk after consecutive wins\n        if self.consecutive_wins >= self.config['consecutive_win_bonus']:\n            multiplier = min(\n                self.config['max_risk_multiplier'],\n                1.0 + (self.consecutive_wins - self.config['consecutive_win_bonus'] + 1) * 0.3\n            )\n            return multiplier\n        \n        # Reduce risk after consecutive losses\n        elif self.consecutive_losses >= self.config['consecutive_loss_reduction']:\n            multiplier = max(\n                self.config['min_risk_multiplier'],\n                1.0 - (self.consecutive_losses - self.config['consecutive_loss_reduction'] + 1) * 0.2\n            )\n            return multiplier\n        \n        return 1.0\n    \n    def _calculate_volatility_adjustment(self) -> float:\n        \"\"\"\n        Calculate volatility-based position size adjustment\n        \"\"\"\n        # This would need ATR data - simplified for now\n        return 1.0\n    \n    def _is_portfolio_risk_acceptable(self, new_trade_risk: float) -> bool:\n        \"\"\"\n        Check if new trade risk is acceptable given portfolio limits\n        \n        Args:\n            new_trade_risk: Risk of new trade as percentage of account\n        \n        Returns:\n            bool: True if risk is acceptable\n        \"\"\"\n        # Check maximum concurrent trades\n        if len(self.positions) >= self.config['max_concurrent_trades']:\n            return False\n        \n        # Check portfolio heat\n        total_risk = self.portfolio_heat + new_trade_risk\n        if total_risk > self.config['max_portfolio_heat']:\n            return False\n        \n        # Check currency exposure (simplified)\n        # In real implementation, would track exposure by currency\n        \n        return True\n    \n    # =========================================================================\n    # FILTERS\n    # =========================================================================\n    \n    def _is_volatility_acceptable(self, df: pd.DataFrame, bar_index: int) -> bool:\n        \"\"\"\n        Check if current volatility is acceptable for trading\n        \"\"\"\n        current_bar = df.iloc[bar_index]\n        \n        # Bollinger Bands width filter\n        bb_width = current_bar['bb_upper'] - current_bar['bb_lower']\n        min_bb_width = self.config['min_bb_width_pips'] * 0.0001  # Convert pips to price\n        \n        if bb_width < min_bb_width:\n            return False\n        \n        # ATR filter (if enabled)\n        if self.config['use_atr_filter'] and 'atr' in df.columns:\n            current_atr = current_bar['atr']\n            \n            # Calculate average ATR\n            atr_data = df['atr'].iloc[max(0, bar_index - self.config['atr_period']):bar_index + 1]\n            avg_atr = atr_data.mean()\n            \n            if avg_atr > 0:\n                atr_ratio = current_atr / avg_atr\n                if (atr_ratio < self.config['min_atr_multiplier'] or \n                    atr_ratio > self.config['max_atr_multiplier']):\n                    return False\n        \n        return True\n    \n    def _is_valid_trading_time(self, current_time: datetime) -> bool:\n        \"\"\"\n        Check if current time is within trading hours\n        \"\"\"\n        hour = current_time.hour\n        \n        if hour < self.config['start_hour'] or hour >= self.config['end_hour']:\n            return False\n        \n        if self.config['trade_only_overlap']:\n            # London-NY overlap (12:00-16:00 GMT)\n            if hour < 12 or hour >= 16:\n                return False\n        \n        return True\n    \n    def _is_safe_to_trade(self, current_time: datetime) -> bool:\n        \"\"\"\n        Check if it's safe to trade (news filter)\n        \"\"\"\n        if not self.config['avoid_news']:\n            return True\n        \n        hour = current_time.hour\n        minute = current_time.minute\n        \n        # Avoid trading around major session opens\n        # London open (8:00 GMT)\n        if (hour == 7 and minute >= 30) or (hour == 8 and minute <= 30):\n            return False\n        \n        # NY open (13:00 GMT)\n        if (hour == 12 and minute >= 30) or (hour == 13 and minute <= 30):\n            return False\n        \n        return True\n    \n    def _calculate_signal_confidence(self, df: pd.DataFrame, bar_index: int, direction: str) -> float:\n        \"\"\"\n        Calculate confidence score for signal (0.0 to 1.0)\n        \"\"\"\n        confidence = 0.5  # Base confidence\n        \n        current_bar = df.iloc[bar_index]\n        \n        # ADX strength adds confidence\n        adx = current_bar['adx']\n        if adx > 30:\n            confidence += 0.2\n        elif adx < 20:\n            confidence -= 0.1\n        \n        # RSI position adds confidence\n        rsi = current_bar['rsi']\n        if direction == 'bullish' and 30 < rsi < 70:\n            confidence += 0.1\n        elif direction == 'bearish' and 30 < rsi < 70:\n            confidence += 0.1\n        \n        # Volatility adds confidence\n        bb_width = current_bar['bb_upper'] - current_bar['bb_lower']\n        if bb_width > self.config['min_bb_width_pips'] * 0.0001 * 1.5:\n            confidence += 0.1\n        \n        return min(1.0, max(0.0, confidence))\n    \n    # =========================================================================\n    # TRADE EXECUTION\n    # =========================================================================\n    \n    def execute_trade(self, signal_info: Dict, df: pd.DataFrame, bar_index: int) -> Dict:\n        \"\"\"\n        Execute trade based on signal\n        \n        Args:\n            signal_info: Signal information from get_trading_signal\n            df: DataFrame with price data\n            bar_index: Current bar index\n        \n        Returns:\n            dict: Trade execution result\n        \"\"\"\n        if signal_info['signal'] == 0:\n            return {'success': False, 'reason': 'no_signal'}\n        \n        current_bar = df.iloc[bar_index]\n        signal = signal_info['signal']\n        strategy = signal_info['strategy']\n        \n        # Calculate entry price (simplified - using close price)\n        entry_price = current_bar['close']\n        \n        # Calculate stop loss and take profit\n        sl_tp = self._calculate_stop_loss_take_profit(\n            df, bar_index, signal, entry_price\n        )\n        \n        if not sl_tp:\n            return {'success': False, 'reason': 'invalid_sl_tp'}\n        \n        stop_loss = sl_tp['stop_loss']\n        take_profit = sl_tp['take_profit']\n        stop_loss_distance = abs(entry_price - stop_loss)\n        \n        # Calculate position size\n        position_size = self.calculate_position_size(stop_loss_distance, entry_price)\n        \n        if position_size <= 0:\n            return {'success': False, 'reason': 'invalid_position_size'}\n        \n        # Check portfolio risk\n        trade_risk = (stop_loss_distance / entry_price) * 100\n        if not self._is_portfolio_risk_acceptable(trade_risk):\n            return {'success': False, 'reason': 'portfolio_risk_exceeded'}\n        \n        # Create trade\n        trade_id = len(self.trade_history) + 1\n        trade = {\n            'id': trade_id,\n            'symbol': 'EURUSD',  # Hardcoded for now\n            'direction': 'BUY' if signal > 0 else 'SELL',\n            'size': position_size,\n            'entry_price': entry_price,\n            'stop_loss': stop_loss,\n            'take_profit': take_profit,\n            'entry_time': current_bar['datetime'],\n            'strategy': strategy,\n            'status': 'open',\n            'unrealized_pnl': 0.0,\n            'commission': 0.0,  # Simplified\n            'swap': 0.0,  # Simplified\n            'partial_closes': []\n        }\n        \n        # Add to positions\n        self.positions[trade_id] = trade\n        \n        # Update portfolio heat\n        self.portfolio_heat += trade_risk\n        \n        return {\n            'success': True,\n            'trade_id': trade_id,\n            'trade': trade,\n            'reason': f'{strategy}_{signal_info[\"reason\"]}'\n        }\n    \n    def _calculate_stop_loss_take_profit(self, df: pd.DataFrame, bar_index: int, \n                                       signal: int, entry_price: float) -> Optional[Dict]:\n        \"\"\"\n        Calculate stop loss and take profit levels\n        \"\"\"\n        current_bar = df.iloc[bar_index]\n        \n        # Bollinger Bands based stop loss\n        if signal > 0:  # Buy\n            stop_loss = current_bar['bb_lower'] - self.config['stop_loss_buffer'] * 0.0001\n        else:  # Sell\n            stop_loss = current_bar['bb_upper'] + self.config['stop_loss_buffer'] * 0.0001\n        \n        # Ensure minimum stop loss distance\n        min_stop_distance = 10 * 0.0001  # 10 pips\n        if abs(entry_price - stop_loss) < min_stop_distance:\n            if signal > 0:\n                stop_loss = entry_price - min_stop_distance\n            else:\n                stop_loss = entry_price + min_stop_distance\n        \n        # Calculate take profit\n        stop_loss_distance = abs(entry_price - stop_loss)\n        take_profit_distance = stop_loss_distance * self.config['take_profit_ratio']\n        \n        if signal > 0:\n            take_profit = entry_price + take_profit_distance\n        else:\n            take_profit = entry_price - take_profit_distance\n        \n        return {\n            'stop_loss': stop_loss,\n            'take_profit': take_profit,\n            'stop_loss_distance': stop_loss_distance\n        }\n    \n    # =========================================================================\n    # POSITION MANAGEMENT\n    # =========================================================================\n    \n    def update_positions(self, df: pd.DataFrame, bar_index: int):\n        \"\"\"\n        Update all open positions with current market data\n        \"\"\"\n        if not self.positions:\n            return\n        \n        current_bar = df.iloc[bar_index]\n        current_price = current_bar['close']\n        \n        positions_to_close = []\n        \n        for trade_id, trade in self.positions.items():\n            # Update unrealized P&L\n            if trade['direction'] == 'BUY':\n                pnl = (current_price - trade['entry_price']) * trade['size'] * 100000  # Standard lot\n            else:\n                pnl = (trade['entry_price'] - current_price) * trade['size'] * 100000\n            \n            trade['unrealized_pnl'] = pnl\n            \n            # Check for stop loss/take profit\n            if trade['direction'] == 'BUY':\n                if current_price <= trade['stop_loss']:\n                    positions_to_close.append((trade_id, 'stop_loss', trade['stop_loss']))\n                elif current_price >= trade['take_profit']:\n                    positions_to_close.append((trade_id, 'take_profit', trade['take_profit']))\n            else:\n                if current_price >= trade['stop_loss']:\n                    positions_to_close.append((trade_id, 'stop_loss', trade['stop_loss']))\n                elif current_price <= trade['take_profit']:\n                    positions_to_close.append((trade_id, 'take_profit', trade['take_profit']))\n        \n        # Close positions that hit SL/TP\n        for trade_id, reason, close_price in positions_to_close:\n            self.close_position(trade_id, close_price, reason, current_bar['datetime'])\n    \n    def close_position(self, trade_id: int, close_price: float, reason: str, close_time: datetime):\n        \"\"\"\n        Close a position\n        \"\"\"\n        if trade_id not in self.positions:\n            return\n        \n        trade = self.positions[trade_id]\n        \n        # Calculate final P&L\n        if trade['direction'] == 'BUY':\n            pnl = (close_price - trade['entry_price']) * trade['size'] * 100000\n        else:\n            pnl = (trade['entry_price'] - close_price) * trade['size'] * 100000\n        \n        # Update trade record\n        trade['close_price'] = close_price\n        trade['close_time'] = close_time\n        trade['realized_pnl'] = pnl\n        trade['status'] = 'closed'\n        trade['close_reason'] = reason\n        \n        # Update balance\n        self.balance += pnl\n        \n        # Update performance tracking\n        if pnl > 0:\n            self.consecutive_wins += 1\n            self.consecutive_losses = 0\n        else:\n            self.consecutive_losses += 1\n            self.consecutive_wins = 0\n        \n        # Move to trade history\n        self.trade_history.append(trade)\n        del self.positions[trade_id]\n        \n        # Update portfolio heat\n        trade_risk = abs(trade['entry_price'] - trade['stop_loss']) / trade['entry_price'] * 100\n        self.portfolio_heat = max(0, self.portfolio_heat - trade_risk)\n        \n        # Update equity and drawdown\n        self.equity = self.balance + sum(pos['unrealized_pnl'] for pos in self.positions.values())\n        self.max_equity = max(self.max_equity, self.equity)\n        current_drawdown = (self.max_equity - self.equity) / self.max_equity * 100\n        self.max_drawdown = max(self.max_drawdown, current_drawdown)\n    \n    # =========================================================================\n    # PERFORMANCE METRICS\n    # =========================================================================\n    \n    def get_performance_metrics(self) -> Dict:\n        \"\"\"\n        Calculate comprehensive performance metrics\n        \"\"\"\n        if not self.trade_history:\n            return {\n                'total_trades': 0,\n                'win_rate': 0.0,\n                'profit_factor': 0.0,\n                'total_return': 0.0,\n                'max_drawdown': self.max_drawdown,\n                'sharpe_ratio': 0.0\n            }\n        \n        trades_df = pd.DataFrame(self.trade_history)\n        \n        # Basic metrics\n        total_trades = len(trades_df)\n        winning_trades = len(trades_df[trades_df['realized_pnl'] > 0])\n        losing_trades = len(trades_df[trades_df['realized_pnl'] < 0])\n        \n        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0\n        \n        # P&L metrics\n        total_pnl = trades_df['realized_pnl'].sum()\n        gross_profit = trades_df[trades_df['realized_pnl'] > 0]['realized_pnl'].sum()\n        gross_loss = abs(trades_df[trades_df['realized_pnl'] < 0]['realized_pnl'].sum())\n        \n        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')\n        total_return = (self.balance - self.config['initial_balance']) / self.config['initial_balance'] * 100\n        \n        # Risk metrics\n        if len(trades_df) > 1:\n            returns = trades_df['realized_pnl'] / self.config['initial_balance']\n            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0\n        else:\n            sharpe_ratio = 0\n        \n        return {\n            'total_trades': total_trades,\n            'winning_trades': winning_trades,\n            'losing_trades': losing_trades,\n            'win_rate': win_rate,\n            'profit_factor': profit_factor,\n            'total_return': total_return,\n            'total_pnl': total_pnl,\n            'gross_profit': gross_profit,\n            'gross_loss': gross_loss,\n            'max_drawdown': self.max_drawdown,\n            'sharpe_ratio': sharpe_ratio,\n            'current_balance': self.balance,\n            'current_equity': self.equity,\n            'open_positions': len(self.positions)\n        }\n\n\ndef main():\n    \"\"\"Example usage of UltraStrategy\"\"\"\n    print(\"🎯 Ultra Strategy Example\")\n    print(\"========================\")\n    \n    # Create strategy with default config\n    strategy = UltraStrategy()\n    \n    print(f\"✅ Strategy initialized\")\n    print(f\"💰 Initial balance: ${strategy.balance:,.2f}\")\n    print(f\"📊 Configuration: {len(strategy.config)} parameters\")\n    \n    # Show key configuration\n    key_params = [\n        'max_risk_percent', 'take_profit_ratio', 'adx_trend', \n        'max_concurrent_trades', 'use_adaptive_risk'\n    ]\n    \n    print(\"\\n🔧 Key parameters:\")\n    for param in key_params:\n        value = strategy.config.get(param, 'N/A')\n        print(f\"   {param}: {value}\")\n\n\nif __name__ == \"__main__\":\n    main()