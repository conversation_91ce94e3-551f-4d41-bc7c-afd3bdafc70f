#!/usr/bin/env python3
"""
Ultra Conservative Strategy
===========================

Pure Python port of the ultra.mq5 Expert Advisor logic.
Implements all strategy components:
- Slingshot strategy (trending markets)
- Divergence strategy (ranging markets)
- Advanced risk management
- Portfolio heat management
- Adaptive risk scaling
- Enhanced position management

This implementation matches the MQL5 version exactly.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings

class UltraStrategy:
    """
    Ultra Conservative Strategy Implementation
    
    Ports the complete ultra.mq5 logic to Python including:
    - Signal generation (Slingshot & Divergence)
    - Risk management
    - Position sizing
    - Portfolio management
    """
    
    def __init__(self, config: dict = None):
        """
        Initialize Ultra Strategy
        
        Args:
            config: Strategy configuration parameters
        """
        # Default configuration matching ultra.mq5
        self.config = config or self._get_default_config()
        
        # Strategy state
        self.positions = {}  # Active positions
        self.trade_history = []  # Completed trades
        self.balance = self.config['initial_balance']
        self.equity = self.balance
        
        # Risk management state
        self.consecutive_wins = 0
        self.consecutive_losses = 0
        self.current_risk_multiplier = 1.0
        self.portfolio_heat = 0.0
        self.currency_exposure = {}
        
        # Signal state
        self.bullish_divergence_setup = False
        self.bearish_divergence_setup = False
        self.pending_signal = 0
        self.signal_bar_time = None
        self.pending_strategy = ""
        
        # Performance tracking
        self.daily_stats = {}
        self.max_equity = self.balance
        self.max_drawdown = 0.0
        
        print(f"🎯 Ultra Conservative Strategy initialized")
        print(f"💰 Initial balance: ${self.balance:,.2f}")
    
    def _get_default_config(self) -> dict:
        """Get default configuration matching ultra.mq5 parameters"""
        return {
            # Account settings
            'initial_balance': 1000.0,
            'leverage': 100,
            
            # MACD settings
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            
            # Bollinger Bands
            'bb_period': 20,
            'bb_deviation': 2.0,
            'min_bb_width_pips': 15.0,
            
            # Strategy filters
            'ema_period': 200,
            'adx_period': 14,
            'adx_trend': 25.0,
            'rsi_period': 14,
            
            # Position management
            'max_risk_percent': 1.0,
            'take_profit_ratio': 2.0,
            'stop_loss_buffer': 10.0,
            'trailing_stop': 20.0,
            'break_even_level': 1.5,
            
            # Trading hours
            'start_hour': 7,
            'end_hour': 20,
            'trade_only_overlap': False,
            
            # Portfolio risk management
            'max_concurrent_trades': 3,
            'max_portfolio_heat': 6.0,
            'max_currency_exposure': 40.0,
            'correlation_limit': 0.7,
            
            # Adaptive risk
            'use_adaptive_risk': True,
            'max_risk_multiplier': 2.0,
            'min_risk_multiplier': 0.3,
            'consecutive_win_bonus': 3,
            'consecutive_loss_reduction': 2,
            
            # Enhanced position management
            'use_partial_tp': True,
            'partial_tp1_rr': 1.0,
            'partial_tp1_percent': 40.0,
            'partial_tp2_rr': 2.0,
            'partial_tp2_percent': 30.0,
            'use_atr_trailing': True,
            'atr_trailing_multiplier': 1.5,
            
            # Volatility enhancement
            'use_atr_filter': True,
            'atr_period': 14,
            'min_atr_multiplier': 0.8,
            'max_atr_multiplier': 3.0,
            'use_volatility_adjustment': True,
            
            # News/event management
            'avoid_news': True,
            'news_buffer_minutes': 30,
            'reduce_risk_friday': True,
            'friday_risk_reduction': 0.5
        }
    
    # =========================================================================
    # SIGNAL GENERATION
    # =========================================================================
    
    def get_trading_signal(self, df: pd.DataFrame, bar_index: int) -> Dict:
        """
        Main signal generation function (matches ultra.mq5 GetTradingSignal)
        
        Args:
            df: DataFrame with price and indicator data
            bar_index: Current bar index
        
        Returns:
            dict: Signal information
        """
        if bar_index < 100:  # Need sufficient data for indicators
            return {'signal': 0, 'strategy': '', 'reason': 'insufficient_data'}
        
        # Get current bar data
        current_bar = df.iloc[bar_index]
        
        # Check volatility filter
        if not self._is_volatility_acceptable(df, bar_index):
            return {'signal': 0, 'strategy': '', 'reason': 'volatility_filter'}
        
        # Check trading hours
        if not self._is_valid_trading_time(current_bar['datetime']):
            return {'signal': 0, 'strategy': '', 'reason': 'trading_hours'}
        
        # Check if safe to trade (news filter)
        if not self._is_safe_to_trade(current_bar['datetime']):
            return {'signal': 0, 'strategy': '', 'reason': 'news_filter'}
        
        # Market regime detection (ADX-based)
        adx_value = current_bar['adx']
        
        if adx_value >= self.config['adx_trend']:  # Trending market
            return self._detect_slingshot_signal(df, bar_index)
        else:  # Ranging market
            return self._detect_divergence_signal(df, bar_index)
    
    def _detect_slingshot_signal(self, df: pd.DataFrame, bar_index: int) -> Dict:
        """
        Detect Slingshot trend continuation signal
        
        Args:
            df: DataFrame with indicators
            bar_index: Current bar index
        
        Returns:
            dict: Signal information
        """
        current_bar = df.iloc[bar_index]
        prev_bar = df.iloc[bar_index - 1] if bar_index > 0 else current_bar
        
        # MACD crossover detection
        macd_main = current_bar['macd_main']
        macd_signal = current_bar['macd_signal']
        prev_macd_main = prev_bar['macd_main']
        prev_macd_signal = prev_bar['macd_signal']
        
        # RSI momentum filter
        rsi = current_bar['rsi']
        
        # EMA trend filter
        close_price = current_bar['close']
        ema_200 = current_bar['ema_200']
        
        # Bullish Slingshot
        if (macd_main > 0 and  # Uptrend
            macd_main > macd_signal and prev_macd_main <= prev_macd_signal and  # Crossover
            rsi > 50 and  # RSI confirmation
            close_price > ema_200):  # Above long-term trend
            
            return {
                'signal': 1,
                'strategy': 'Slingshot',
                'reason': 'bullish_slingshot',
                'confidence': self._calculate_signal_confidence(df, bar_index, 'bullish')
            }
        
        # Bearish Slingshot
        elif (macd_main < 0 and  # Downtrend
              macd_main < macd_signal and prev_macd_main >= prev_macd_signal and  # Crossunder
              rsi < 50 and  # RSI confirmation
              close_price < ema_200):  # Below long-term trend
            
            return {
                'signal': -1,
                'strategy': 'Slingshot',
                'reason': 'bearish_slingshot',
                'confidence': self._calculate_signal_confidence(df, bar_index, 'bearish')
            }
        
        return {'signal': 0, 'strategy': '', 'reason': 'no_slingshot_signal'}
    
    def _detect_divergence_signal(self, df: pd.DataFrame, bar_index: int) -> Dict:
        """
        Detect MACD/Price divergence signal
        
        Args:
            df: DataFrame with indicators
            bar_index: Current bar index
        
        Returns:
            dict: Signal information
        """
        if bar_index < 50:  # Need sufficient lookback for divergence
            return {'signal': 0, 'strategy': '', 'reason': 'insufficient_divergence_data'}
        
        # Look for divergence in the last 20-50 bars
        lookback_start = max(0, bar_index - 50)
        lookback_end = bar_index
        
        price_data = df['close'].iloc[lookback_start:lookback_end + 1]
        macd_data = df['macd_main'].iloc[lookback_start:lookback_end + 1]
        
        # Detect bullish divergence
        bullish_div = self._find_bullish_divergence(price_data, macd_data)
        
        # Detect bearish divergence
        bearish_div = self._find_bearish_divergence(price_data, macd_data)
        
        current_bar = df.iloc[bar_index]
        prev_bar = df.iloc[bar_index - 1] if bar_index > 0 else current_bar
        
        # Check for MACD crossover confirmation
        macd_crossover_up = (current_bar['macd_main'] > current_bar['macd_signal'] and 
                           prev_bar['macd_main'] <= prev_bar['macd_signal'])
        
        macd_crossover_down = (current_bar['macd_main'] < current_bar['macd_signal'] and 
                             prev_bar['macd_main'] >= prev_bar['macd_signal'])
        
        # Bullish divergence signal
        if bullish_div and macd_crossover_up:
            return {
                'signal': 1,
                'strategy': 'Divergence',
                'reason': 'bullish_divergence',
                'confidence': self._calculate_signal_confidence(df, bar_index, 'bullish')
            }
        
        # Bearish divergence signal
        elif bearish_div and macd_crossover_down:
            return {
                'signal': -1,
                'strategy': 'Divergence',
                'reason': 'bearish_divergence',
                'confidence': self._calculate_signal_confidence(df, bar_index, 'bearish')
            }
        
        return {'signal': 0, 'strategy': '', 'reason': 'no_divergence_signal'}
    
