# Ultra Backtesting System Requirements
# ====================================

# Core Data Science Libraries
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# Optimization
optuna>=3.0.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Technical Analysis (optional, can implement manually)
ta-lib>=0.4.24

# Configuration
pyyaml>=6.0

# Database
sqlite3  # Built-in with Python

# Utilities
tqdm>=4.64.0
pathlib  # Built-in with Python

# Development/Testing
pytest>=7.0.0

# Optional: MetaTrader5 (only for one-time data export)
# MetaTrader5>=5.0.0  # Uncomment if you want to run the exporter

# Optional: Machine Learning (for advanced features)
# scikit-learn>=1.1.0
# xgboost>=1.6.0

# Optional: Performance optimization
# numba>=0.56.0
# bottleneck>=1.3.0