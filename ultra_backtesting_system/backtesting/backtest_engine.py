#!/usr/bin/env python3
"""
Backtesting Engine
==================

Event-driven backtesting engine that simulates the Ultra Conservative Strategy
with realistic order execution, slippage, and portfolio management.

This engine processes historical data bar-by-bar, generating signals through the
strategy and executing trades with proper risk management.

Features:
- Event-driven architecture
- Realistic trade execution with slippage
- Portfolio heat management
- Detailed trade tracking
- Performance analytics
- Progress reporting
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
from dataclasses import dataclass

@dataclass
class TradeEvent:
    """Represents a trade event in the backtesting engine"""
    timestamp: datetime
    symbol: str
    event_type: str  # 'signal', 'order', 'fill', 'close'
    direction: str   # 'BUY', 'SELL'
    size: float
    price: float
    strategy: str
    details: Dict

class BacktestEngine:
    """
    Event-driven backtesting engine for Ultra Conservative Strategy
    
    Simulates realistic trading conditions including:
    - Order execution with slippage
    - Spread simulation
    - Commission and swap costs
    - Portfolio heat management
    - Risk limits enforcement
    """
    
    def __init__(self, initial_balance: float = 1000.0, config: dict = None):
        """
        Initialize backtesting engine
        
        Args:
            initial_balance: Starting account balance
            config: Engine configuration parameters
        """
        self.initial_balance = initial_balance
        self.config = config or self._get_default_config()
        
        # Account state
        self.balance = initial_balance
        self.equity = initial_balance
        self.free_margin = initial_balance
        
        # Trading state
        self.positions = {}  # Active positions
        self.trade_history = []  # Completed trades
        self.events = []  # All trading events
        self.open_orders = {}  # Pending orders
        
        # Performance tracking
        self.daily_stats = {}
        self.max_equity = initial_balance
        self.max_drawdown = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
        # Runtime state
        self.current_bar = None
        self.current_time = None
        self.bar_index = 0
        
        print(f"🚀 Backtest Engine initialized")
        print(f"💰 Initial balance: ${initial_balance:,.2f}")
    
    def _get_default_config(self) -> dict:
        """Get default engine configuration"""
        return {
            # Execution settings
            'spread_pips': 1.5,           # Bid-ask spread in pips
            'slippage_pips': 0.5,         # Execution slippage in pips
            'commission_per_lot': 7.0,    # Commission per lot
            'swap_long': -0.5,            # Daily swap for long positions
            'swap_short': 0.3,            # Daily swap for short positions
            
            # Market settings
            'pip_value': 10.0,            # Dollar value per pip per lot
            'leverage': 100,              # Account leverage
            'margin_requirement': 0.01,   # Margin requirement (1%)
            
            # Risk settings
            'max_slippage_pips': 3.0,     # Maximum allowed slippage
            'max_spread_pips': 5.0,       # Maximum allowed spread
            'order_timeout_bars': 5,      # Order timeout in bars
            
            # Reporting
            'progress_frequency': 1000,   # Progress update frequency
            'detailed_logging': False,    # Enable detailed event logging
        }
    
    # =========================================================================
    # MAIN BACKTESTING INTERFACE
    # =========================================================================
    
    def run_backtest(self, data: pd.DataFrame, strategy, start_date: str = None, 
                    end_date: str = None) -> Dict:
        """
        Run complete backtest on historical data
        
        Args:
            data: DataFrame with OHLCV and indicator data
            strategy: Strategy instance (UltraStrategy)
            start_date: Start date for backtest (YYYY-MM-DD)
            end_date: End date for backtest (YYYY-MM-DD)
        
        Returns:
            dict: Comprehensive backtest results
        """
        print(f"🔄 Starting backtest...")
        print(f"📊 Data range: {data.index[0]} to {data.index[-1]}")
        print(f"📈 Total bars: {len(data):,}")
        
        # Filter data by date range if specified
        if start_date or end_date:
            data = self._filter_data_by_date(data, start_date, end_date)
            print(f"📅 Filtered to: {len(data):,} bars")
        
        # Validate data
        if not self._validate_backtest_data(data):
            raise ValueError("Invalid backtest data")
        
        # Reset engine state
        self._reset_engine_state()
        
        # Initialize strategy with engine config
        strategy_config = strategy.config.copy()
        strategy_config['initial_balance'] = self.initial_balance
        strategy.__init__(strategy_config)
        
        # Run bar-by-bar simulation
        total_bars = len(data)
        start_time = datetime.now()
        
        for self.bar_index in range(total_bars):
            self.current_bar = data.iloc[self.bar_index]
            self.current_time = self.current_bar['datetime']
            
            # Process trading events for this bar
            self._process_bar(data, strategy, self.bar_index)
            
            # Update progress
            if self.bar_index % self.config['progress_frequency'] == 0 or self.bar_index == total_bars - 1:
                progress = (self.bar_index + 1) / total_bars * 100
                elapsed = datetime.now() - start_time
                print(f"⏳ Progress: {progress:.1f}% | Bar {self.bar_index + 1:,}/{total_bars:,} | "
                      f"Balance: ${self.balance:,.2f} | Positions: {len(self.positions)}")
        
        # Final calculations
        final_results = self._calculate_final_results(strategy, start_time)
        
        print(f"✅ Backtest completed!")
        print(f"📈 Total return: {final_results['total_return']:.2f}%")
        print(f"🎯 Win rate: {final_results['win_rate']:.1f}%")
        print(f"📊 Total trades: {final_results['total_trades']}")
        
        return final_results
    
    def _process_bar(self, data: pd.DataFrame, strategy, bar_index: int):
        """
        Process all events for a single bar
        
        Args:
            data: Full dataset
            strategy: Strategy instance
            bar_index: Current bar index
        """
        # 1. Process pending orders (check for fills)
        self._process_pending_orders()
        
        # 2. Update existing positions (check SL/TP, update P&L)
        self._update_positions()
        
        # 3. Calculate daily swaps (if new day)
        self._process_daily_swaps()
        
        # 4. Get trading signal from strategy
        signal_info = strategy.get_trading_signal(data, bar_index)
        
        # 5. Execute trades based on signals
        if signal_info['signal'] != 0:
            self._execute_signal(signal_info, strategy, data, bar_index)
        
        # 6. Update account metrics
        self._update_account_metrics()
        
        # 7. Log events if detailed logging enabled
        if self.config['detailed_logging']:
            self._log_bar_events(signal_info)
    
    # =========================================================================
    # ORDER EXECUTION
    # =========================================================================
    
    def _execute_signal(self, signal_info: Dict, strategy, data: pd.DataFrame, bar_index: int):
        """
        Execute trading signal by creating market order
        
        Args:
            signal_info: Signal from strategy
            strategy: Strategy instance  
            data: Price data
            bar_index: Current bar index
        """
        # Check if we can trade (spread, volatility, etc.)
        if not self._can_execute_trade():
            return
        
        # Execute trade through strategy
        trade_result = strategy.execute_trade(signal_info, data, bar_index)
        
        if trade_result['success']:
            trade = trade_result['trade']
            
            # Calculate execution price with spread and slippage
            execution_price = self._calculate_execution_price(
                trade['entry_price'], 
                trade['direction']
            )
            
            # Check if execution is still valid
            max_slippage = self.config['max_slippage_pips'] * 0.0001
            price_diff = abs(execution_price - trade['entry_price'])
            
            if price_diff > max_slippage:
                self._log_event(TradeEvent(
                    timestamp=self.current_time,
                    symbol=trade['symbol'],
                    event_type='order_rejected',
                    direction=trade['direction'],
                    size=trade['size'],
                    price=execution_price,
                    strategy=trade['strategy'],
                    details={'reason': 'excessive_slippage', 'slippage': price_diff}
                ))
                return
            
            # Execute the trade
            trade['entry_price'] = execution_price
            trade['commission'] = self._calculate_commission(trade['size'])
            
            # Update balance for commission
            self.balance -= trade['commission']
            
            # Add to our positions tracking
            self.positions[trade['id']] = trade
            
            # Log execution event
            self._log_event(TradeEvent(
                timestamp=self.current_time,
                symbol=trade['symbol'],
                event_type='order_filled',
                direction=trade['direction'],
                size=trade['size'],
                price=execution_price,
                strategy=trade['strategy'],
                details={
                    'trade_id': trade['id'],
                    'stop_loss': trade['stop_loss'],
                    'take_profit': trade['take_profit'],
                    'commission': trade['commission']
                }
            ))
    
    def _calculate_execution_price(self, market_price: float, direction: str) -> float:
        """
        Calculate realistic execution price with spread and slippage
        
        Args:
            market_price: Current market price
            direction: 'BUY' or 'SELL'
        
        Returns:
            float: Execution price
        """
        spread_half = self.config['spread_pips'] * 0.0001 / 2
        slippage = np.random.normal(0, self.config['slippage_pips'] * 0.0001 / 2)
        
        if direction == 'BUY':
            # Buy at ask price + slippage
            execution_price = market_price + spread_half + abs(slippage)
        else:
            # Sell at bid price - slippage  
            execution_price = market_price - spread_half - abs(slippage)
        
        return execution_price
    
    def _calculate_commission(self, size: float) -> float:
        """Calculate commission for trade size"""
        return size * self.config['commission_per_lot']
    
    # =========================================================================
    # POSITION MANAGEMENT
    # =========================================================================
    
    def _update_positions(self):
        """Update all open positions with current market data"""
        if not self.positions:
            return
        
        current_price = self.current_bar['close']
        positions_to_close = []
        
        for trade_id, trade in self.positions.items():
            # Update unrealized P&L
            if trade['direction'] == 'BUY':
                # For buy: profit when price goes up
                pnl = (current_price - trade['entry_price']) * trade['size'] * 100000
            else:
                # For sell: profit when price goes down
                pnl = (trade['entry_price'] - current_price) * trade['size'] * 100000
            
            trade['unrealized_pnl'] = pnl
            
            # Check for stop loss/take profit hits
            if self._check_sl_tp_hit(trade, current_price):
                if trade['direction'] == 'BUY':
                    if current_price <= trade['stop_loss']:
                        positions_to_close.append((trade_id, 'stop_loss', trade['stop_loss']))
                    elif current_price >= trade['take_profit']:
                        positions_to_close.append((trade_id, 'take_profit', trade['take_profit']))
                else:  # SELL
                    if current_price >= trade['stop_loss']:
                        positions_to_close.append((trade_id, 'stop_loss', trade['stop_loss']))
                    elif current_price <= trade['take_profit']:
                        positions_to_close.append((trade_id, 'take_profit', trade['take_profit']))
        
        # Close positions that hit SL/TP
        for trade_id, reason, close_price in positions_to_close:
            self._close_position(trade_id, close_price, reason)
    
    def _check_sl_tp_hit(self, trade: Dict, current_price: float) -> bool:
        """Check if current price hit stop loss or take profit"""
        if trade['direction'] == 'BUY':
            return (current_price <= trade['stop_loss'] or 
                   current_price >= trade['take_profit'])
        else:
            return (current_price >= trade['stop_loss'] or 
                   current_price <= trade['take_profit'])
    
    def _close_position(self, trade_id: int, close_price: float, reason: str):
        """
        Close a position and update account
        
        Args:
            trade_id: ID of trade to close
            close_price: Closing price
            reason: Reason for closing ('stop_loss', 'take_profit', 'manual')
        """
        if trade_id not in self.positions:
            return
        
        trade = self.positions[trade_id]
        
        # Calculate execution price with spread
        if trade['direction'] == 'BUY':
            # Close buy at bid
            execution_price = close_price - self.config['spread_pips'] * 0.0001 / 2
        else:
            # Close sell at ask
            execution_price = close_price + self.config['spread_pips'] * 0.0001 / 2
        
        # Calculate final P&L
        if trade['direction'] == 'BUY':
            pnl = (execution_price - trade['entry_price']) * trade['size'] * 100000
        else:
            pnl = (trade['entry_price'] - execution_price) * trade['size'] * 100000
        
        # Calculate closing commission
        close_commission = self._calculate_commission(trade['size'])
        total_commission = trade['commission'] + close_commission
        
        # Net P&L after commissions
        net_pnl = pnl - total_commission
        
        # Update trade record
        trade.update({
            'close_price': execution_price,
            'close_time': self.current_time,
            'realized_pnl': net_pnl,
            'gross_pnl': pnl,
            'total_commission': total_commission,
            'status': 'closed',
            'close_reason': reason
        })
        
        # Update account balance
        self.balance += net_pnl
        
        # Update statistics
        self.total_trades += 1
        if net_pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # Move to trade history
        self.trade_history.append(trade)
        del self.positions[trade_id]
        
        # Log closing event
        self._log_event(TradeEvent(
            timestamp=self.current_time,
            symbol=trade['symbol'],
            event_type='position_closed',
            direction=trade['direction'],
            size=trade['size'],
            price=execution_price,
            strategy=trade['strategy'],
            details={
                'trade_id': trade_id,
                'reason': reason,
                'pnl': net_pnl,
                'duration': self.current_time - trade['entry_time']
            }
        ))
    
    # =========================================================================
    # ACCOUNT MANAGEMENT
    # =========================================================================
    
    def _update_account_metrics(self):
        """Update account equity and risk metrics"""
        # Calculate current equity
        unrealized_pnl = sum(pos['unrealized_pnl'] for pos in self.positions.values())
        self.equity = self.balance + unrealized_pnl
        
        # Update maximum equity and drawdown
        if self.equity > self.max_equity:
            self.max_equity = self.equity
        
        current_drawdown = (self.max_equity - self.equity) / self.max_equity * 100
        self.max_drawdown = max(self.max_drawdown, current_drawdown)
        
        # Calculate free margin
        used_margin = sum(
            abs(pos['size']) * pos['entry_price'] * 100000 / self.config['leverage'] 
            for pos in self.positions.values()
        )
        self.free_margin = self.equity - used_margin
    
    def _process_daily_swaps(self):
        """Process daily swap charges for open positions"""
        if not self.positions:
            return
        
        # Check if it's a new day (simplified - just check hour)
        if self.current_time.hour == 0 and self.current_time.minute == 0:
            total_swap = 0
            
            for trade in self.positions.values():
                if trade['direction'] == 'BUY':
                    swap = trade['size'] * self.config['swap_long']
                else:
                    swap = trade['size'] * self.config['swap_short']
                
                trade['swap'] = trade.get('swap', 0) + swap
                total_swap += swap
            
            # Deduct total swap from balance
            self.balance += total_swap
    
    # =========================================================================
    # VALIDATION AND UTILITIES
    # =========================================================================
    
    def _validate_backtest_data(self, data: pd.DataFrame) -> bool:
        """Validate that data is suitable for backtesting"""
        required_columns = ['open', 'high', 'low', 'close', 'datetime']
        indicator_columns = ['macd_main', 'macd_signal', 'bb_upper', 'bb_lower', 'rsi', 'adx', 'ema_200']
        
        # Check required columns
        missing_basic = [col for col in required_columns if col not in data.columns]
        if missing_basic:
            print(f"❌ Missing required columns: {missing_basic}")
            return False
        
        # Check indicator columns
        missing_indicators = [col for col in indicator_columns if col not in data.columns]
        if missing_indicators:
            print(f"⚠️ Missing indicator columns: {missing_indicators}")
            return False
        
        # Check for sufficient data
        if len(data) < 100:
            print(f"❌ Insufficient data: {len(data)} bars (minimum 100 required)")
            return False
        
        # Check for data quality
        nan_pct = data[required_columns].isnull().sum().sum() / (len(data) * len(required_columns)) * 100
        if nan_pct > 5:
            print(f"⚠️ High percentage of missing data: {nan_pct:.1f}%")
        
        return True
    
    def _filter_data_by_date(self, data: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """Filter data by date range"""
        mask = pd.Series(True, index=data.index)
        
        if start_date:
            start_dt = pd.to_datetime(start_date)
            mask &= data['datetime'] >= start_dt
        
        if end_date:
            end_dt = pd.to_datetime(end_date)
            mask &= data['datetime'] <= end_dt
        
        return data[mask].copy()
    
    def _can_execute_trade(self) -> bool:
        """Check if trade execution is possible given current market conditions"""
        # Check spread
        current_spread = self.config['spread_pips']
        if current_spread > self.config['max_spread_pips']:
            return False
        
        # Check free margin
        if self.free_margin < 100:  # Minimum margin requirement
            return False
        
        return True
    
    def _reset_engine_state(self):
        """Reset engine to initial state for new backtest"""
        self.balance = self.initial_balance
        self.equity = self.initial_balance
        self.free_margin = self.initial_balance
        
        self.positions.clear()
        self.trade_history.clear()
        self.events.clear()
        self.open_orders.clear()
        
        self.max_equity = self.initial_balance
        self.max_drawdown = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
        self.current_bar = None
        self.current_time = None
        self.bar_index = 0
    
    # =========================================================================
    # RESULTS AND REPORTING
    # =========================================================================
    
    def _calculate_final_results(self, strategy, start_time: datetime) -> Dict:
        """Calculate comprehensive backtest results"""
        end_time = datetime.now()
        runtime = end_time - start_time
        
        # Basic performance metrics
        total_return = (self.equity - self.initial_balance) / self.initial_balance * 100
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        # P&L analysis
        if self.trade_history:
            trade_df = pd.DataFrame(self.trade_history)
            gross_profit = trade_df[trade_df['realized_pnl'] > 0]['realized_pnl'].sum()
            gross_loss = abs(trade_df[trade_df['realized_pnl'] < 0]['realized_pnl'].sum())
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            avg_win = trade_df[trade_df['realized_pnl'] > 0]['realized_pnl'].mean() if self.winning_trades > 0 else 0
            avg_loss = trade_df[trade_df['realized_pnl'] < 0]['realized_pnl'].mean() if self.losing_trades > 0 else 0
            
            # Calculate Sharpe ratio (simplified)
            if len(trade_df) > 1:
                returns = trade_df['realized_pnl'] / self.initial_balance
                sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
            else:
                sharpe_ratio = 0
        else:
            gross_profit = gross_loss = profit_factor = avg_win = avg_loss = sharpe_ratio = 0
        
        # Strategy-specific metrics
        strategy_metrics = strategy.get_performance_metrics()
        
        return {
            # Performance
            'total_return': total_return,
            'final_balance': self.equity,
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'profit_factor': profit_factor,
            
            # Trading
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            
            # P&L
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,
            'net_profit': self.equity - self.initial_balance,
            
            # Execution
            'total_events': len(self.events),
            'open_positions': len(self.positions),
            
            # Runtime
            'backtest_duration': runtime.total_seconds(),
            'bars_processed': self.bar_index + 1,
            
            # Strategy metrics
            'strategy_metrics': strategy_metrics,
            
            # Trade history
            'trade_history': self.trade_history.copy(),
            'events': self.events.copy() if self.config['detailed_logging'] else []
        }
    
    # =========================================================================
    # EVENT LOGGING
    # =========================================================================
    
    def _log_event(self, event: TradeEvent):
        """Log a trading event"""
        self.events.append(event)
    
    def _log_bar_events(self, signal_info: Dict):
        """Log events for current bar"""
        if signal_info['signal'] != 0:
            self._log_event(TradeEvent(
                timestamp=self.current_time,
                symbol='EURUSD',
                event_type='signal',
                direction='BUY' if signal_info['signal'] > 0 else 'SELL',
                size=0,
                price=self.current_bar['close'],
                strategy=signal_info['strategy'],
                details={
                    'reason': signal_info['reason'],
                    'confidence': signal_info.get('confidence', 0)
                }
            ))
    
    def _process_pending_orders(self):
        """Process any pending orders (placeholder for future order types)"""
        # For now, we only use market orders, but this method provides
        # structure for implementing pending orders, stop orders, etc.
        pass


def main():
    """Example usage of BacktestEngine"""
    print("🚀 Backtest Engine Example")
    print("==========================")
    
    # Create engine with default settings
    engine = BacktestEngine(initial_balance=1000.0)
    
    print(f"✅ Engine initialized")
    print(f"💰 Initial balance: ${engine.balance:,.2f}")
    print(f"⚙️ Configuration: {len(engine.config)} parameters")
    
    # Show key configuration
    key_params = [
        'spread_pips', 'slippage_pips', 'commission_per_lot',
        'leverage', 'pip_value'
    ]
    
    print("\n🔧 Key parameters:")
    for param in key_params:
        value = engine.config.get(param, 'N/A')
        print(f"   {param}: {value}")


if __name__ == "__main__":
    main()