#!/usr/bin/env python3
"""
Walk-Forward Analysis
=====================

Walk-forward analysis system for robust parameter validation and strategy testing.
This provides out-of-sample validation to detect overfitting and assess real-world performance.

Features:
- Rolling window walk-forward analysis
- Anchored walk-forward analysis  
- Performance stability analysis
- Parameter drift detection
- Robust statistics calculation
- Visual performance reports

Walk-forward analysis is the gold standard for validating trading strategies,
ensuring parameters are robust and not overfit to historical data.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
from dataclasses import dataclass
from pathlib import Path
import json

@dataclass
class WalkForwardPeriod:
    """Represents a single walk-forward period"""
    period_id: int
    train_start: datetime
    train_end: datetime
    test_start: datetime
    test_end: datetime
    optimization_params: Dict
    backtest_results: Dict
    performance_metrics: Dict

class WalkForwardAnalyzer:
    """
    Walk-Forward Analysis system for strategy validation
    
    Performs systematic out-of-sample testing by:
    1. Optimizing parameters on training window
    2. Testing on subsequent out-of-sample period
    3. Rolling forward and repeating
    4. Analyzing performance stability
    """
    
    def __init__(self, data_loader, strategy_class, backtest_engine_class, 
                 optimizer_class, config: dict = None):
        """
        Initialize walk-forward analyzer
        
        Args:
            data_loader: DataLoader instance
            strategy_class: UltraStrategy class
            backtest_engine_class: BacktestEngine class
            optimizer_class: ParameterOptimizer class
            config: Walk-forward configuration
        """
        self.data_loader = data_loader
        self.strategy_class = strategy_class
        self.backtest_engine_class = backtest_engine_class
        self.optimizer_class = optimizer_class
        self.config = config or self._get_default_config()
        
        # Analysis state
        self.periods = []
        self.combined_results = {}
        self.stability_metrics = {}
        
        print(f"📈 Walk-Forward Analyzer initialized")
        print(f"🔄 Method: {self.config['method']}")
        print(f"📊 Training window: {self.config['train_window_months']} months")
        print(f"🎯 Test window: {self.config['test_window_months']} months")
    
    def _get_default_config(self) -> dict:
        """Get default walk-forward configuration"""
        return {
            # Walk-forward method
            'method': 'rolling',                    # 'rolling' or 'anchored'
            'train_window_months': 12,              # Training window size
            'test_window_months': 3,                # Test window size
            'step_months': 3,                       # Step size for rolling window
            
            # Optimization settings
            'optimization_trials': 100,            # Trials per optimization
            'optimization_timeout_hours': 1,       # Max time per optimization
            'min_trades_per_period': 5,            # Minimum trades for valid period
            
            # Analysis settings
            'stability_threshold': 0.3,            # Parameter stability threshold
            'performance_threshold': 0.05,         # Minimum acceptable return per period
            'max_drawdown_threshold': 15.0,        # Maximum acceptable drawdown per period
            
            # Output settings
            'save_detailed_results': True,         # Save all period results
            'create_performance_report': True,     # Generate performance report
            'export_combined_params': True,        # Export final parameter set
        }
    
    # =========================================================================
    # MAIN WALK-FORWARD INTERFACE
    # =========================================================================
    
    def run_walk_forward_analysis(self, symbol: str = 'EURUSD', timeframe: str = 'M15',
                                 start_date: str = None, end_date: str = None) -> Dict:
        """
        Run complete walk-forward analysis
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            start_date: Analysis start date (YYYY-MM-DD)
            end_date: Analysis end date (YYYY-MM-DD)
        
        Returns:
            dict: Complete walk-forward analysis results
        """
        print(f"🚀 Starting walk-forward analysis")
        print(f"📊 Symbol: {symbol} | Timeframe: {timeframe}")
        print(f"📅 Period: {start_date} to {end_date}")
        
        # Load full dataset
        data = self._load_full_dataset(symbol, timeframe, start_date, end_date)
        if data is None:
            raise ValueError("Failed to load data for walk-forward analysis")
        
        # Generate walk-forward periods
        periods = self._generate_walk_forward_periods(data)
        if not periods:
            raise ValueError("No valid walk-forward periods generated")
        
        print(f"📈 Generated {len(periods)} walk-forward periods")
        
        # Run analysis for each period
        start_time = datetime.now()
        
        for i, period_config in enumerate(periods):
            print(f"\n🔄 Processing period {i+1}/{len(periods)}")
            print(f"📅 Train: {period_config['train_start']} to {period_config['train_end']}")
            print(f"🎯 Test: {period_config['test_start']} to {period_config['test_end']}")
            
            period_result = self._run_single_period(period_config, data)
            
            if period_result:
                self.periods.append(period_result)
                print(f"✅ Period {i+1} completed - Return: {period_result.performance_metrics.get('return', 0):.2f}%")
            else:
                print(f"❌ Period {i+1} failed")
        
        # Analyze combined results
        self.combined_results = self._analyze_combined_results()
        
        # Calculate stability metrics
        self.stability_metrics = self._calculate_stability_metrics()
        
        # Generate final results
        final_results = self._generate_final_results(start_time)
        
        # Save results
        if self.config['save_detailed_results']:
            self._save_walk_forward_results(final_results, symbol, timeframe)
        
        # Create performance report
        if self.config['create_performance_report']:
            self._create_performance_report(final_results, symbol, timeframe)
        
        print(f"\n✅ Walk-forward analysis completed!")
        print(f"📊 Total periods: {len(self.periods)}")
        print(f"📈 Combined return: {final_results['combined_performance']['total_return']:.2f}%")
        print(f"🎯 Win rate: {final_results['combined_performance']['win_rate']:.1f}%")
        
        return final_results
    
    def _run_single_period(self, period_config: Dict, full_data: pd.DataFrame) -> Optional[WalkForwardPeriod]:
        """
        Run optimization and testing for a single walk-forward period
        
        Args:
            period_config: Period configuration
            full_data: Full dataset
        
        Returns:
            WalkForwardPeriod: Results for this period
        """
        try:
            # Extract training data
            train_mask = ((full_data['datetime'] >= period_config['train_start']) & 
                         (full_data['datetime'] <= period_config['train_end']))
            train_data = full_data[train_mask].copy()
            
            if len(train_data) < 500:  # Minimum training data
                print(f"⚠️ Insufficient training data: {len(train_data)} bars")
                return None
            
            # Run optimization on training data
            optimizer = self.optimizer_class(
                self.data_loader, self.strategy_class, self.backtest_engine_class,
                config={
                    'n_trials': self.config['optimization_trials'],
                    'timeout_hours': self.config['optimization_timeout_hours'],
                    'save_results': False,  # Don't save individual optimizations
                    'export_mt5_params': False
                }
            )
            
            # Manually set training data (bypass data loading)
            optimizer.train_data = train_data
            optimizer.validation_data = None
            optimizer.test_data = None
            
            # Run optimization
            opt_results = self._run_period_optimization(optimizer, train_data)
            
            if not opt_results or 'best_params' not in opt_results:
                print("❌ Optimization failed")
                return None
            
            # Extract test data
            test_mask = ((full_data['datetime'] >= period_config['test_start']) & 
                        (full_data['datetime'] <= period_config['test_end']))
            test_data = full_data[test_mask].copy()
            
            if len(test_data) < 50:  # Minimum test data
                print(f"⚠️ Insufficient test data: {len(test_data)} bars")
                return None
            
            # Test optimized parameters on out-of-sample data
            test_results = self._run_period_test(opt_results['best_params'], test_data)
            
            if not test_results:
                print("❌ Testing failed")
                return None
            
            # Validate results
            if not self._validate_period_results(test_results):
                print("❌ Period results failed validation")
                return None
            
            # Create period result
            period_result = WalkForwardPeriod(
                period_id=len(self.periods) + 1,
                train_start=period_config['train_start'],
                train_end=period_config['train_end'],
                test_start=period_config['test_start'], 
                test_end=period_config['test_end'],
                optimization_params=opt_results['best_params'],
                backtest_results=test_results,
                performance_metrics=self._extract_period_metrics(test_results)
            )
            
            return period_result
            
        except Exception as e:
            print(f"❌ Error in period processing: {e}")
            return None
    
    def _run_period_optimization(self, optimizer, train_data: pd.DataFrame) -> Dict:
        """Run optimization for a single period"""
        try:
            # Create a minimal objective function for this period
            def objective_wrapper(trial):
                return optimizer._objective_function(trial, train_data)
            
            # Create study
            import optuna
            study = optuna.create_study(direction='maximize')
            
            # Run optimization
            study.optimize(
                objective_wrapper,
                n_trials=self.config['optimization_trials'],
                timeout=self.config['optimization_timeout_hours'] * 3600
            )
            
            if not study.best_trial:
                return {}
            
            return {
                'best_params': study.best_trial.params,
                'best_value': study.best_value,
                'study': study
            }
            
        except Exception as e:
            print(f"❌ Optimization error: {e}")
            return {}
    
    def _run_period_test(self, params: Dict, test_data: pd.DataFrame) -> Dict:
        """Test parameters on out-of-sample data"""
        try:
            # Create strategy with optimized parameters
            strategy = self.strategy_class(params)
            
            # Create backtest engine
            engine = self.backtest_engine_class(initial_balance=1000.0)
            
            # Run backtest
            results = engine.run_backtest(test_data, strategy)
            
            return results
            
        except Exception as e:
            print(f"❌ Testing error: {e}")
            return {}
    
    # =========================================================================
    # PERIOD GENERATION
    # =========================================================================
    
    def _generate_walk_forward_periods(self, data: pd.DataFrame) -> List[Dict]:
        """Generate walk-forward period configurations"""
        periods = []
        
        data_start = data['datetime'].min()
        data_end = data['datetime'].max()
        
        print(f"📊 Data range: {data_start} to {data_end}")
        
        if self.config['method'] == 'rolling':
            periods = self._generate_rolling_periods(data_start, data_end)
        elif self.config['method'] == 'anchored':
            periods = self._generate_anchored_periods(data_start, data_end)
        else:
            raise ValueError(f"Unknown walk-forward method: {self.config['method']}")
        
        return periods
    
    def _generate_rolling_periods(self, data_start: datetime, data_end: datetime) -> List[Dict]:
        """Generate rolling window periods"""
        periods = []
        
        train_months = self.config['train_window_months']
        test_months = self.config['test_window_months']
        step_months = self.config['step_months']
        
        current_start = data_start
        
        while True:
            # Calculate period dates
            train_start = current_start
            train_end = train_start + timedelta(days=train_months * 30)
            test_start = train_end + timedelta(days=1)
            test_end = test_start + timedelta(days=test_months * 30)
            
            # Check if we have enough data
            if test_end > data_end:
                break
            
            periods.append({
                'train_start': train_start,
                'train_end': train_end,
                'test_start': test_start,
                'test_end': test_end
            })
            
            # Move forward by step size
            current_start = current_start + timedelta(days=step_months * 30)
        
        return periods
    
    def _generate_anchored_periods(self, data_start: datetime, data_end: datetime) -> List[Dict]:
        """Generate anchored window periods (fixed start date, expanding training window)"""
        periods = []
        
        test_months = self.config['test_window_months']
        step_months = self.config['step_months']
        min_train_months = self.config['train_window_months']
        
        # Fixed anchor point
        anchor_start = data_start
        
        # Start with minimum training window
        current_train_end = anchor_start + timedelta(days=min_train_months * 30)
        
        while True:
            test_start = current_train_end + timedelta(days=1)
            test_end = test_start + timedelta(days=test_months * 30)
            
            # Check if we have enough data
            if test_end > data_end:
                break
            
            periods.append({
                'train_start': anchor_start,
                'train_end': current_train_end,
                'test_start': test_start,
                'test_end': test_end
            })
            
            # Expand training window
            current_train_end = current_train_end + timedelta(days=step_months * 30)
        
        return periods
    
    # =========================================================================
    # RESULTS ANALYSIS
    # =========================================================================
    
    def _analyze_combined_results(self) -> Dict:
        """Analyze combined results across all periods"""
        if not self.periods:
            return {}
        
        # Combine all trade histories
        all_trades = []
        for period in self.periods:
            trade_history = period.backtest_results.get('trade_history', [])
            for trade in trade_history:
                trade['period_id'] = period.period_id
                all_trades.append(trade)
        
        if not all_trades:
            return {}
        
        # Calculate combined performance metrics
        trade_df = pd.DataFrame(all_trades)
        
        total_pnl = trade_df['realized_pnl'].sum()
        total_trades = len(trade_df)
        winning_trades = len(trade_df[trade_df['realized_pnl'] > 0])
        losing_trades = len(trade_df[trade_df['realized_pnl'] < 0])
        
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        gross_profit = trade_df[trade_df['realized_pnl'] > 0]['realized_pnl'].sum()
        gross_loss = abs(trade_df[trade_df['realized_pnl'] < 0]['realized_pnl'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Calculate period-by-period performance
        period_returns = [p.performance_metrics.get('return', 0) for p in self.periods]
        period_drawdowns = [p.performance_metrics.get('max_drawdown', 0) for p in self.periods]
        
        # Calculate statistics
        avg_return = np.mean(period_returns)
        std_return = np.std(period_returns)
        sharpe_ratio = avg_return / std_return if std_return > 0 else 0
        
        max_period_drawdown = max(period_drawdowns) if period_drawdowns else 0
        
        # Calculate consecutive wins/losses
        consecutive_stats = self._calculate_consecutive_stats(period_returns)
        
        return {
            'total_return': (1000 + total_pnl - 1000) / 1000 * 100,  # Assuming $1000 start
            'total_pnl': total_pnl,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,
            'avg_period_return': avg_return,
            'period_return_std': std_return,
            'sharpe_ratio': sharpe_ratio,
            'max_period_drawdown': max_period_drawdown,
            'periods_profitable': len([r for r in period_returns if r > 0]),
            'periods_total': len(period_returns),
            'consistency_score': len([r for r in period_returns if r > 0]) / len(period_returns) * 100,
            'consecutive_stats': consecutive_stats,
            'period_returns': period_returns
        }
    
    def _calculate_consecutive_stats(self, returns: List[float]) -> Dict:
        """Calculate consecutive wins/losses statistics"""
        if not returns:
            return {}
        
        # Convert to win/loss sequence
        sequence = ['W' if r > 0 else 'L' for r in returns]
        
        # Find consecutive runs
        consecutive_wins = []
        consecutive_losses = []
        current_run = 1
        
        for i in range(1, len(sequence)):
            if sequence[i] == sequence[i-1]:
                current_run += 1
            else:
                if sequence[i-1] == 'W':
                    consecutive_wins.append(current_run)
                else:
                    consecutive_losses.append(current_run)
                current_run = 1
        
        # Add final run
        if sequence:
            if sequence[-1] == 'W':
                consecutive_wins.append(current_run)
            else:
                consecutive_losses.append(current_run)
        
        return {
            'max_consecutive_wins': max(consecutive_wins) if consecutive_wins else 0,
            'max_consecutive_losses': max(consecutive_losses) if consecutive_losses else 0,
            'avg_consecutive_wins': np.mean(consecutive_wins) if consecutive_wins else 0,
            'avg_consecutive_losses': np.mean(consecutive_losses) if consecutive_losses else 0
        }
    
    def _calculate_stability_metrics(self) -> Dict:
        """Calculate parameter stability across periods"""
        if not self.periods:
            return {}
        
        # Collect all parameters
        all_params = {}
        for period in self.periods:
            for param_name, param_value in period.optimization_params.items():
                if param_name not in all_params:
                    all_params[param_name] = []
                all_params[param_name].append(param_value)
        
        # Calculate stability for each parameter
        stability_metrics = {}
        for param_name, values in all_params.items():
            if len(values) > 1:
                mean_val = np.mean(values)
                std_val = np.std(values)
                cv = std_val / mean_val if mean_val != 0 else float('inf')
                
                stability_metrics[param_name] = {
                    'mean': mean_val,
                    'std': std_val,
                    'min': min(values),
                    'max': max(values),
                    'coefficient_of_variation': cv,
                    'stable': cv < self.config['stability_threshold'],
                    'values': values
                }
        
        return stability_metrics
    
    # =========================================================================
    # VALIDATION AND UTILITIES
    # =========================================================================
    
    def _validate_period_results(self, results: Dict) -> bool:
        """Validate results for a single period"""
        # Check minimum trades
        if results.get('total_trades', 0) < self.config['min_trades_per_period']:
            return False
        
        # Check maximum drawdown
        if results.get('max_drawdown', 100) > self.config['max_drawdown_threshold']:
            return False
        
        # Check for valid final balance
        if results.get('final_balance', 0) <= 0:
            return False
        
        return True
    
    def _extract_period_metrics(self, results: Dict) -> Dict:
        """Extract key metrics for a period"""
        return {
            'return': results.get('total_return', 0),
            'max_drawdown': results.get('max_drawdown', 0),
            'profit_factor': results.get('profit_factor', 0),
            'sharpe_ratio': results.get('sharpe_ratio', 0),
            'total_trades': results.get('total_trades', 0),
            'win_rate': results.get('win_rate', 0),
            'final_balance': results.get('final_balance', 0)
        }
    
    def _load_full_dataset(self, symbol: str, timeframe: str, 
                          start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """Load complete dataset for walk-forward analysis"""
        try:
            data = self.data_loader.load_data(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date
            )
            
            if data is None or len(data) < 2000:
                print(f"❌ Insufficient data for walk-forward analysis: {len(data) if data is not None else 0} bars")
                return None
            
            # Calculate indicators
            from ..indicators import TechnicalIndicators
            indicators = TechnicalIndicators(data)
            data_with_indicators = indicators.calculate_all_indicators()
            
            print(f"📊 Loaded {len(data_with_indicators)} bars for walk-forward analysis")
            return data_with_indicators
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return None
    
    # =========================================================================
    # RESULTS GENERATION AND SAVING
    # =========================================================================
    
    def _generate_final_results(self, start_time: datetime) -> Dict:
        """Generate comprehensive final results"""
        return {
            'analysis_summary': {
                'method': self.config['method'],
                'total_periods': len(self.periods),
                'successful_periods': len([p for p in self.periods if p.performance_metrics.get('return', 0) > 0]),
                'analysis_duration': (datetime.now() - start_time).total_seconds(),
                'train_window_months': self.config['train_window_months'],
                'test_window_months': self.config['test_window_months']
            },
            'combined_performance': self.combined_results,
            'stability_analysis': self.stability_metrics,
            'period_details': [
                {
                    'period_id': p.period_id,
                    'train_period': f"{p.train_start.date()} to {p.train_end.date()}",
                    'test_period': f"{p.test_start.date()} to {p.test_end.date()}",
                    'parameters': p.optimization_params,
                    'performance': p.performance_metrics
                }
                for p in self.periods
            ],
            'recommended_parameters': self._calculate_recommended_parameters()
        }
    
    def _calculate_recommended_parameters(self) -> Dict:
        """Calculate recommended parameters based on stability and performance"""
        if not self.periods or not self.stability_metrics:
            return {}
        
        recommended = {}
        
        # For each parameter, use median of stable periods or weighted average
        for param_name, stability_info in self.stability_metrics.items():
            if stability_info['stable']:
                # Use median for stable parameters
                recommended[param_name] = np.median(stability_info['values'])
            else:
                # Use performance-weighted average for unstable parameters
                weights = [p.performance_metrics.get('return', 0) for p in self.periods]
                values = stability_info['values']
                
                if sum(weights) > 0:
                    # Normalize weights
                    weights = np.array(weights)
                    weights = np.maximum(weights, 0)  # No negative weights
                    weights = weights / sum(weights) if sum(weights) > 0 else np.ones_like(weights) / len(weights)
                    
                    recommended[param_name] = np.average(values, weights=weights)
                else:
                    recommended[param_name] = np.mean(values)
        
        return recommended
    
    def _save_walk_forward_results(self, results: Dict, symbol: str, timeframe: str):
        """Save walk-forward results to files"""
        results_dir = Path("walk_forward_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename_base = f"wf_{symbol}_{timeframe}_{timestamp}"
        
        # Save complete results
        results_file = results_dir / f"{filename_base}_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Save recommended parameters
        if self.config['export_combined_params']:
            params_file = results_dir / f"{filename_base}_recommended_params.json"
            with open(params_file, 'w') as f:
                json.dump(results['recommended_parameters'], f, indent=2)
        
        print(f"💾 Walk-forward results saved to {results_dir}")
    
    def _create_performance_report(self, results: Dict, symbol: str, timeframe: str):
        """Create a text-based performance report"""
        results_dir = Path("walk_forward_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = results_dir / f"wf_report_{symbol}_{timeframe}_{timestamp}.txt"
        
        with open(report_file, 'w') as f:
            f.write("WALK-FORWARD ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            # Summary
            summary = results['analysis_summary']
            f.write(f"Symbol: {symbol}\n")
            f.write(f"Timeframe: {timeframe}\n")
            f.write(f"Method: {summary['method']}\n")
            f.write(f"Total Periods: {summary['total_periods']}\n")
            f.write(f"Successful Periods: {summary['successful_periods']}\n")
            f.write(f"Success Rate: {summary['successful_periods']/summary['total_periods']*100:.1f}%\n\n")
            
            # Combined Performance
            perf = results['combined_performance']
            f.write("COMBINED PERFORMANCE\n")
            f.write("-" * 20 + "\n")
            f.write(f"Total Return: {perf.get('total_return', 0):.2f}%\n")
            f.write(f"Total Trades: {perf.get('total_trades', 0)}\n")
            f.write(f"Win Rate: {perf.get('win_rate', 0):.1f}%\n")
            f.write(f"Profit Factor: {perf.get('profit_factor', 0):.2f}\n")
            f.write(f"Sharpe Ratio: {perf.get('sharpe_ratio', 0):.2f}\n")
            f.write(f"Max Period Drawdown: {perf.get('max_period_drawdown', 0):.2f}%\n")
            f.write(f"Consistency Score: {perf.get('consistency_score', 0):.1f}%\n\n")
            
            # Parameter Stability
            f.write("PARAMETER STABILITY\n")
            f.write("-" * 20 + "\n")
            stability = results['stability_analysis']
            stable_params = [p for p, info in stability.items() if info.get('stable', False)]
            unstable_params = [p for p, info in stability.items() if not info.get('stable', False)]
            
            f.write(f"Stable Parameters ({len(stable_params)}): {', '.join(stable_params)}\n")
            f.write(f"Unstable Parameters ({len(unstable_params)}): {', '.join(unstable_params)}\n\n")
            
            # Recommended Parameters
            f.write("RECOMMENDED PARAMETERS\n")
            f.write("-" * 25 + "\n")
            for param, value in results['recommended_parameters'].items():
                f.write(f"{param}: {value:.4f}\n")
        
        print(f"📄 Performance report saved to {report_file}")


def main():
    """Example usage of WalkForwardAnalyzer"""
    print("📈 Walk-Forward Analyzer Example")
    print("=================================")
    
    print("✅ This is an example of how to use the WalkForwardAnalyzer")
    print("🔄 To use in practice:")
    print("   1. Load your data with DataLoader")
    print("   2. Import required classes")
    print("   3. Create analyzer instance")
    print("   4. Run run_walk_forward_analysis()")
    
    # Example configuration
    analyzer = WalkForwardAnalyzer(None, None, None, None)
    
    print(f"\n📊 Default configuration:")
    for key, value in analyzer.config.items():
        print(f"   {key}: {value}")


if __name__ == "__main__":
    main()