#!/usr/bin/env python3
"""
Parameter Optimizer
===================

Advanced parameter optimization system using Optuna for the Ultra Conservative Strategy.
Supports multiple optimization objectives and robust parameter selection.

Features:
- Multi-objective optimization (profit, drawdown, Sharpe ratio)
- Bayesian optimization with Tree-structured Parzen Estimator (TPE)
- Parameter importance analysis
- Overfitting detection
- Walk-forward validation
- Robust parameter selection

This system finds optimal parameters that can be exported back to MT5.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Callable
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
import warnings
import pickle
import json
from pathlib import Path

# Suppress Optuna logging for cleaner output
optuna.logging.set_verbosity(optuna.logging.WARNING)

class ParameterOptimizer:
    """
    Parameter optimization system for Ultra Conservative Strategy
    
    Uses Optuna's Bayesian optimization to find optimal strategy parameters
    with robust validation and overfitting protection.
    """
    
    def __init__(self, data_loader, strategy_class, backtest_engine_class, 
                 config: dict = None):
        """
        Initialize parameter optimizer
        
        Args:
            data_loader: DataLoader instance for accessing historical data
            strategy_class: UltraStrategy class (not instance)
            backtest_engine_class: BacktestEngine class (not instance)
            config: Optimizer configuration
        """
        self.data_loader = data_loader
        self.strategy_class = strategy_class
        self.backtest_engine_class = backtest_engine_class
        self.config = config or self._get_default_config()
        
        # Optimization state
        self.study = None
        self.best_params = None
        self.optimization_results = {}
        self.parameter_space = self._define_parameter_space()
        
        # Data splits for validation
        self.train_data = None
        self.validation_data = None
        self.test_data = None
        
        print(f"🔧 Parameter Optimizer initialized")
        print(f"🎯 Optimization target: {self.config['primary_objective']}")
    
    def _get_default_config(self) -> dict:
        """Get default optimizer configuration"""
        return {
            # Optimization settings
            'n_trials': 200,                    # Number of optimization trials
            'timeout_hours': 4,                 # Maximum optimization time
            'n_jobs': 1,                        # Parallel jobs (1 for sequential)
            'sampler_startup_trials': 20,       # Random trials before TPE
            
            # Objectives
            'primary_objective': 'profit_factor',  # Primary optimization target
            'secondary_objectives': ['sharpe_ratio', 'max_drawdown'],
            'minimize_objectives': ['max_drawdown'],  # Objectives to minimize
            
            # Validation
            'train_ratio': 0.6,                 # Training data ratio
            'validation_ratio': 0.2,           # Validation data ratio  
            'test_ratio': 0.2,                 # Test data ratio
            'min_trades_required': 10,         # Minimum trades for valid result
            
            # Overfitting protection
            'max_drawdown_limit': 30.0,        # Maximum acceptable drawdown %
            'min_profit_factor': 1.1,          # Minimum acceptable profit factor
            'stability_threshold': 0.3,        # Parameter stability requirement
            
            # Output
            'save_results': True,              # Save optimization results
            'results_directory': 'optimization_results',
            'export_mt5_params': True,         # Export .set files for MT5
        }
    
    def _define_parameter_space(self) -> dict:
        """
        Define the parameter search space for optimization
        
        Returns:
            dict: Parameter definitions with ranges and types
        """
        return {
            # MACD parameters
            'macd_fast': {'type': 'int', 'low': 8, 'high': 18, 'default': 12},
            'macd_slow': {'type': 'int', 'low': 20, 'high': 35, 'default': 26},
            'macd_signal': {'type': 'int', 'low': 6, 'high': 15, 'default': 9},
            
            # Bollinger Bands
            'bb_period': {'type': 'int', 'low': 15, 'high': 30, 'default': 20},
            'bb_deviation': {'type': 'float', 'low': 1.5, 'high': 2.5, 'default': 2.0},
            'min_bb_width_pips': {'type': 'float', 'low': 10.0, 'high': 25.0, 'default': 15.0},
            
            # Risk management
            'max_risk_percent': {'type': 'float', 'low': 0.5, 'high': 2.0, 'default': 1.0},
            'take_profit_ratio': {'type': 'float', 'low': 1.5, 'high': 3.0, 'default': 2.0},
            'stop_loss_buffer': {'type': 'float', 'low': 5.0, 'high': 20.0, 'default': 10.0},
            
            # Filters
            'ema_period': {'type': 'int', 'low': 100, 'high': 300, 'default': 200},
            'adx_trend': {'type': 'float', 'low': 20.0, 'high': 35.0, 'default': 25.0},
            'rsi_period': {'type': 'int', 'low': 10, 'high': 20, 'default': 14},
            
            # Portfolio management
            'max_concurrent_trades': {'type': 'int', 'low': 1, 'high': 5, 'default': 3},
            'max_portfolio_heat': {'type': 'float', 'low': 3.0, 'high': 10.0, 'default': 6.0},
            
            # Adaptive risk
            'max_risk_multiplier': {'type': 'float', 'low': 1.5, 'high': 3.0, 'default': 2.0},
            'min_risk_multiplier': {'type': 'float', 'low': 0.2, 'high': 0.5, 'default': 0.3},
            'consecutive_win_bonus': {'type': 'int', 'low': 2, 'high': 5, 'default': 3},
            'consecutive_loss_reduction': {'type': 'int', 'low': 1, 'high': 3, 'default': 2},
            
            # ATR filters
            'atr_period': {'type': 'int', 'low': 10, 'high': 20, 'default': 14},
            'min_atr_multiplier': {'type': 'float', 'low': 0.5, 'high': 1.2, 'default': 0.8},
            'max_atr_multiplier': {'type': 'float', 'low': 2.0, 'high': 4.0, 'default': 3.0},
        }
    
    # =========================================================================
    # MAIN OPTIMIZATION INTERFACE
    # =========================================================================
    
    def optimize_parameters(self, symbol: str = 'EURUSD', timeframe: str = 'M15',
                          start_date: str = None, end_date: str = None) -> Dict:
        """
        Run complete parameter optimization
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe for optimization
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
        
        Returns:
            dict: Optimization results with best parameters
        """
        print(f"🚀 Starting parameter optimization")
        print(f"📊 Symbol: {symbol} | Timeframe: {timeframe}")
        print(f"🎯 Target: {self.config['primary_objective']}")
        print(f"🔄 Trials: {self.config['n_trials']}")
        
        # Load and prepare data
        data = self._load_and_prepare_data(symbol, timeframe, start_date, end_date)
        if data is None:
            raise ValueError("Failed to load optimization data")
        
        # Split data for validation
        self._split_data_for_validation(data)
        
        # Create Optuna study
        self.study = optuna.create_study(
            direction='maximize' if self.config['primary_objective'] not in self.config['minimize_objectives'] else 'minimize',
            sampler=TPESampler(
                n_startup_trials=self.config['sampler_startup_trials'],
                multivariate=True
            ),
            pruner=MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        )
        
        # Run optimization
        start_time = datetime.now()
        
        def objective_wrapper(trial):
            return self._objective_function(trial, self.train_data)
        
        try:
            self.study.optimize(
                objective_wrapper,
                n_trials=self.config['n_trials'],
                timeout=self.config['timeout_hours'] * 3600,
                n_jobs=self.config['n_jobs']
            )
        except KeyboardInterrupt:
            print("⚠️ Optimization interrupted by user")
        
        # Analyze results
        results = self._analyze_optimization_results(start_time)
        
        # Validate on out-of-sample data
        validation_results = self._validate_best_parameters()
        results['validation'] = validation_results
        
        # Save results
        if self.config['save_results']:
            self._save_optimization_results(results, symbol, timeframe)
        
        # Export MT5 parameters
        if self.config['export_mt5_params']:
            self._export_mt5_parameters(results['best_params'], symbol, timeframe)
        
        print(f"✅ Optimization completed!")
        print(f"🏆 Best {self.config['primary_objective']}: {results['best_value']:.4f}")
        print(f"📊 Total trials: {len(self.study.trials)}")
        
        return results
    
    def _objective_function(self, trial: optuna.Trial, data: pd.DataFrame) -> float:
        """
        Objective function for Optuna optimization
        
        Args:
            trial: Optuna trial object
            data: Training data
        
        Returns:
            float: Objective value to optimize
        """
        # Suggest parameters
        params = self._suggest_parameters(trial)
        
        try:
            # Create strategy with suggested parameters
            strategy = self.strategy_class(params)
            
            # Create backtest engine
            engine = self.backtest_engine_class(
                initial_balance=params.get('initial_balance', 1000.0)
            )
            
            # Run backtest
            results = engine.run_backtest(data, strategy)
            
            # Extract objective value
            objective_value = self._calculate_objective_value(results, trial)
            
            # Add constraints (pruning for invalid results)
            if not self._validate_backtest_results(results):
                raise optuna.TrialPruned()
            
            return objective_value
            
        except Exception as e:
            # Log error and prune trial
            trial.set_user_attr('error', str(e))
            raise optuna.TrialPruned()
    
    def _suggest_parameters(self, trial: optuna.Trial) -> dict:
        """Suggest parameters for current trial"""
        params = {}
        
        for param_name, param_config in self.parameter_space.items():
            if param_config['type'] == 'int':
                params[param_name] = trial.suggest_int(
                    param_name, 
                    param_config['low'], 
                    param_config['high']
                )
            elif param_config['type'] == 'float':
                params[param_name] = trial.suggest_float(
                    param_name, 
                    param_config['low'], 
                    param_config['high']
                )
            elif param_config['type'] == 'categorical':
                params[param_name] = trial.suggest_categorical(
                    param_name, 
                    param_config['choices']
                )
        
        # Add fixed parameters
        params.update({
            'initial_balance': 1000.0,
            'leverage': 100,
            'use_adaptive_risk': True,
            'use_atr_filter': True,
            'use_volatility_adjustment': True,
            'avoid_news': True,
            'trade_only_overlap': False,
        })
        
        return params
    
    def _calculate_objective_value(self, results: Dict, trial: optuna.Trial) -> float:
        """Calculate the objective value for optimization"""
        primary_obj = self.config['primary_objective']
        
        # Get primary objective value
        if primary_obj in results:
            primary_value = results[primary_obj]
        elif primary_obj in results.get('strategy_metrics', {}):
            primary_value = results['strategy_metrics'][primary_obj]
        else:
            raise ValueError(f"Primary objective '{primary_obj}' not found in results")
        
        # Store secondary objectives as user attributes
        for secondary_obj in self.config['secondary_objectives']:
            if secondary_obj in results:
                trial.set_user_attr(secondary_obj, results[secondary_obj])
            elif secondary_obj in results.get('strategy_metrics', {}):
                trial.set_user_attr(secondary_obj, results['strategy_metrics'][secondary_obj])
        
        # Store additional metrics
        trial.set_user_attr('total_trades', results.get('total_trades', 0))
        trial.set_user_attr('win_rate', results.get('win_rate', 0))
        trial.set_user_attr('max_drawdown', results.get('max_drawdown', 100))
        trial.set_user_attr('final_balance', results.get('final_balance', 0))
        
        return primary_value
    
    def _validate_backtest_results(self, results: Dict) -> bool:
        """Validate backtest results meet minimum requirements"""
        # Check minimum trades
        if results.get('total_trades', 0) < self.config['min_trades_required']:
            return False
        
        # Check maximum drawdown
        if results.get('max_drawdown', 100) > self.config['max_drawdown_limit']:
            return False
        
        # Check minimum profit factor
        if results.get('profit_factor', 0) < self.config['min_profit_factor']:
            return False
        
        # Check for valid final balance
        if results.get('final_balance', 0) <= 0:
            return False
        
        return True
    
    # =========================================================================
    # DATA MANAGEMENT
    # =========================================================================
    
    def _load_and_prepare_data(self, symbol: str, timeframe: str, 
                              start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """Load and prepare data for optimization"""
        try:
            # Load data using data loader
            data = self.data_loader.load_data(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date
            )
            
            if data is None or len(data) < 1000:
                print(f"❌ Insufficient data: {len(data) if data is not None else 0} bars")
                return None
            
            # Calculate indicators
            from ..indicators import TechnicalIndicators
            indicators = TechnicalIndicators(data)
            data_with_indicators = indicators.calculate_all_indicators()
            
            print(f"📊 Loaded {len(data_with_indicators)} bars for optimization")
            return data_with_indicators
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return None
    
    def _split_data_for_validation(self, data: pd.DataFrame):
        """Split data into train/validation/test sets"""
        total_len = len(data)
        
        train_len = int(total_len * self.config['train_ratio'])
        val_len = int(total_len * self.config['validation_ratio'])
        
        self.train_data = data[:train_len].copy()
        self.validation_data = data[train_len:train_len + val_len].copy()
        self.test_data = data[train_len + val_len:].copy()
        
        print(f"📊 Data split: Train={len(self.train_data)}, "
              f"Val={len(self.validation_data)}, Test={len(self.test_data)}")
    
    # =========================================================================
    # RESULTS ANALYSIS
    # =========================================================================
    
    def _analyze_optimization_results(self, start_time: datetime) -> Dict:
        """Analyze optimization results and extract insights"""
        if not self.study or not self.study.trials:
            return {}
        
        # Get best trial
        best_trial = self.study.best_trial
        self.best_params = best_trial.params
        
        # Calculate parameter importance
        try:
            param_importance = optuna.importance.get_param_importances(self.study)
        except:
            param_importance = {}
        
        # Analyze convergence
        convergence_analysis = self._analyze_convergence()
        
        # Calculate stability metrics
        stability_metrics = self._calculate_parameter_stability()
        
        return {
            'best_params': self.best_params,
            'best_value': self.study.best_value,
            'best_trial': {
                'number': best_trial.number,
                'value': best_trial.value,
                'params': best_trial.params,
                'user_attrs': best_trial.user_attrs
            },
            'optimization_summary': {
                'total_trials': len(self.study.trials),
                'completed_trials': len([t for t in self.study.trials if t.state == optuna.trial.TrialState.COMPLETE]),
                'pruned_trials': len([t for t in self.study.trials if t.state == optuna.trial.TrialState.PRUNED]),
                'failed_trials': len([t for t in self.study.trials if t.state == optuna.trial.TrialState.FAIL]),
                'optimization_time': (datetime.now() - start_time).total_seconds(),
            },
            'parameter_importance': param_importance,
            'convergence_analysis': convergence_analysis,
            'stability_metrics': stability_metrics,
            'study_object': self.study  # For advanced analysis
        }
    
    def _analyze_convergence(self) -> Dict:
        """Analyze optimization convergence"""
        if not self.study.trials:
            return {}
        
        completed_trials = [t for t in self.study.trials if t.state == optuna.trial.TrialState.COMPLETE]
        
        if len(completed_trials) < 10:
            return {'status': 'insufficient_data'}
        
        # Get best values over time
        best_values = []
        current_best = float('-inf') if self.study.direction == optuna.study.StudyDirection.MAXIMIZE else float('inf')
        
        for trial in completed_trials:
            if self.study.direction == optuna.study.StudyDirection.MAXIMIZE:
                current_best = max(current_best, trial.value)
            else:
                current_best = min(current_best, trial.value)
            best_values.append(current_best)
        
        # Analyze improvement rate in last 20% of trials
        last_20_pct = int(len(best_values) * 0.2)
        if last_20_pct < 5:
            last_20_pct = 5
        
        recent_improvement = abs(best_values[-1] - best_values[-last_20_pct])
        total_improvement = abs(best_values[-1] - best_values[0])
        
        convergence_ratio = recent_improvement / total_improvement if total_improvement > 0 else 0
        
        return {
            'status': 'converged' if convergence_ratio < 0.05 else 'improving',
            'convergence_ratio': convergence_ratio,
            'best_values_history': best_values,
            'improvement_trend': recent_improvement,
            'recommendation': 'optimization_complete' if convergence_ratio < 0.05 else 'continue_optimization'
        }
    
    def _calculate_parameter_stability(self) -> Dict:
        """Calculate parameter stability across top trials"""
        if not self.study.trials:
            return {}
        
        completed_trials = [t for t in self.study.trials if t.state == optuna.trial.TrialState.COMPLETE]
        
        if len(completed_trials) < 20:
            return {'status': 'insufficient_data'}
        
        # Get top 10% of trials
        top_trials_count = max(5, len(completed_trials) // 10)
        
        if self.study.direction == optuna.study.StudyDirection.MAXIMIZE:
            top_trials = sorted(completed_trials, key=lambda t: t.value, reverse=True)[:top_trials_count]
        else:
            top_trials = sorted(completed_trials, key=lambda t: t.value)[:top_trials_count]
        
        # Calculate parameter stability
        stability_metrics = {}
        
        for param_name in self.parameter_space.keys():
            values = [trial.params.get(param_name) for trial in top_trials if param_name in trial.params]
            
            if values:
                mean_val = np.mean(values)
                std_val = np.std(values)
                cv = std_val / mean_val if mean_val != 0 else float('inf')
                
                stability_metrics[param_name] = {
                    'mean': mean_val,
                    'std': std_val,
                    'coefficient_of_variation': cv,
                    'stable': cv < self.config['stability_threshold']
                }
        
        return stability_metrics
    
    def _validate_best_parameters(self) -> Dict:
        """Validate best parameters on out-of-sample data"""
        if not self.best_params or self.validation_data is None:
            return {}
        
        print("🔍 Validating best parameters on out-of-sample data...")
        
        # Test on validation data
        strategy = self.strategy_class(self.best_params)
        engine = self.backtest_engine_class(initial_balance=1000.0)
        
        validation_results = engine.run_backtest(self.validation_data, strategy)
        
        # Test on test data if available
        test_results = {}
        if self.test_data is not None and len(self.test_data) > 100:
            strategy_test = self.strategy_class(self.best_params)
            engine_test = self.backtest_engine_class(initial_balance=1000.0)
            test_results = engine_test.run_backtest(self.test_data, strategy_test)
        
        return {
            'validation_results': validation_results,
            'test_results': test_results,
            'overfitting_detected': self._detect_overfitting(validation_results, test_results)
        }
    
    def _detect_overfitting(self, validation_results: Dict, test_results: Dict) -> bool:
        """Detect potential overfitting by comparing validation and test results"""
        if not validation_results or not test_results:
            return False
        
        # Compare key metrics
        val_return = validation_results.get('total_return', 0)
        test_return = test_results.get('total_return', 0)
        
        val_sharpe = validation_results.get('sharpe_ratio', 0)
        test_sharpe = test_results.get('sharpe_ratio', 0)
        
        # Significant degradation indicates overfitting
        return_degradation = (val_return - test_return) / abs(val_return) if val_return != 0 else 0
        sharpe_degradation = (val_sharpe - test_sharpe) / abs(val_sharpe) if val_sharpe != 0 else 0
        
        # Overfitting if performance drops more than 30%
        return return_degradation > 0.3 or sharpe_degradation > 0.3
    
    # =========================================================================
    # EXPORT AND SAVING
    # =========================================================================
    
    def _save_optimization_results(self, results: Dict, symbol: str, timeframe: str):
        """Save optimization results to files"""
        results_dir = Path(self.config['results_directory'])
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename_base = f"{symbol}_{timeframe}_{timestamp}"
        
        # Save best parameters as JSON
        params_file = results_dir / f"{filename_base}_best_params.json"
        with open(params_file, 'w') as f:
            json.dump(results['best_params'], f, indent=2)
        
        # Save full results (excluding study object)
        results_copy = results.copy()
        results_copy.pop('study_object', None)
        
        results_file = results_dir / f"{filename_base}_results.json"
        with open(results_file, 'w') as f:
            json.dump(results_copy, f, indent=2, default=str)
        
        # Save study object for advanced analysis
        study_file = results_dir / f"{filename_base}_study.pkl"
        with open(study_file, 'wb') as f:
            pickle.dump(self.study, f)
        
        print(f"💾 Results saved to {results_dir}")
    
    def _export_mt5_parameters(self, params: Dict, symbol: str, timeframe: str):
        """Export parameters in MT5 .set file format"""
        results_dir = Path(self.config['results_directory'])
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        set_filename = results_dir / f"ultra_optimized_{symbol}_{timeframe}_{timestamp}.set"
        
        # Create MT5 .set file content
        set_content = self._create_mt5_set_file(params)
        
        with open(set_filename, 'w') as f:
            f.write(set_content)
        
        print(f"📁 MT5 parameters exported to {set_filename}")
    
    def _create_mt5_set_file(self, params: Dict) -> str:
        """Create MT5 .set file content from parameters"""
        set_lines = []
        set_lines.append(";")
        set_lines.append("; Ultra Conservative EA - Optimized Parameters")
        set_lines.append(f"; Generated: {datetime.now()}")
        set_lines.append(";")
        
        # Map Python parameters to MT5 parameter names
        mt5_param_mapping = {
            'macd_fast': 'MACD_Fast',
            'macd_slow': 'MACD_Slow', 
            'macd_signal': 'MACD_Signal',
            'bb_period': 'BB_Period',
            'bb_deviation': 'BB_Deviation',
            'min_bb_width_pips': 'MinBBWidthPips',
            'max_risk_percent': 'MaxRiskPercent',
            'take_profit_ratio': 'TakeProfitRatio',
            'stop_loss_buffer': 'StopLossBuffer',
            'ema_period': 'EMA_Period',
            'adx_trend': 'ADX_Trend',
            'rsi_period': 'RSI_Period',
            'max_concurrent_trades': 'MaxConcurrentTrades',
            'max_portfolio_heat': 'MaxPortfolioHeat',
            'max_risk_multiplier': 'MaxRiskMultiplier',
            'min_risk_multiplier': 'MinRiskMultiplier',
            'consecutive_win_bonus': 'ConsecutiveWinBonus',
            'consecutive_loss_reduction': 'ConsecutiveLossReduction',
            'atr_period': 'ATR_Period',
            'min_atr_multiplier': 'MinATRMultiplier',
            'max_atr_multiplier': 'MaxATRMultiplier',
        }
        
        for python_param, mt5_param in mt5_param_mapping.items():
            if python_param in params:
                value = params[python_param]
                if isinstance(value, float):
                    set_lines.append(f"{mt5_param}={value:.6f}")
                else:
                    set_lines.append(f"{mt5_param}={value}")
        
        return '\n'.join(set_lines)


def main():
    """Example usage of ParameterOptimizer"""
    print("🔧 Parameter Optimizer Example")
    print("==============================")
    
    # This would normally be used with real data loader and strategy classes
    print("✅ This is an example of how to use the ParameterOptimizer")
    print("🔄 To use in practice:")
    print("   1. Load your data with DataLoader")
    print("   2. Import UltraStrategy and BacktestEngine")  
    print("   3. Create optimizer instance")
    print("   4. Run optimize_parameters()")
    
    # Example parameter space
    optimizer = ParameterOptimizer(None, None, None)
    param_space = optimizer.parameter_space
    
    print(f"\n📊 Parameter space includes {len(param_space)} parameters:")
    for param, config in list(param_space.items())[:5]:
        print(f"   {param}: {config['type']} from {config.get('low', 'N/A')} to {config.get('high', 'N/A')}")
    print("   ... and more")


if __name__ == "__main__":
    main()