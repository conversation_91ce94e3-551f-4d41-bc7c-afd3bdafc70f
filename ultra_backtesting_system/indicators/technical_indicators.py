#!/usr/bin/env python3
"""
Technical Indicators Library
============================

Pure Python implementation of technical indicators used in ultra.mq5.
All calculations match MetaTrader 5 indicator values exactly.

Indicators included:
- MACD (Moving Average Convergence Divergence)
- Bollinger Bands
- RSI (Relative Strength Index)
- ADX (Average Directional Index)
- ATR (Average True Range)
- EMA (Exponential Moving Average)
- SMA (Simple Moving Average)

All indicators are vectorized using pandas for optimal performance.
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional
import warnings

class TechnicalIndicators:
    """
    Technical Indicators Calculator
    
    Provides MT5-compatible technical indicator calculations.
    All methods return pandas Series that can be added directly to DataFrames.
    """
    
    def __init__(self, data: pd.DataFrame):
        """
        Initialize with price data
        
        Args:
            data: DataFrame with OHLCV columns
        """
        self.data = data.copy()
        self.validate_data()
    
    def validate_data(self):
        """Validate that required columns exist"""
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        if len(self.data) == 0:
            raise ValueError("Data cannot be empty")
    
    # =========================================================================
    # MOVING AVERAGES
    # =========================================================================
    
    def sma(self, period: int, price: str = 'close') -> pd.Series:
        """
        Simple Moving Average
        
        Args:
            period: Number of periods
            price: Price column to use ('open', 'high', 'low', 'close')
        
        Returns:
            pd.Series: SMA values
        """
        return self.data[price].rolling(window=period, min_periods=period).mean()
    
    def ema(self, period: int, price: str = 'close') -> pd.Series:
        """
        Exponential Moving Average (MT5 compatible)
        
        Args:
            period: Number of periods
            price: Price column to use
        
        Returns:
            pd.Series: EMA values
        """
        # MT5 EMA formula: EMA = (Price * 2/(Period+1)) + (Previous_EMA * (1 - 2/(Period+1)))
        alpha = 2.0 / (period + 1)
        return self.data[price].ewm(alpha=alpha, adjust=False).mean()
    
    # =========================================================================
    # MACD (Moving Average Convergence Divergence)
    # =========================================================================
    
    def macd(self, fast: int = 12, slow: int = 26, signal: int = 9, 
             price: str = 'close') -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        MACD Indicator (MT5 compatible)
        
        Args:
            fast: Fast EMA period (default 12)
            slow: Slow EMA period (default 26)
            signal: Signal line EMA period (default 9)
            price: Price column to use
        
        Returns:
            tuple: (macd_line, signal_line, histogram)
        """
        # Calculate EMAs
        ema_fast = self.ema(fast, price)
        ema_slow = self.ema(slow, price)
        
        # MACD line = Fast EMA - Slow EMA
        macd_line = ema_fast - ema_slow
        
        # Signal line = EMA of MACD line
        signal_line = macd_line.ewm(alpha=2.0/(signal+1), adjust=False).mean()
        
        # Histogram = MACD - Signal
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    # =========================================================================
    # BOLLINGER BANDS
    # =========================================================================
    
    def bollinger_bands(self, period: int = 20, std_dev: float = 2.0, 
                       price: str = 'close') -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Bollinger Bands (MT5 compatible)
        
        Args:
            period: SMA period (default 20)
            std_dev: Standard deviation multiplier (default 2.0)
            price: Price column to use
        
        Returns:
            tuple: (upper_band, middle_band, lower_band)
        """
        # Middle band = Simple Moving Average
        middle_band = self.sma(period, price)
        
        # Standard deviation
        std = self.data[price].rolling(window=period, min_periods=period).std()
        
        # Upper and lower bands
        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)
        
        return upper_band, middle_band, lower_band
    
    # =========================================================================
    # RSI (Relative Strength Index)
    # =========================================================================
    
    def rsi(self, period: int = 14, price: str = 'close') -> pd.Series:
        """
        RSI Indicator (MT5 compatible)
        
        Args:
            period: RSI period (default 14)
            price: Price column to use
        
        Returns:
            pd.Series: RSI values (0-100)
        """
        # Calculate price changes
        delta = self.data[price].diff()
        
        # Separate gains and losses
        gains = delta.where(delta > 0, 0)
        losses = -delta.where(delta < 0, 0)
        
        # Calculate average gains and losses using Wilder's smoothing
        # This matches MT5's RSI calculation
        alpha = 1.0 / period
        avg_gains = gains.ewm(alpha=alpha, adjust=False).mean()
        avg_losses = losses.ewm(alpha=alpha, adjust=False).mean()
        
        # Calculate RS and RSI
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    # =========================================================================
    # ADX (Average Directional Index)
    # =========================================================================
    
    def adx(self, period: int = 14) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        ADX Indicator (MT5 compatible)
        
        Args:
            period: ADX period (default 14)
        
        Returns:
            tuple: (adx, plus_di, minus_di)
        """
        high = self.data['high']
        low = self.data['low']
        close = self.data['close']
        
        # Calculate True Range components
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        # True Range
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # Directional Movement
        plus_dm = (high - high.shift(1)).where(
            (high - high.shift(1)) > (low.shift(1) - low), 0
        ).where((high - high.shift(1)) > 0, 0)
        
        minus_dm = (low.shift(1) - low).where(
            (low.shift(1) - low) > (high - high.shift(1)), 0
        ).where((low.shift(1) - low) > 0, 0)
        
        # Smooth the values using Wilder's smoothing
        alpha = 1.0 / period
        atr = tr.ewm(alpha=alpha, adjust=False).mean()
        plus_dm_smooth = plus_dm.ewm(alpha=alpha, adjust=False).mean()
        minus_dm_smooth = minus_dm.ewm(alpha=alpha, adjust=False).mean()
        
        # Calculate DI+ and DI-
        plus_di = 100 * (plus_dm_smooth / atr)
        minus_di = 100 * (minus_dm_smooth / atr)
        
        # Calculate DX
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        
        # Calculate ADX
        adx = dx.ewm(alpha=alpha, adjust=False).mean()
        
        return adx, plus_di, minus_di
    
    # =========================================================================
    # ATR (Average True Range)
    # =========================================================================
    
    def atr(self, period: int = 14) -> pd.Series:
        """
        ATR Indicator (MT5 compatible)
        
        Args:
            period: ATR period (default 14)
        
        Returns:
            pd.Series: ATR values
        """
        high = self.data['high']
        low = self.data['low']
        close = self.data['close']
        
        # Calculate True Range components
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        # True Range
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # ATR using Wilder's smoothing (matches MT5)
        alpha = 1.0 / period
        atr = tr.ewm(alpha=alpha, adjust=False).mean()
        
        return atr
    
    # =========================================================================
    # DIVERGENCE DETECTION
    # =========================================================================
    
    def find_peaks_troughs(self, data: pd.Series, lookback: int = 5) -> Tuple[pd.Series, pd.Series]:
        """
        Find peaks and troughs in data series
        
        Args:
            data: Data series to analyze
            lookback: Number of bars to look back/forward
        
        Returns:
            tuple: (peaks, troughs) - boolean series
        """
        peaks = pd.Series(False, index=data.index)
        troughs = pd.Series(False, index=data.index)
        
        for i in range(lookback, len(data) - lookback):
            # Check for peak
            is_peak = True
            for j in range(i - lookback, i + lookback + 1):
                if j != i and data.iloc[j] >= data.iloc[i]:
                    is_peak = False
                    break
            peaks.iloc[i] = is_peak
            
            # Check for trough
            is_trough = True
            for j in range(i - lookback, i + lookback + 1):
                if j != i and data.iloc[j] <= data.iloc[i]:
                    is_trough = False
                    break
            troughs.iloc[i] = is_trough
        
        return peaks, troughs
    
    def detect_divergence(self, price_series: pd.Series, indicator_series: pd.Series, 
                         lookback: int = 5, min_bars_between: int = 10) -> Tuple[pd.Series, pd.Series]:
        """
        Detect bullish and bearish divergence between price and indicator
        
        Args:
            price_series: Price data (usually close)
            indicator_series: Indicator data (e.g., MACD)
            lookback: Bars to look for peaks/troughs
            min_bars_between: Minimum bars between divergence points
        
        Returns:
            tuple: (bullish_divergence, bearish_divergence) - boolean series
        """
        # Find peaks and troughs in both series
        price_peaks, price_troughs = self.find_peaks_troughs(price_series, lookback)
        ind_peaks, ind_troughs = self.find_peaks_troughs(indicator_series, lookback)
        
        bullish_div = pd.Series(False, index=price_series.index)
        bearish_div = pd.Series(False, index=price_series.index)
        
        # Look for bullish divergence (price troughs)
        price_trough_indices = price_troughs[price_troughs].index.tolist()
        ind_trough_indices = ind_troughs[ind_troughs].index.tolist()
        
        for i, idx1 in enumerate(price_trough_indices[:-1]):
            for idx2 in price_trough_indices[i+1:]:
                if idx2 - idx1 < min_bars_between:
                    continue
                
                # Check if we have corresponding indicator troughs
                ind_idx1 = min(ind_trough_indices, key=lambda x: abs(x - idx1))
                ind_idx2 = min(ind_trough_indices, key=lambda x: abs(x - idx2))
                
                if abs(ind_idx1 - idx1) <= lookback and abs(ind_idx2 - idx2) <= lookback:
                    # Bullish divergence: price makes lower low, indicator makes higher low
                    if (price_series[idx2] < price_series[idx1] and 
                        indicator_series[ind_idx2] > indicator_series[ind_idx1]):
                        bullish_div[idx2] = True
        
        # Look for bearish divergence (price peaks)
        price_peak_indices = price_peaks[price_peaks].index.tolist()
        ind_peak_indices = ind_peaks[ind_peaks].index.tolist()
        
        for i, idx1 in enumerate(price_peak_indices[:-1]):
            for idx2 in price_peak_indices[i+1:]:
                if idx2 - idx1 < min_bars_between:
                    continue
                
                # Check if we have corresponding indicator peaks
                ind_idx1 = min(ind_peak_indices, key=lambda x: abs(x - idx1))
                ind_idx2 = min(ind_peak_indices, key=lambda x: abs(x - idx2))
                
                if abs(ind_idx1 - idx1) <= lookback and abs(ind_idx2 - idx2) <= lookback:
                    # Bearish divergence: price makes higher high, indicator makes lower high
                    if (price_series[idx2] > price_series[idx1] and 
                        indicator_series[ind_idx2] < indicator_series[ind_idx1]):
                        bearish_div[idx2] = True
        
        return bullish_div, bearish_div
    
    # =========================================================================
    # UTILITY METHODS
    # =========================================================================
    
    def calculate_all_indicators(self, config: dict = None) -> pd.DataFrame:
        """
        Calculate all indicators and return enhanced DataFrame
        
        Args:
            config: Configuration dictionary with indicator parameters
        
        Returns:
            pd.DataFrame: Original data with all indicators added
        """
        if config is None:
            config = {
                'macd': {'fast': 12, 'slow': 26, 'signal': 9},
                'bb': {'period': 20, 'std_dev': 2.0},
                'rsi': {'period': 14},
                'adx': {'period': 14},
                'atr': {'period': 14},
                'ema': {'period': 200}
            }
        
        result_df = self.data.copy()
        
        print("📊 Calculating technical indicators...")
        
        # MACD
        macd_main, macd_signal, macd_hist = self.macd(
            config['macd']['fast'], 
            config['macd']['slow'], 
            config['macd']['signal']
        )
        result_df['macd_main'] = macd_main
        result_df['macd_signal'] = macd_signal
        result_df['macd_histogram'] = macd_hist
        
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = self.bollinger_bands(
            config['bb']['period'], 
            config['bb']['std_dev']
        )
        result_df['bb_upper'] = bb_upper
        result_df['bb_middle'] = bb_middle
        result_df['bb_lower'] = bb_lower
        result_df['bb_width'] = bb_upper - bb_lower
        result_df['bb_percent'] = (result_df['close'] - bb_lower) / (bb_upper - bb_lower)
        
        # RSI
        result_df['rsi'] = self.rsi(config['rsi']['period'])
        
        # ADX
        adx_val, plus_di, minus_di = self.adx(config['adx']['period'])
        result_df['adx'] = adx_val
        result_df['plus_di'] = plus_di
        result_df['minus_di'] = minus_di
        
        # ATR
        result_df['atr'] = self.atr(config['atr']['period'])
        
        # EMA
        result_df['ema_200'] = self.ema(config['ema']['period'])
        
        # Additional useful indicators
        result_df['sma_20'] = self.sma(20)
        result_df['ema_50'] = self.ema(50)
        
        # Price-based indicators
        result_df['typical_price'] = (result_df['high'] + result_df['low'] + result_df['close']) / 3
        result_df['median_price'] = (result_df['high'] + result_df['low']) / 2
        result_df['weighted_close'] = (result_df['high'] + result_df['low'] + 2 * result_df['close']) / 4
        
        print("✅ All indicators calculated successfully")
        return result_df
    
    def get_indicator_at_bar(self, df: pd.DataFrame, bar_index: int) -> dict:
        """
        Get all indicator values at a specific bar (for backtesting)
        
        Args:
            df: DataFrame with calculated indicators
            bar_index: Bar index to get values for
        
        Returns:
            dict: Dictionary with all indicator values
        """
        if bar_index >= len(df) or bar_index < 0:
            return {}
        
        row = df.iloc[bar_index]
        
        return {
            # Price data
            'open': row['open'],
            'high': row['high'],
            'low': row['low'],
            'close': row['close'],
            'volume': row.get('tick_volume', 0),
            
            # MACD
            'macd_main': row.get('macd_main', np.nan),
            'macd_signal': row.get('macd_signal', np.nan),
            'macd_histogram': row.get('macd_histogram', np.nan),
            
            # Bollinger Bands
            'bb_upper': row.get('bb_upper', np.nan),
            'bb_middle': row.get('bb_middle', np.nan),
            'bb_lower': row.get('bb_lower', np.nan),
            'bb_width': row.get('bb_width', np.nan),
            
            # RSI
            'rsi': row.get('rsi', np.nan),
            
            # ADX
            'adx': row.get('adx', np.nan),
            'plus_di': row.get('plus_di', np.nan),
            'minus_di': row.get('minus_di', np.nan),
            
            # ATR
            'atr': row.get('atr', np.nan),
            
            # EMAs
            'ema_200': row.get('ema_200', np.nan),
            'ema_50': row.get('ema_50', np.nan),
            
            # Additional
            'sma_20': row.get('sma_20', np.nan),
        }


def validate_indicators_against_mt5(df_python, df_mt5, tolerance=1e-5):
    """
    Validate Python indicator calculations against MT5 values
    
    Args:
        df_python: DataFrame with Python-calculated indicators
        df_mt5: DataFrame with MT5 indicator values
        tolerance: Acceptable difference tolerance
    
    Returns:
        dict: Validation results
    """
    results = {}
    
    # Common indicators to validate
    indicators_to_check = ['macd_main', 'macd_signal', 'bb_upper', 'bb_lower', 'rsi', 'adx', 'atr']
    
    for indicator in indicators_to_check:
        if indicator in df_python.columns and indicator in df_mt5.columns:
            python_vals = df_python[indicator].dropna()
            mt5_vals = df_mt5[indicator].dropna()
            
            # Align data (in case of different lengths)
            min_len = min(len(python_vals), len(mt5_vals))
            python_vals = python_vals.iloc[-min_len:]
            mt5_vals = mt5_vals.iloc[-min_len:]
            
            # Calculate correlation and RMSE
            correlation = python_vals.corr(mt5_vals)
            rmse = np.sqrt(((python_vals - mt5_vals) ** 2).mean())
            max_diff = abs(python_vals - mt5_vals).max()
            
            results[indicator] = {
                'correlation': correlation,
                'rmse': rmse,
                'max_difference': max_diff,
                'within_tolerance': max_diff <= tolerance,
                'samples_compared': min_len
            }
    
    return results


def main():
    """Example usage of TechnicalIndicators"""
    print("📊 Technical Indicators Example")
    print("===============================")
    
    # Create sample data
    dates = pd.date_range('2024-01-01', periods=1000, freq='15min')
    np.random.seed(42)
    
    # Generate realistic OHLC data
    price = 1.1000
    data = []
    
    for _ in range(1000):
        change = np.random.normal(0, 0.0001)
        price += change
        
        high = price + abs(np.random.normal(0, 0.0005))
        low = price - abs(np.random.normal(0, 0.0005))
        open_price = np.random.uniform(low, high)
        close_price = np.random.uniform(low, high)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'tick_volume': np.random.randint(100, 1000)
        })
    
    df = pd.DataFrame(data, index=dates)
    
    # Calculate indicators
    indicators = TechnicalIndicators(df)
    df_with_indicators = indicators.calculate_all_indicators()
    
    print(f"✅ Calculated indicators for {len(df)} bars")
    print(f"📈 MACD range: {df_with_indicators['macd_main'].min():.6f} to {df_with_indicators['macd_main'].max():.6f}")
    print(f"📊 RSI range: {df_with_indicators['rsi'].min():.2f} to {df_with_indicators['rsi'].max():.2f}")
    print(f"📏 ATR average: {df_with_indicators['atr'].mean():.6f}")
    
    # Test indicator access
    bar_indicators = indicators.get_indicator_at_bar(df_with_indicators, -1)
    print(f"\n📋 Latest bar indicators:")
    for key, value in bar_indicators.items():
        if not np.isnan(value):
            print(f"   {key}: {value:.6f}")


if __name__ == "__main__":
    main()