# 📊 MT5 Symbols Window Export Guide (Wine on Mac)

This guide shows you how to export EURUSD data using MT5's **View → Symbols** method, which is the modern way to download and export historical data.

## 🎯 **Method 1: Symbols Window Export (Recommended)**

### **STEP 1: Open Symbols Window**
```
1. Open MT5 through Wine
2. Go to View → Symbols
3. Or press Ctrl+U
4. The Symbols window opens
```

### **STEP 2: Find and Download EURUSD Data**
```
1. In Symbols window, expand "Forex" folder
2. Find "EURUSD" in the list
3. Right-click on EURUSD
4. Select "Charts" or "Specification"
5. Look for "Download bars" or "Update history"
6. Click it to download recent data
```

### **STEP 3: Open EURUSD Chart**
```
1. Double-click EURUSD in Symbols window
2. This opens a new chart window
3. Change timeframe to M30 (30 minutes)
   - Look for timeframe buttons: M1, M5, M15, M30, H1, H4, D1
   - Click M30
```

### **STEP 4: Export Chart Data**
```
1. Right-click anywhere on the chart
2. Look for one of these options:
   - "Save as"
   - "Export data"
   - "Data Window" → then export
   - "Template" → "Save template"
3. Choose CSV format
4. Select date range (last 6-12 months)
5. Save as: EURUSD_M30_20230601_to_20241201.csv
```

## 🎯 **Method 2: Market Watch Export**

### **Alternative if Symbols doesn't work:**
```
1. Look for "Market Watch" window (usually on left side)
2. Right-click in Market Watch area
3. Select "Symbols" 
4. Find EURUSD and add it if not visible
5. Right-click EURUSD in Market Watch
6. Select "Chart Window" → M30
7. Follow export steps from Method 1
```

## 🎯 **Method 3: Direct Chart Export**

### **If you already have EURUSD chart open:**
```
1. Make sure chart is set to M30 timeframe
2. Right-click on chart
3. Select "Properties" or "Data Window"
4. Look for export options
5. Or try: Chart → Template → Save Template
6. Choose CSV format
```

## 📊 **What to Look For in MT5 Interface**

### **Common Menu Locations:**
- **View → Symbols** (main method)
- **Insert → Indicators → Custom** (sometimes has export tools)
- **Tools → Options → Charts** (chart settings)
- **File → Save As** (when chart is active)

### **Right-Click Context Menus:**
- **On Symbol in list**: Download, Charts, Specification
- **On Chart**: Save As, Export, Properties, Data Window
- **On Market Watch**: Symbols, Show All, Hide

## 🔍 **Troubleshooting Common Issues**

### **Problem: "No Export Option"**
**Solutions:**
```
1. Try different right-click locations on chart
2. Check File menu when chart is active
3. Look in Tools menu for export options
4. Try Chart → Template → Save Template
```

### **Problem: "No Data Downloaded"**
**Solutions:**
```
1. Check internet connection in Wine
2. Right-click symbol → "Download bars"
3. Go to Tools → Options → Charts
4. Increase "Max bars in history"
5. Try different timeframes first (H1, then M30)
```

### **Problem: "Can't Find CSV Option"**
**Solutions:**
```
1. Look for "Text file" or "Data file" options
2. Try saving as .txt then rename to .csv
3. Use "Template" save option
4. Check if there's a "Data Window" with export
```

## 📁 **Expected File Locations in Wine**

Your exported files will likely be saved to:
```
~/.wine/drive_c/users/[username]/Desktop/
~/.wine/drive_c/users/[username]/Documents/
~/.wine/drive_c/users/[username]/Downloads/
```

## 📊 **Verify Your Export**

### **Good CSV file should have:**
```csv
Date,Time,Open,High,Low,Close,Volume
2023.06.01,00:00,1.07000,1.07200,1.06950,1.07100,1250
2023.06.01,00:30,1.07100,1.07250,1.07050,1.07150,1180
2023.06.01,01:00,1.07150,1.07300,1.07100,1.07200,1320
```

### **Check for:**
- ✅ **Date column** (YYYY.MM.DD format)
- ✅ **Time column** (HH:MM format)
- ✅ **OHLC columns** (Open, High, Low, Close)
- ✅ **Volume column** (any positive numbers)
- ✅ **At least 1000+ rows** (for 6+ months of M30 data)

## 🚀 **Complete Workflow**

### **Phase 1: Export from MT5 (Wine)**
```
1. View → Symbols → EURUSD
2. Download bars/Update history
3. Open M30 chart
4. Right-click → Export → CSV
5. Save as: EURUSD_M30_20230601_to_20241201.csv
```

### **Phase 2: Import on Mac**
```bash
cd /Users/<USER>/day_trading
python quick_start.py
# Choose option 1: 🍷 Import Wine MT5 Data & Analyze
```

## 💡 **Pro Tips**

### **For Better Data Quality:**
1. **Download recent data first** - Right-click symbol → Download bars
2. **Check multiple timeframes** - Ensure M30 data is complete
3. **Verify date range** - Should cover last 6-12 months
4. **Check file size** - M30 data for 6 months should be 1-5 MB

### **If Export Fails:**
1. **Try different save locations** (Desktop, Documents, C:\)
2. **Use shorter date ranges** (3 months instead of 12)
3. **Try different file formats** (.txt, .dat, then rename)
4. **Check Wine permissions** for file writing

### **Alternative Data Sources:**
If MT5 export still doesn't work:
1. **Use Yahoo Finance fallback** (built into the system)
2. **Try Alpha Vantage API** (test with: `python test_api_key.py`)
3. **Contact your broker** for historical data files

## 📞 **Quick Commands After Export**

```bash
# Find exported files in Wine
find ~/.wine -name "*EURUSD*.csv" -type f

# Copy to day_trading folder
cp ~/.wine/drive_c/users/*/Desktop/EURUSD*.csv /Users/<USER>/day_trading/

# Run import and analysis
python quick_start.py
```

## 🎯 **Expected Results**

After successful export and import:
- **Real broker data** for accurate backtesting
- **Optimal parameters** based on your actual trading conditions
- **Performance metrics** that match live trading environment

The Symbols window method often works better than History Center in modern MT5 versions, so this should give you exactly what you need! 📊
