#!/usr/bin/env python3
"""
Import MT5 Data from Wine Environment
Specifically designed for Mac users running MT5 through Wine
"""

import os
import glob
import shutil
from pathlib import Path
from config import Config
from mt5_data_importer import MT5DataImporter

def find_wine_csv_files():
    """Find CSV files in Wine environment"""
    
    print("🍷 Searching for CSV files in Wine environment...")
    
    # Common Wine locations
    wine_paths = [
        os.path.expanduser("~/.wine/drive_c/users/*/Desktop/"),
        os.path.expanduser("~/.wine/drive_c/users/*/Documents/"),
        os.path.expanduser("~/.wine/drive_c/users/*/Downloads/"),
        os.path.expanduser("~/.wine/drive_c/Program Files/MetaTrader 5/"),
        os.path.expanduser("~/.wine/drive_c/Program Files (x86)/MetaTrader 5/"),
        os.path.expanduser("~/.wine/drive_c/"),
    ]
    
    found_files = []
    
    for path_pattern in wine_paths:
        try:
            # Expand wildcards in path
            expanded_paths = glob.glob(path_pattern)
            
            for path in expanded_paths:
                if os.path.isdir(path):
                    # Look for CSV files in this directory
                    csv_files = glob.glob(os.path.join(path, "*.csv"))
                    found_files.extend(csv_files)
                    
                    # Also check subdirectories
                    for subdir in ["MQL5", "Files", "Data"]:
                        subpath = os.path.join(path, subdir)
                        if os.path.isdir(subpath):
                            csv_files = glob.glob(os.path.join(subpath, "*.csv"))
                            found_files.extend(csv_files)
        except Exception as e:
            print(f"Error searching {path_pattern}: {e}")
    
    # Remove duplicates and filter for likely MT5 exports
    unique_files = list(set(found_files))
    mt5_files = []
    
    for file in unique_files:
        filename = os.path.basename(file).upper()
        # Look for forex-related CSV files
        if any(symbol in filename for symbol in ['EURUSD', 'GBPUSD', 'USDJPY', 'FOREX']) or \
           any(tf in filename for tf in ['M30', 'H1', 'M15', 'H4']) or \
           'OHLC' in filename or len(filename) > 10:  # Likely data export
            mt5_files.append(file)
    
    return mt5_files

def copy_wine_files_to_trading_folder(wine_files):
    """Copy Wine CSV files to day_trading folder"""
    
    current_dir = os.getcwd()
    copied_files = []
    
    print(f"\n📁 Copying files to: {current_dir}")
    
    for wine_file in wine_files:
        try:
            filename = os.path.basename(wine_file)
            destination = os.path.join(current_dir, filename)
            
            # Avoid overwriting existing files
            if os.path.exists(destination):
                base, ext = os.path.splitext(filename)
                counter = 1
                while os.path.exists(destination):
                    destination = os.path.join(current_dir, f"{base}_{counter}{ext}")
                    counter += 1
            
            shutil.copy2(wine_file, destination)
            copied_files.append(destination)
            
            file_size = os.path.getsize(destination) / (1024 * 1024)  # MB
            print(f"✅ Copied: {filename} ({file_size:.1f} MB)")
            
        except Exception as e:
            print(f"❌ Error copying {wine_file}: {e}")
    
    return copied_files

def analyze_wine_csv_files(csv_files):
    """Analyze copied CSV files to identify the best ones for trading"""
    
    print(f"\n🔍 Analyzing {len(csv_files)} CSV files...")
    
    analysis_results = []
    
    for csv_file in csv_files:
        try:
            # Quick analysis
            with open(csv_file, 'r') as f:
                lines = f.readlines()
            
            if len(lines) < 10:
                continue  # Skip very small files
            
            # Check header
            header = lines[0].strip().upper()
            has_ohlc = all(col in header for col in ['OPEN', 'HIGH', 'LOW', 'CLOSE'])
            has_time = 'TIME' in header or 'DATE' in header
            
            # Estimate timeframe from filename
            filename = os.path.basename(csv_file).upper()
            timeframe = 'Unknown'
            if 'M30' in filename or '30' in filename:
                timeframe = 'M30'
            elif 'H1' in filename or 'H1' in filename:
                timeframe = 'H1'
            elif 'M15' in filename:
                timeframe = 'M15'
            elif 'H4' in filename:
                timeframe = 'H4'
            
            # Estimate symbol
            symbol = 'Unknown'
            for sym in ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD']:
                if sym in filename:
                    symbol = sym
                    break
            
            analysis_results.append({
                'file': csv_file,
                'filename': os.path.basename(csv_file),
                'lines': len(lines),
                'has_ohlc': has_ohlc,
                'has_time': has_time,
                'symbol': symbol,
                'timeframe': timeframe,
                'score': (len(lines) * 0.1) + (has_ohlc * 50) + (has_time * 25) + (symbol != 'Unknown' * 25)
            })
            
        except Exception as e:
            print(f"Error analyzing {csv_file}: {e}")
    
    # Sort by score (best files first)
    analysis_results.sort(key=lambda x: x['score'], reverse=True)
    
    print("\n📊 File Analysis Results:")
    print("Rank | Filename | Symbol | TF | Lines | OHLC | Score")
    print("-" * 60)
    
    for i, result in enumerate(analysis_results[:10]):  # Show top 10
        print(f"{i+1:4d} | {result['filename'][:20]:<20} | {result['symbol']:<6} | {result['timeframe']:<3} | {result['lines']:>5} | {'✅' if result['has_ohlc'] else '❌'} | {result['score']:>5.1f}")
    
    return analysis_results

def main():
    """Main function for Wine MT5 data import"""
    
    print("🍷 MT5 Wine Data Import Tool")
    print("=" * 40)
    print("This tool will:")
    print("1. Search for CSV files in your Wine environment")
    print("2. Copy them to your day_trading folder")
    print("3. Analyze and import the best files")
    print("4. Run backtesting analysis")
    print()
    
    # Create directories
    Config.create_directories()
    
    # Step 1: Find Wine CSV files
    wine_files = find_wine_csv_files()
    
    if not wine_files:
        print("❌ No CSV files found in Wine environment")
        print("\n📋 To export data from MT5 in Wine:")
        print("METHOD 1 - Using Symbols Window (Recommended):")
        print("1. Open MT5 through Wine")
        print("2. Go to View → Symbols (or press Ctrl+U)")
        print("3. Find EURUSD in Forex folder")
        print("4. Right-click EURUSD → Download bars/Update history")
        print("5. Open EURUSD M30 chart")
        print("6. Right-click chart → Save as/Export → CSV")
        print("7. Save to Desktop or Documents")
        print()
        print("METHOD 2 - Using History Center (if available):")
        print("1. Press F2 (History Center)")
        print("2. Navigate: Forex → Your Broker → EURUSD → 30 Minutes")
        print("3. Right-click → Export → CSV")
        print("4. Save to Desktop or Documents")
        print()
        print("Then run this script again")
        return False
    
    print(f"✅ Found {len(wine_files)} CSV files in Wine environment:")
    for i, file in enumerate(wine_files[:10]):  # Show first 10
        file_size = os.path.getsize(file) / (1024 * 1024)  # MB
        print(f"  {i+1}. {os.path.basename(file)} ({file_size:.1f} MB)")
    
    if len(wine_files) > 10:
        print(f"  ... and {len(wine_files) - 10} more files")
    
    # Step 2: Copy files
    proceed = input(f"\n📁 Copy these files to current directory? (y/n): ").strip().lower()
    if not proceed.startswith('y'):
        print("Operation cancelled")
        return False
    
    copied_files = copy_wine_files_to_trading_folder(wine_files)
    
    if not copied_files:
        print("❌ No files were copied successfully")
        return False
    
    # Step 3: Analyze files
    analysis_results = analyze_wine_csv_files(copied_files)
    
    if not analysis_results:
        print("❌ No valid CSV files found")
        return False
    
    # Step 4: Import best file
    best_file = analysis_results[0]
    print(f"\n🎯 Best file identified: {best_file['filename']}")
    print(f"   Symbol: {best_file['symbol']}")
    print(f"   Timeframe: {best_file['timeframe']}")
    print(f"   Data points: {best_file['lines']}")
    
    # Import using MT5DataImporter
    importer = MT5DataImporter()
    
    try:
        symbol = best_file['symbol'] if best_file['symbol'] != 'Unknown' else 'EURUSD'
        data = importer.load_mt5_csv(best_file['file'], symbol)
        
        if not data.empty:
            print(f"✅ Successfully imported {len(data)} bars")
            print(f"   Date range: {data.index[0]} to {data.index[-1]}")
            
            # Save to organized location
            organized_filename = Config.get_timestamped_filename(
                f'{symbol}_{best_file["timeframe"]}_wine_mt5', '.csv', Config.DATA_DIR
            )
            importer.save_data(data, organized_filename)
            print(f"   Saved to: {organized_filename}")
            
            # Ask if user wants to run analysis
            run_analysis = input("\n🚀 Run complete backtesting analysis now? (y/n): ").strip().lower()
            
            if run_analysis.startswith('y'):
                print("Starting backtesting analysis...")
                try:
                    from run_complete_analysis import CompleteAnalysisWorkflow
                    
                    workflow = CompleteAnalysisWorkflow(
                        symbol=symbol,
                        start_date=data.index[0].strftime('%Y-%m-%d'),
                        end_date=data.index[-1].strftime('%Y-%m-%d')
                    )
                    
                    # Use imported data
                    workflow.data = data
                    workflow.backtester = type('obj', (object,), {})()
                    workflow.backtester.data_30m = data
                    workflow.backtester.data_1h = importer.resample_data(data, '1H')
                    
                    # Run analysis
                    workflow.step2_single_backtest()
                    workflow.step3_parameter_optimization()
                    workflow.step4_walkforward_analysis()
                    workflow.step5_export_optimal_parameters()
                    
                    print("\n🎉 Complete analysis finished!")
                    print(f"Check results in: {Config.EXPORTS_DIR}")
                    
                except Exception as e:
                    print(f"❌ Error running analysis: {e}")
            
            return True
        else:
            print("❌ Failed to import data")
            return False
            
    except Exception as e:
        print(f"❌ Error importing data: {e}")
        return False

if __name__ == "__main__":
    main()
