#!/usr/bin/env python3
"""
Export Optimal Parameters to MT5 Format
Converts Python optimization results to MT5 EA input parameters
"""

import json
import os
from datetime import datetime
from typing import Dict, Any
from config import Config

class MT5ParameterExporter:
    """
    Export optimized parameters to MT5-compatible formats
    """
    
    def __init__(self):
        Config.create_directories()
        
        # Parameter mapping from Python to MT5
        self.parameter_mapping = {
            # Basic parameters
            'session_start_hour': 'SessionStartHour',
            'risk_percent': 'RiskPercent',
            'max_spread_pips': 'MaxSpread',
            'risk_reward_ratio': 'RiskRewardRatio',
            
            # Enhanced filters
            'use_volume_filter': 'UseVolumeFilter',
            'volume_multiplier': 'VolumeMultiplier',
            'use_atr_filter': 'UseATRFilter',
            'atr_period': 'ATRPeriod',
            'min_atr_multiplier': 'MinATRMultiplier',
            'max_atr_multiplier': 'MaxATRMultiplier',
            
            # Multiple timeframe
            'use_mtf': 'UseMultiTimeframe',
            'htf_period': 'TrendPeriod',
            'only_trend_direction': 'OnlyTrendDirection',
            'use_htf_sup_res': 'UseHTFSupRes',
            
            # Range filters
            'use_range_filter': 'UseRangeFilter',
            'min_range_pips': 'MinRangePips',
            'max_range_pips': 'MaxRangePips',
            'range_percentile': 'RangePercentile',
            'range_history_bars': 'RangeHistoryBars',
            
            # Profit management
            'use_multiple_targets': 'UseMultipleTargets',
            'first_target_ratio': 'FirstTargetRatio',
            'second_target_ratio': 'SecondTargetRatio',
            'final_target_ratio': 'FinalTargetRatio',
            'first_target_percent': 'FirstTargetPercent',
            'second_target_percent': 'SecondTargetPercent',
            
            # Order settings
            'order_offset_pips': 'OrderOffsetPips',
            'max_slippage': 'MaxSlippage',
            
            # Trade management
            'use_dynamic_breakeven': 'UseDynamicBreakeven',
            'breakeven_ratio': 'BreakevenAtRatio',
            'trailing_pips': 'TrailingStopPips',
            'max_trade_hours': 'MaxTradeHours',
            
            # Advanced features
            'trailing_type': 'TrailingType',
            'atr_trailing_multi': 'ATRTrailingMulti',
            'step_trailing_pips': 'StepTrailingPips',
            'acceleration_factor': 'AccelerationFactor'
        }
        
        # Data type mapping
        self.data_types = {
            'bool': 'bool',
            'int': 'int',
            'float': 'double',
            'str': 'string'
        }
    
    def load_optimization_results(self, results_file: str) -> Dict[str, Any]:
        """Load optimization results from JSON file"""
        
        try:
            with open(results_file, 'r') as f:
                data = json.load(f)
            
            if 'best_parameters' in data:
                return data
            elif 'results' in data and len(data['results']) > 0:
                # Extract best result
                best_result = data['results'][0]
                return {
                    'best_parameters': best_result.get('parameters', {}),
                    'performance_metrics': {
                        'total_return': best_result.get('total_return', 0),
                        'win_rate': best_result.get('win_rate', 0),
                        'sharpe_ratio': best_result.get('sharpe_ratio', 0),
                        'max_drawdown': best_result.get('max_drawdown', 0),
                        'total_trades': best_result.get('total_trades', 0)
                    }
                }
            else:
                raise ValueError("Invalid results file format")
                
        except Exception as e:
            print(f"Error loading results file: {e}")
            return {}
    
    def export_mt5_inputs(self, parameters: Dict[str, Any], output_file: str, 
                         performance_metrics: Dict = None, validation_results: Dict = None) -> bool:
        """Export parameters as MT5 input declarations"""
        
        try:
            with open(output_file, 'w') as f:
                # Header
                f.write("//+------------------------------------------------------------------+\n")
                f.write("//|                    Optimized EA Parameters                       |\n")
                f.write("//|                Generated by Python Backtesting System           |\n")
                f.write(f"//|                Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}                    |\n")
                f.write("//+------------------------------------------------------------------+\n\n")
                
                # Performance summary
                if performance_metrics:
                    f.write("// OPTIMIZATION RESULTS:\n")
                    f.write(f"// Total Return: {performance_metrics.get('total_return', 0):.2f}%\n")
                    f.write(f"// Win Rate: {performance_metrics.get('win_rate', 0):.1f}%\n")
                    f.write(f"// Sharpe Ratio: {performance_metrics.get('sharpe_ratio', 0):.2f}\n")
                    f.write(f"// Max Drawdown: {performance_metrics.get('max_drawdown', 0):.2f}%\n")
                    f.write(f"// Total Trades: {performance_metrics.get('total_trades', 0)}\n")
                    
                if validation_results:
                    f.write(f"// Walk-Forward Periods: {validation_results.get('walkforward_periods', 0)}\n")
                    f.write(f"// Average Test Return: {validation_results.get('avg_test_return', 0):.2f}%\n")
                    f.write(f"// Consistency Score: {validation_results.get('consistency_score', 0)*100:.1f}%\n")
                
                f.write("\n")
                
                # Group parameters by category
                categories = {
                    'Session Settings': ['session_start_hour'],
                    'Risk Management': ['risk_percent', 'max_spread_pips', 'risk_reward_ratio'],
                    'Enhanced Filters': ['use_volume_filter', 'volume_multiplier', 'use_atr_filter', 
                                       'atr_period', 'min_atr_multiplier', 'max_atr_multiplier'],
                    'Multiple Timeframe Analysis': ['use_mtf', 'htf_period', 'only_trend_direction', 'use_htf_sup_res'],
                    'Range-Based Filters': ['use_range_filter', 'min_range_pips', 'max_range_pips', 
                                          'range_percentile', 'range_history_bars'],
                    'Profit Management': ['use_multiple_targets', 'first_target_ratio', 'second_target_ratio',
                                        'final_target_ratio', 'first_target_percent', 'second_target_percent'],
                    'Order Settings': ['order_offset_pips', 'max_slippage'],
                    'Advanced Trade Management': ['use_dynamic_breakeven', 'breakeven_ratio', 'trailing_pips',
                                                'max_trade_hours', 'trailing_type', 'atr_trailing_multi',
                                                'step_trailing_pips', 'acceleration_factor']
                }
                
                for category, param_list in categories.items():
                    f.write(f"//--- {category}\n")
                    f.write(f"input group           \"{category}\"\n")
                    
                    for param in param_list:
                        if param in parameters:
                            value = parameters[param]
                            mt5_name = self.parameter_mapping.get(param, param)
                            
                            # Determine data type and format
                            if isinstance(value, bool):
                                f.write(f"input bool            {mt5_name:<20} = {'true' if value else 'false'};\n")
                            elif isinstance(value, int):
                                f.write(f"input int             {mt5_name:<20} = {value};\n")
                            elif isinstance(value, float):
                                f.write(f"input double          {mt5_name:<20} = {value};\n")
                            elif isinstance(value, str):
                                f.write(f"input string          {mt5_name:<20} = \"{value}\";\n")
                    
                    f.write("\n")
                
                # Footer
                f.write("//+------------------------------------------------------------------+\n")
                f.write("// USAGE INSTRUCTIONS:\n")
                f.write("// 1. Copy the input parameters above to your EA\n")
                f.write("// 2. Replace the existing input declarations\n")
                f.write("// 3. Compile and test on demo account first\n")
                f.write("// 4. Monitor performance vs backtest results\n")
                f.write("//+------------------------------------------------------------------+\n")
            
            return True
            
        except Exception as e:
            print(f"Error exporting MT5 inputs: {e}")
            return False
    
    def export_mt5_set_file(self, parameters: Dict[str, Any], output_file: str) -> bool:
        """Export parameters as MT5 .set file for EA testing"""
        
        try:
            with open(output_file, 'w') as f:
                f.write(";--- Optimized Parameters Set File\n")
                f.write(f";--- Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(";--- Load this file in MT5 Strategy Tester\n\n")
                
                for param, value in parameters.items():
                    mt5_name = self.parameter_mapping.get(param, param)
                    
                    if isinstance(value, bool):
                        f.write(f"{mt5_name}={'1' if value else '0'}\n")
                    else:
                        f.write(f"{mt5_name}={value}\n")
            
            return True
            
        except Exception as e:
            print(f"Error exporting .set file: {e}")
            return False
    
    def export_summary_report(self, parameters: Dict[str, Any], output_file: str,
                            performance_metrics: Dict = None, validation_results: Dict = None) -> bool:
        """Export a human-readable summary report"""
        
        try:
            with open(output_file, 'w') as f:
                f.write("FIRST CANDLE EA - OPTIMIZATION SUMMARY REPORT\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Performance Summary
                if performance_metrics:
                    f.write("PERFORMANCE METRICS:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"Total Return:     {performance_metrics.get('total_return', 0):>8.2f}%\n")
                    f.write(f"Win Rate:         {performance_metrics.get('win_rate', 0):>8.1f}%\n")
                    f.write(f"Sharpe Ratio:     {performance_metrics.get('sharpe_ratio', 0):>8.2f}\n")
                    f.write(f"Max Drawdown:     {performance_metrics.get('max_drawdown', 0):>8.2f}%\n")
                    f.write(f"Total Trades:     {performance_metrics.get('total_trades', 0):>8}\n\n")
                
                # Validation Results
                if validation_results:
                    f.write("VALIDATION RESULTS:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"Walk-Forward Periods: {validation_results.get('walkforward_periods', 0):>4}\n")
                    f.write(f"Avg Test Return:      {validation_results.get('avg_test_return', 0):>8.2f}%\n")
                    f.write(f"Positive Periods:     {validation_results.get('positive_periods', 0):>4}\n")
                    f.write(f"Consistency Score:    {validation_results.get('consistency_score', 0)*100:>8.1f}%\n\n")
                
                # Optimized Parameters
                f.write("OPTIMIZED PARAMETERS:\n")
                f.write("-" * 30 + "\n")
                
                for param, value in sorted(parameters.items()):
                    mt5_name = self.parameter_mapping.get(param, param)
                    f.write(f"{mt5_name:<25}: {value}\n")
                
                f.write("\n" + "=" * 60 + "\n")
                f.write("IMPLEMENTATION NOTES:\n")
                f.write("1. Test these parameters on demo account first\n")
                f.write("2. Monitor live performance vs backtest results\n")
                f.write("3. Consider re-optimization monthly\n")
                f.write("4. Validate on different market conditions\n")
            
            return True
            
        except Exception as e:
            print(f"Error exporting summary report: {e}")
            return False
    
    def export_all_formats(self, results_file: str) -> bool:
        """Export optimization results in all formats"""
        
        print(f"📤 Exporting optimization results from: {results_file}")
        
        # Load results
        data = self.load_optimization_results(results_file)
        
        if not data:
            print("❌ Failed to load optimization results")
            return False
        
        parameters = data.get('best_parameters', {})
        performance = data.get('performance_metrics', {})
        validation = data.get('validation_results', {})
        
        if not parameters:
            print("❌ No parameters found in results file")
            return False
        
        # Generate timestamped filenames
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Export MT5 input parameters
        mt5_inputs_file = os.path.join(Config.EXPORTS_DIR, f'mt5_inputs_{timestamp}.mq5')
        if self.export_mt5_inputs(parameters, mt5_inputs_file, performance, validation):
            print(f"✅ MT5 inputs exported: {mt5_inputs_file}")
        
        # Export .set file
        set_file = os.path.join(Config.EXPORTS_DIR, f'ea_parameters_{timestamp}.set')
        if self.export_mt5_set_file(parameters, set_file):
            print(f"✅ .set file exported: {set_file}")
        
        # Export summary report
        summary_file = os.path.join(Config.EXPORTS_DIR, f'optimization_summary_{timestamp}.txt')
        if self.export_summary_report(parameters, summary_file, performance, validation):
            print(f"✅ Summary report exported: {summary_file}")
        
        print(f"\n📁 All exports saved to: {Config.EXPORTS_DIR}")
        
        return True


def main():
    """Main function for command-line usage"""
    
    print("📤 MT5 Parameter Exporter")
    print("=" * 30)
    
    # Find latest optimization results
    results_dir = Config.RESULTS_DIR
    
    if not os.path.exists(results_dir):
        print(f"❌ Results directory not found: {results_dir}")
        print("Run optimization first: python run_complete_analysis.py")
        return
    
    # List available results files
    results_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    
    if not results_files:
        print(f"❌ No optimization results found in: {results_dir}")
        print("Run optimization first: python run_complete_analysis.py")
        return
    
    print(f"Found {len(results_files)} results files:")
    for i, file in enumerate(sorted(results_files, reverse=True)):
        print(f"{i+1}. {file}")
    
    # Select file
    try:
        choice = input(f"\nSelect file (1-{len(results_files)}, or Enter for latest): ").strip()
        
        if choice == "":
            selected_file = sorted(results_files, reverse=True)[0]
        else:
            selected_file = sorted(results_files, reverse=True)[int(choice)-1]
        
        results_path = os.path.join(results_dir, selected_file)
        
        # Export
        exporter = MT5ParameterExporter()
        success = exporter.export_all_formats(results_path)
        
        if success:
            print("\n🎉 Export completed successfully!")
            print("\n📋 Next steps:")
            print("1. Copy the MT5 input parameters to your EA")
            print("2. Or load the .set file in MT5 Strategy Tester")
            print("3. Test on demo account first")
        else:
            print("\n❌ Export failed")
            
    except (ValueError, IndexError):
        print("❌ Invalid selection")
    except KeyboardInterrupt:
        print("\n👋 Export cancelled")


if __name__ == "__main__":
    main()
