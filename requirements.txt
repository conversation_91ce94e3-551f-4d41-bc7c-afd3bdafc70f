# First Candle Backtester Requirements
# Compatible with Mac, Windows, and Linux

# Core backtesting framework
backtrader>=**********

# Data handling and analysis
pandas>=1.5.0
numpy>=1.21.0
yfinance>=0.2.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Optional: For more advanced data sources
# alpha_vantage>=2.3.1
# quandl>=3.7.0
# ccxt>=3.0.0  # For crypto data

# Optional: For machine learning features
# scikit-learn>=1.1.0
# scipy>=1.9.0

# Optional: For faster computations
# numba>=0.56.0

# Optional: For web interface
# streamlit>=1.25.0
# plotly>=5.15.0

# Optional: For database storage
# sqlalchemy>=1.4.0
# sqlite3  # Built into Python

# Development and testing
pytest>=7.0.0
jupyter>=1.0.0
