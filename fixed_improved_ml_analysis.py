#!/usr/bin/env python3
"""
Fixed Improved ML-Enhanced MT5 Analysis Tool
Addresses NaN values and class imbalance issues
"""

import numpy as np
import pandas as pd
import backtrader as bt
from datetime import datetime, time
import json
import os
import warnings
warnings.filterwarnings('ignore')

# Import from the original ML file
from ml_enhanced_mt5_analysis import (
    OptimizedFeatureEngine, BreakoutLabelGenerator, MLBreakoutPredictor,
    find_mt5_csv_files, convert_mt5_csv
)

try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.model_selection import train_test_split, TimeSeriesSplit
    from sklearn.metrics import classification_report, precision_recall_curve
    from sklearn.preprocessing import StandardScaler
    from sklearn.impute import SimpleImputer
    from imblearn.over_sampling import SMOTE
    from imblearn.under_sampling import RandomUnderSampler
    from imblearn.pipeline import Pipeline as ImbPipeline
    import joblib
    ML_AVAILABLE = True
    IMBLEARN_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Missing libraries: {e}")
    print("Install with: pip install scikit-learn imbalanced-learn")
    ML_AVAILABLE = False
    IMBLEARN_AVAILABLE = False

class RobustFeatureEngine(OptimizedFeatureEngine):
    """Robust feature engineering with NaN handling and validation"""
    
    def extract_features(self, data):
        """Extract features with robust NaN handling"""
        print("🔧 Extracting robust ML features...")
        
        # Start with basic features
        features = pd.DataFrame(index=data.index)
        
        # Price-based features with NaN handling
        features['close_sma_5'] = data['Close'].rolling(5, min_periods=3).mean()
        features['close_sma_20'] = data['Close'].rolling(20, min_periods=10).mean()
        features['close_sma_50'] = data['Close'].rolling(50, min_periods=25).mean()
        
        # Technical indicators with fallback
        features['rsi_14'] = self.safe_rsi(data['Close'], 14)
        features['rsi_21'] = self.safe_rsi(data['Close'], 21)
        features['bb_position'] = self.safe_bollinger_position(data['Close'])
        features['atr_percentile'] = self.safe_atr_percentile(data['High'], data['Low'], data['Close'])
        
        # Volume features
        features['volume_sma'] = data['Volume'].rolling(20, min_periods=10).mean()
        features['volume_ratio'] = data['Volume'] / features['volume_sma']
        features['volume_percentile'] = data['Volume'].rolling(100, min_periods=50).rank(pct=True)
        
        # Price action features
        features['daily_range'] = (data['High'] - data['Low']) / data['Close']
        features['body_size'] = abs(data['Close'] - data['Open']) / data['Close']
        features['upper_shadow'] = (data['High'] - data[['Open', 'Close']].max(axis=1)) / data['Close']
        features['lower_shadow'] = (data[['Open', 'Close']].min(axis=1) - data['Low']) / data['Close']
        
        # Time-based features (no NaN risk)
        features['hour'] = data.index.hour
        features['day_of_week'] = data.index.dayofweek
        features['is_london_session'] = ((data.index.hour >= 8) & (data.index.hour <= 16)).astype(int)
        features['is_ny_session'] = ((data.index.hour >= 13) & (data.index.hour <= 21)).astype(int)
        features['is_overlap'] = ((data.index.hour >= 13) & (data.index.hour <= 16)).astype(int)
        
        # Momentum features
        features['price_change_1h'] = data['Close'].pct_change(2)
        features['price_change_4h'] = data['Close'].pct_change(8)
        features['price_change_1d'] = data['Close'].pct_change(48)
        
        # Volatility features
        features['volatility_5'] = data['Close'].rolling(5, min_periods=3).std()
        features['volatility_20'] = data['Close'].rolling(20, min_periods=10).std()
        features['volatility_ratio'] = features['volatility_5'] / features['volatility_20']
        
        # Support/Resistance features
        features['distance_to_high_20'] = (data['High'].rolling(20, min_periods=10).max() - data['Close']) / data['Close']
        features['distance_to_low_20'] = (data['Close'] - data['Low'].rolling(20, min_periods=10).min()) / data['Close']
        
        # Enhanced features with robust calculation
        features['volatility_regime'] = self.safe_volatility_regime(data)
        features['trend_strength'] = self.safe_trend_strength(data)
        features['market_efficiency'] = self.safe_market_efficiency(data)
        
        # Simple interaction features
        features['rsi_volume_interaction'] = features['rsi_14'] * features['volume_ratio']
        features['bb_atr_interaction'] = features['bb_position'] * features['atr_percentile']
        
        # Handle remaining NaN values
        print(f"Features before cleaning: {len(features.columns)} columns, {len(features)} rows")
        
        # Fill NaN values with appropriate defaults
        numeric_columns = features.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            if features[col].isna().any():
                if 'ratio' in col.lower() or 'percentile' in col.lower():
                    features[col] = features[col].fillna(0.5)  # Neutral value
                elif 'change' in col.lower():
                    features[col] = features[col].fillna(0.0)  # No change
                elif 'regime' in col.lower():
                    features[col] = features[col].fillna(1.0)  # Medium regime
                else:
                    features[col] = features[col].fillna(features[col].median())
        
        # Remove rows with too many NaN values (first 50 bars typically)
        features = features.iloc[50:].copy()
        
        # Final NaN check
        nan_counts = features.isna().sum()
        if nan_counts.any():
            print(f"⚠️ Remaining NaN values: {nan_counts[nan_counts > 0].to_dict()}")
            features = features.dropna()
        
        print(f"✅ Final features: {len(features.columns)} columns, {len(features)} rows")
        
        return features
    
    def safe_rsi(self, prices, period=14):
        """RSI calculation with NaN handling"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period, min_periods=period//2).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period, min_periods=period//2).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.fillna(50)  # Neutral RSI
        except:
            return pd.Series(50, index=prices.index)
    
    def safe_bollinger_position(self, prices, period=20, std=2):
        """Bollinger Band position with NaN handling"""
        try:
            sma = prices.rolling(window=period, min_periods=period//2).mean()
            std_dev = prices.rolling(window=period, min_periods=period//2).std()
            upper = sma + (std_dev * std)
            lower = sma - (std_dev * std)
            position = (prices - lower) / (upper - lower)
            return position.fillna(0.5).clip(0, 1)
        except:
            return pd.Series(0.5, index=prices.index)
    
    def safe_atr_percentile(self, high, low, close, period=14, lookback=100):
        """ATR percentile with NaN handling"""
        try:
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(window=period, min_periods=period//2).mean()
            percentile = atr.rolling(window=lookback, min_periods=lookback//2).rank(pct=True)
            return percentile.fillna(0.5)
        except:
            return pd.Series(0.5, index=close.index)
    
    def safe_volatility_regime(self, data, lookback=50):
        """Volatility regime with NaN handling"""
        try:
            atr = (data['High'] - data['Low']).rolling(14, min_periods=7).mean()
            atr_percentile = atr.rolling(lookback, min_periods=lookback//2).rank(pct=True)
            
            regime = pd.Series(1.0, index=data.index)  # Default to medium
            regime[atr_percentile <= 0.33] = 0  # Low volatility
            regime[atr_percentile > 0.66] = 2   # High volatility
            
            return regime.fillna(1.0)
        except:
            return pd.Series(1.0, index=data.index)
    
    def safe_trend_strength(self, data, period=20):
        """Simplified trend strength calculation"""
        try:
            close = data['Close']
            sma_short = close.rolling(period//2, min_periods=period//4).mean()
            sma_long = close.rolling(period, min_periods=period//2).mean()
            
            # Trend strength based on SMA separation
            trend_strength = abs(sma_short - sma_long) / sma_long
            return trend_strength.fillna(0.1)
        except:
            return pd.Series(0.1, index=data.index)
    
    def safe_market_efficiency(self, data, period=20):
        """Simplified market efficiency calculation"""
        try:
            close = data['Close']
            price_change = abs(close - close.shift(period))
            daily_changes = abs(close - close.shift(1))
            sum_daily_changes = daily_changes.rolling(period, min_periods=period//2).sum()
            
            efficiency = price_change / sum_daily_changes
            return efficiency.fillna(0.5).clip(0, 1)
        except:
            return pd.Series(0.5, index=data.index)

class ImbalancedMLPredictor(MLBreakoutPredictor):
    """ML predictor that handles class imbalance"""
    
    def train(self, features, labels):
        """Train with class imbalance handling"""
        print("🧠 Training ML model with imbalance handling...")
        
        # Remove no-trade samples
        mask = labels != -1
        X = features[mask].copy()
        y = labels[mask].copy()
        
        print(f"Training samples: {len(X)} (Positive: {sum(y)}, Negative: {len(y) - sum(y)})")
        
        if len(X) < 100:
            print("❌ Insufficient training data")
            return False
        
        # Check class imbalance
        positive_ratio = sum(y) / len(y)
        print(f"Positive class ratio: {positive_ratio:.3f}")
        
        if positive_ratio < 0.1:
            print("⚠️ Severe class imbalance detected. Applying balancing...")
            
        # Handle NaN values with imputer
        imputer = SimpleImputer(strategy='median')
        X_imputed = pd.DataFrame(
            imputer.fit_transform(X), 
            columns=X.columns, 
            index=X.index
        )
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X_imputed)
        
        # Handle class imbalance
        if positive_ratio < 0.15:  # If less than 15% positive samples
            try:
                # Use SMOTE for oversampling minority class
                smote = SMOTE(random_state=42, k_neighbors=min(5, sum(y)-1))
                X_balanced, y_balanced = smote.fit_resample(X_scaled, y)
                print(f"After SMOTE: {len(X_balanced)} samples (Positive: {sum(y_balanced)}, Negative: {len(y_balanced) - sum(y_balanced)})")
            except Exception as e:
                print(f"SMOTE failed: {e}. Using class weights instead.")
                X_balanced, y_balanced = X_scaled, y
        else:
            X_balanced, y_balanced = X_scaled, y
        
        # Train model with class weights
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=8,
            min_samples_split=20,
            min_samples_leaf=10,
            random_state=42,
            n_jobs=-1,
            class_weight='balanced'  # Handle remaining imbalance
        )
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=3)
        cv_scores = []
        
        for train_idx, val_idx in tscv.split(X_balanced):
            X_train, X_val = X_balanced[train_idx], X_balanced[val_idx]
            y_train, y_val = y_balanced[train_idx], y_balanced[val_idx]
            
            self.model.fit(X_train, y_train)
            score = self.model.score(X_val, y_val)
            cv_scores.append(score)
        
        # Final training on all balanced data
        self.model.fit(X_balanced, y_balanced)
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"✅ Model trained successfully!")
        print(f"   Cross-validation scores: {[f'{s:.3f}' for s in cv_scores]}")
        print(f"   Average CV score: {np.mean(cv_scores):.3f} ± {np.std(cv_scores):.3f}")
        print(f"\\n🔝 Top 5 Important Features:")
        for _, row in feature_importance.head().iterrows():
            print(f"   {row['feature']}: {row['importance']:.3f}")
        
        self.feature_names = X.columns.tolist()
        self.is_trained = True
        
        return True

# Import the improved strategy from the previous file
from improved_ml_mt5_analysis import ImprovedBreakoutStrategy

def run_fixed_analysis(data):
    """Run analysis with fixed NaN handling and class imbalance"""

    print("🚀 Running Fixed ML Analysis...")

    # Robust feature engineering
    feature_engine = RobustFeatureEngine()
    features = feature_engine.extract_features(data)

    if len(features) < 1000:
        print("❌ Insufficient feature data after cleaning")
        return None

    # Enhanced label generation
    label_generator = BreakoutLabelGenerator(
        risk_reward_ratio=2.0,  # More conservative
        max_trade_hours=8
    )
    labels = label_generator.generate_labels(data)

    # Align data with robust intersection
    common_index = features.index.intersection(labels.index)
    print(f"Common data points: {len(common_index)}")

    if len(common_index) < 500:
        print("❌ Insufficient aligned data for ML training")
        return None

    features_aligned = features.loc[common_index]
    labels_aligned = labels.loc[common_index]

    # Check data quality
    print(f"Features shape: {features_aligned.shape}")
    print(f"Labels distribution: {labels_aligned.value_counts().to_dict()}")

    # Train with imbalance handling
    ml_predictor = ImbalancedMLPredictor()
    success = ml_predictor.train(features_aligned, labels_aligned)

    if not success:
        print("❌ ML training failed")
        return None

    # Test conservative parameters only (to avoid overfitting)
    param_sets = [
        {
            'ml_confidence_threshold': 0.6,
            'risk_reward_ratio': 2.0,
            'max_trade_hours': 8,
            'min_range_pips': 8.0,  # Higher minimum range
            'max_daily_trades': 1
        },
        {
            'ml_confidence_threshold': 0.65,
            'risk_reward_ratio': 2.5,
            'max_trade_hours': 6,
            'min_range_pips': 10.0,
            'max_daily_trades': 1
        },
        {
            'ml_confidence_threshold': 0.7,
            'risk_reward_ratio': 3.0,
            'max_trade_hours': 4,
            'min_range_pips': 12.0,
            'max_daily_trades': 1
        }
    ]

    best_result = None
    best_params = None

    for i, params in enumerate(param_sets):
        print(f"\\n📊 Testing parameter set {i+1}/3...")
        print(f"   ML Threshold: {params['ml_confidence_threshold']}")
        print(f"   Risk:Reward: {params['risk_reward_ratio']}")
        print(f"   Min Range: {params['min_range_pips']} pips")

        try:
            result = run_fixed_backtest(data, ml_predictor, params)

            print(f"   Return: {result['total_return']:.2f}%")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Trades: {result['total_trades']}")
            print(f"   Sharpe: {result['sharpe_ratio']:.2f}")
            print(f"   Max DD: {result['max_drawdown']:.2f}%")

            # Select best based on positive returns and reasonable trade count
            if (result['total_return'] > 0 and
                result['total_trades'] >= 10 and  # Minimum trades for significance
                (best_result is None or
                 (result['sharpe_ratio'] > best_result['sharpe_ratio'] and result['total_return'] > 2))):
                best_result = result
                best_params = params

        except Exception as e:
            print(f"   ❌ Error: {e}")
            continue

    if best_result is None:
        print("\\n⚠️ No profitable configurations found")
        print("This could indicate:")
        print("- Market conditions not suitable for this strategy")
        print("- Need for different parameters")
        print("- Data quality issues")

        # Return the least bad result for analysis
        if len(param_sets) > 0:
            try:
                fallback_result = run_fixed_backtest(data, ml_predictor, param_sets[0])
                return fallback_result, param_sets[0], ml_predictor
            except:
                return None

    return best_result, best_params, ml_predictor

def run_fixed_backtest(data, ml_predictor, params):
    """Run backtest with fixed strategy"""

    cerebro = bt.Cerebro()
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)

    # Add strategy with conservative defaults
    strategy_params = {
        'risk_percent': 0.5,  # Lower risk
        'dynamic_position_sizing': False,  # Fixed sizing for now
        'breakeven_enabled': True,
        'trailing_enabled': False,  # Disable for simplicity
        **params
    }

    cerebro.addstrategy(ImprovedBreakoutStrategy, **strategy_params)

    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.0002)  # More realistic commission

    # Add analyzers
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')

    # Run backtest
    results = cerebro.run()
    strategy = results[0]

    # Set ML predictor
    strategy.set_ml_predictor(ml_predictor)

    final_value = cerebro.broker.getvalue()

    # Extract results
    trade_analyzer = strategy.analyzers.trades.get_analysis()
    sharpe_analyzer = strategy.analyzers.sharpe.get_analysis()
    drawdown_analyzer = strategy.analyzers.drawdown.get_analysis()

    # Calculate metrics
    total_return = (final_value - 10000) / 10000 * 100
    total_trades = trade_analyzer.get('total', {}).get('total', 0)
    winning_trades = trade_analyzer.get('won', {}).get('total', 0)
    losing_trades = trade_analyzer.get('lost', {}).get('total', 0)
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    max_drawdown = drawdown_analyzer.get('max', {}).get('drawdown', 0)
    sharpe_ratio = sharpe_analyzer.get('sharperatio', 0) or 0

    # Additional metrics
    avg_win = trade_analyzer.get('won', {}).get('pnl', {}).get('average', 0)
    avg_loss = trade_analyzer.get('lost', {}).get('pnl', {}).get('average', 0)
    profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 else 0

    return {
        'total_return': total_return,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'final_value': final_value,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'parameters': strategy_params
    }

def main():
    """Main fixed analysis function"""

    print("🔧 Fixed ML-Enhanced MT5 Analysis")
    print("=" * 50)
    print("Fixes:")
    print("✅ Robust NaN handling in feature engineering")
    print("✅ Class imbalance correction with SMOTE")
    print("✅ Conservative parameter testing")
    print("✅ Enhanced data validation")
    print("✅ Fallback mechanisms")
    print()

    if not ML_AVAILABLE:
        print("❌ ML libraries not available")
        return False

    # Find data
    csv_files = find_mt5_csv_files()
    if not csv_files:
        print("❌ No CSV files found")
        return False

    # Use existing cleaned file
    cleaned_files = [f for f in os.listdir('.') if 'cleaned.csv' in f]

    if cleaned_files:
        print(f"📁 Using existing cleaned file: {cleaned_files[0]}")
        data = pd.read_csv(cleaned_files[0], index_col=0, parse_dates=True)
    else:
        print(f"📁 Converting: {csv_files[0]}")
        data, _ = convert_mt5_csv(csv_files[0])
        if data is None:
            return False

    print(f"✅ Data loaded: {len(data)} bars")
    print(f"   Period: {data.index[0].date()} to {data.index[-1].date()}")

    # Run fixed analysis
    result = run_fixed_analysis(data)

    if result is None:
        print("❌ Analysis failed completely")
        return False

    best_result, best_params, ml_predictor = result

    # Display results
    print("\\n🏆 FIXED ANALYSIS RESULTS:")
    print("=" * 40)
    print(f"Total Return: {best_result['total_return']:.2f}%")
    print(f"Win Rate: {best_result['win_rate']:.1f}%")
    print(f"Total Trades: {best_result['total_trades']}")
    print(f"Profit Factor: {best_result['profit_factor']:.2f}")
    print(f"Sharpe Ratio: {best_result['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {best_result['max_drawdown']:.2f}%")
    print(f"Average Win: ${best_result['avg_win']:.2f}")
    print(f"Average Loss: ${best_result['avg_loss']:.2f}")

    print(f"\\n⚙️ Best Parameters:")
    for key, value in best_params.items():
        print(f"   {key}: {value}")

    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    result_file = f'backtesting/results/fixed_analysis_{timestamp}.json'

    os.makedirs('backtesting/results', exist_ok=True)

    with open(result_file, 'w') as f:
        json.dump({
            'strategy': 'Fixed_ML_Enhanced_FirstCandle',
            'results': best_result,
            'best_parameters': best_params,
            'timestamp': timestamp,
            'data_period': f'{data.index[0].date()} to {data.index[-1].date()}'
        }, f, indent=2, default=str)

    print(f"\\n💾 Results saved to: {result_file}")

    # Interpretation
    if best_result['total_return'] > 5:
        print("\\n✅ Good results! Strategy shows promise.")
    elif best_result['total_return'] > 0:
        print("\\n⚠️ Modest positive returns. Consider further optimization.")
    else:
        print("\\n❌ Negative returns. Strategy may need fundamental changes.")
        print("💡 Consider:")
        print("   - Different time periods")
        print("   - Alternative entry/exit rules")
        print("   - Different market conditions")

    return True

if __name__ == "__main__":
    main()
