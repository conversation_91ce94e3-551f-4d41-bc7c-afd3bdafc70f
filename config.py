#!/usr/bin/env python3
"""
Configuration file for First Candle EA Backtesting System
Store your API keys and settings here
"""

import os
from typing import Dict, Any

class Config:
    """Configuration class for API keys and settings"""
    
    # Alpha Vantage API Configuration
    ALPHA_VANTAGE_API_KEY = 'RWYAWAFKFWBRKBVZ'
    ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query'
    
    # Default trading parameters
    DEFAULT_PARAMS = {
        'session_start_hour': 8,  # London: 8, NY: 13
        'session_duration': 9,
        'risk_percent': 1.0,
        'max_spread_pips': 2.0,
        'risk_reward_ratio': 2.0,
        'use_volume_filter': True,
        'volume_multiplier': 1.2,
        'use_atr_filter': True,
        'atr_period': 14,
        'min_atr_multiplier': 1.0,
        'max_atr_multiplier': 3.0,
        'use_mtf': True,
        'htf_period': 20,
        'only_trend_direction': True,
        'use_range_filter': True,
        'min_range_pips': 5.0,
        'max_range_pips': 50.0,
        'range_percentile': 70.0,
        'use_multiple_targets': True,
        'first_target_ratio': 1.0,
        'second_target_ratio': 1.5,
        'final_target_ratio': 2.5,
        'first_target_percent': 30.0,
        'second_target_percent': 40.0,
        'breakeven_ratio': 1.2,
        'trailing_pips': 15,
        'max_trade_hours': 8,
        'order_offset_pips': 1.0,
    }
    
    # Optimization parameter ranges
    OPTIMIZATION_RANGES = {
        'risk_percent': [0.5, 1.0, 1.5, 2.0],
        'risk_reward_ratio': [1.5, 2.0, 2.5, 3.0],
        'volume_multiplier': [1.0, 1.2, 1.5, 1.8],
        'min_atr_multiplier': [0.8, 1.0, 1.2],
        'max_atr_multiplier': [2.5, 3.0, 3.5, 4.0],
        'range_percentile': [60.0, 70.0, 80.0],
        'breakeven_ratio': [1.0, 1.2, 1.5],
        'trailing_pips': [10, 15, 20, 25]
    }
    
    # Session-specific parameters
    LONDON_PARAMS = {
        'session_start_hour': 8,
        'risk_reward_ratio': 2.0,
        'min_range_pips': 8.0,
        'max_range_pips': 40.0,
        'volume_multiplier': 1.3
    }
    
    NEW_YORK_PARAMS = {
        'session_start_hour': 13,
        'risk_reward_ratio': 2.5,
        'min_range_pips': 10.0,
        'max_range_pips': 60.0,
        'volume_multiplier': 1.5
    }
    
    # Data source settings
    DATA_SOURCES = {
        'yahoo_finance': {
            'enabled': True,
            'symbols': {
                'EURUSD': 'EURUSD=X',
                'GBPUSD': 'GBPUSD=X',
                'USDJPY': 'USDJPY=X',
                'USDCHF': 'USDCHF=X',
                'AUDUSD': 'AUDUSD=X',
                'USDCAD': 'USDCAD=X',
                'NZDUSD': 'NZDUSD=X'
            }
        },
        'alpha_vantage': {
            'enabled': True,
            'api_key': ALPHA_VANTAGE_API_KEY,
            'symbols': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'],
            'intervals': ['1min', '5min', '15min', '30min', '60min']
        }
    }
    
    # Backtesting settings
    BACKTEST_SETTINGS = {
        'initial_capital': 10000.0,
        'commission': 0.0001,  # 1 pip spread
        'slippage': 0.5,  # 0.5 pip slippage
        'leverage': 100,
        'margin_requirement': 0.01  # 1% margin
    }
    
    # File paths
    DATA_DIR = 'data'
    RESULTS_DIR = 'results'
    CACHE_DIR = 'cache'
    
    @classmethod
    def get_api_key(cls, service: str) -> str:
        """Get API key for a specific service"""
        if service.lower() == 'alpha_vantage':
            # First try environment variable, then fall back to config
            return os.getenv('ALPHA_VANTAGE_API_KEY', cls.ALPHA_VANTAGE_API_KEY)
        return None
    
    @classmethod
    def get_symbol_mapping(cls, symbol: str, source: str = 'yahoo_finance') -> str:
        """Get the correct symbol format for a data source"""
        if source == 'yahoo_finance':
            return cls.DATA_SOURCES['yahoo_finance']['symbols'].get(symbol, symbol)
        elif source == 'alpha_vantage':
            return symbol  # Alpha Vantage uses standard 6-character format
        return symbol
    
    @classmethod
    def get_session_params(cls, session: str) -> Dict[str, Any]:
        """Get session-specific parameters"""
        if session.upper() == 'LONDON':
            return cls.LONDON_PARAMS.copy()
        elif session.upper() == 'NEW_YORK' or session.upper() == 'NY':
            return cls.NEW_YORK_PARAMS.copy()
        else:
            return cls.DEFAULT_PARAMS.copy()
    
    @classmethod
    def create_directories(cls):
        """Create necessary directories"""
        for directory in [cls.DATA_DIR, cls.RESULTS_DIR, cls.CACHE_DIR]:
            os.makedirs(directory, exist_ok=True)


# Quick access functions
def get_alpha_vantage_key():
    """Quick function to get Alpha Vantage API key"""
    return Config.get_api_key('alpha_vantage')

def get_default_params():
    """Quick function to get default parameters"""
    return Config.DEFAULT_PARAMS.copy()

def get_optimization_ranges():
    """Quick function to get optimization parameter ranges"""
    return Config.OPTIMIZATION_RANGES.copy()

# Initialize directories on import
Config.create_directories()

# Example usage and testing
if __name__ == "__main__":
    print("=== First Candle EA Configuration ===")
    print(f"Alpha Vantage API Key: {get_alpha_vantage_key()}")
    print(f"Default Parameters: {len(get_default_params())} parameters configured")
    print(f"Optimization Ranges: {len(get_optimization_ranges())} parameters for optimization")
    
    print("\n=== Available Symbols ===")
    for source, config in Config.DATA_SOURCES.items():
        if config['enabled']:
            print(f"{source.title()}:")
            if 'symbols' in config:
                for symbol, mapped in config['symbols'].items():
                    print(f"  {symbol} -> {mapped}")
            elif 'symbols' in config:
                for symbol in config['symbols']:
                    print(f"  {symbol}")
    
    print("\n=== Session Parameters ===")
    for session in ['LONDON', 'NEW_YORK']:
        params = Config.get_session_params(session)
        print(f"{session}: {params}")
