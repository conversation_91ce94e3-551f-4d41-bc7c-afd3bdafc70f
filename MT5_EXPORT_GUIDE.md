# 📊 MT5 Data Export Guide for Mac Backtesting

Since Alpha Vantage API isn't working, here's the complete guide to export data from MT5 and use it for backtesting on your Mac.

## 🎯 What Data to Export

### **Required Data:**
1. **EURUSD M30 (30-minute)** - Primary timeframe for First Candle strategy
2. **EURUSD H1 (1-hour)** - For multiple timeframe analysis (optional but recommended)

### **Recommended Period:**
- **Minimum**: 6 months of data
- **Optimal**: 12 months of data
- **Date Range**: June 2023 to December 2024 (or latest available)

## 📋 Step-by-Step MT5 Export Process

### **STEP 1: Open MT5 History Center**
1. Open MetaTrader 5
2. Press **F2** (or go to **Tools → History Center**)
3. Wait for the History Center window to open

### **STEP 2: Navigate to EURUSD Data**
1. In the left panel, expand: **Forex**
2. Expand your broker name (e.g., "IC Markets", "FTMO", etc.)
3. Find and click: **EURUSD**
4. Click on: **30 Minutes (M30)**

### **STEP 3: Select Date Range**
1. In the right panel, you'll see historical data
2. **Right-click** on any data row
3. Select **"All History"** or manually select your desired range
4. Ensure you have at least 6 months of data visible

### **STEP 4: Export to CSV**
1. **Right-click** on the data area
2. Select **"Export"**
3. Choose **CSV format**
4. **Important**: Use this exact filename format:

```
EURUSD_M30_20230601_to_20241201.csv
```

### **STEP 5: Repeat for H1 Data (Optional)**
1. Click on: **60 Minutes (H1)** in the left panel
2. Repeat the export process
3. Save as:

```
EURUSD_H1_20230601_to_20241201.csv
```

## 📁 File Naming Convention

### **Required Format:**
```
SYMBOL_TIMEFRAME_STARTDATE_to_ENDDATE.csv
```

### **Examples:**
```
✅ EURUSD_M30_20230601_to_20241201.csv
✅ EURUSD_H1_20230601_to_20241201.csv
✅ GBPUSD_M30_20230601_to_20241201.csv
```

### **Avoid These Names:**
```
❌ eurusd.csv
❌ data.csv
❌ export.csv
❌ EURUSD_2023.csv
```

## 📊 Expected CSV Format

Your exported CSV should look like this:

```csv
Date,Time,Open,High,Low,Close,Volume
2023.06.01,00:00,1.07000,1.07200,1.06950,1.07100,1250
2023.06.01,00:30,1.07100,1.07250,1.07050,1.07150,1180
2023.06.01,01:00,1.07150,1.07300,1.07100,1.07200,1320
2023.06.01,01:30,1.07200,1.07180,1.07050,1.07080,980
...
```

### **Column Requirements:**
- **Date**: Format YYYY.MM.DD
- **Time**: Format HH:MM (24-hour)
- **Open, High, Low, Close**: Price data with 4-5 decimal places
- **Volume**: Tick volume (any positive number)

## 🔧 Import Process on Mac

### **STEP 1: Place Files**
1. Copy your CSV files to the `day_trading` folder
2. Make sure filenames follow the naming convention

### **STEP 2: Run Import Script**
```bash
cd /Users/<USER>/day_trading
python import_mt5_data.py
```

### **STEP 3: Follow Interactive Prompts**
The script will:
1. Find your CSV files automatically
2. Analyze the file structure
3. Import and organize the data
4. Offer to run backtesting analysis immediately

## 🎯 Complete Workflow Example

### **On Windows/VPS (MT5 Export):**
1. Open MT5
2. Export EURUSD M30 data → Save as `EURUSD_M30_20230601_to_20241201.csv`
3. Export EURUSD H1 data → Save as `EURUSD_H1_20230601_to_20241201.csv`
4. Transfer files to Mac (USB, cloud, email, etc.)

### **On Mac (Backtesting):**
```bash
# 1. Place CSV files in day_trading folder
# 2. Run import and analysis
python quick_start.py
# Choose option 1: Import MT5 Data & Analyze

# 3. Check results
ls backtesting/exports/
```

## 📊 What You'll Get

After successful import and analysis:

### **Organized Data:**
```
backtesting/
├── data/
│   ├── EURUSD_M30_mt5_20241216_143022.csv
│   └── EURUSD_H1_mt5_20241216_143022.csv
```

### **Analysis Results:**
```
backtesting/
├── results/
│   ├── optimization_results_20241216_143022.json
│   └── walkforward_results_20241216_143022.csv
├── exports/
│   ├── optimal_parameters_20241216_143022.json
│   ├── mt5_inputs_20241216_143022.mq5
│   └── optimization_summary_20241216_143022.txt
└── reports/
    └── walkforward_analysis_20241216_143022.png
```

## 🔍 Data Quality Checks

The import script automatically checks:

### **✅ Valid Checks:**
- Proper OHLC relationships (Low ≤ Open,Close ≤ High)
- No missing values
- Reasonable time continuity
- Sufficient data volume (minimum 1000 bars)

### **⚠️ Common Issues:**
- **Weekend gaps**: Normal, will be handled automatically
- **Holiday gaps**: Normal for forex markets
- **Extreme price movements**: Flagged but usually normal during news events

## 💡 Pro Tips

### **Data Quality:**
1. **Export recent data**: Last 6-12 months for best results
2. **Check broker spread**: Lower spread brokers give better backtest results
3. **Verify timeframe**: Ensure M30 data is actually 30-minute intervals

### **File Management:**
1. **Use descriptive names**: Include broker name if testing multiple sources
2. **Keep originals**: Save original MT5 exports as backup
3. **Document settings**: Note any special MT5 settings used

### **Optimization:**
1. **Start with M30**: Primary timeframe for First Candle strategy
2. **Add H1 later**: For multiple timeframe analysis
3. **Test different periods**: Compare results across different date ranges

## 🆘 Troubleshooting

### **Problem: "No CSV files found"**
**Solution:**
- Check filename format exactly matches examples
- Ensure files are in the correct directory
- Try running `ls *.csv` to see what files exist

### **Problem: "Invalid CSV format"**
**Solution:**
- Re-export from MT5 with CSV format
- Check that columns match expected format
- Ensure no extra headers or footers in file

### **Problem: "Insufficient data"**
**Solution:**
- Export longer time period (6+ months)
- Check that data actually contains M30 intervals
- Verify date range covers recent periods

### **Problem: "Import successful but analysis fails"**
**Solution:**
- Check data quality (no gaps, valid prices)
- Ensure sufficient data volume
- Try with default parameters first

## 📞 Quick Commands

```bash
# Check for CSV files
ls *.csv

# Import MT5 data
python import_mt5_data.py

# Quick start with MT5 data
python quick_start.py

# Web interface
streamlit run streamlit_app.py
```

## 🎯 Expected Timeline

- **MT5 Export**: 5-10 minutes
- **File Transfer**: 1-2 minutes  
- **Import & Analysis**: 10-20 minutes
- **Parameter Export**: 1 minute
- **Total**: ~20-30 minutes for complete workflow

This method gives you the most accurate backtesting results since you're using the exact same data your broker provides for live trading!
