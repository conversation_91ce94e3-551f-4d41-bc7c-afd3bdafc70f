#!/usr/bin/env python3
"""
Quick Start Script for First Candle EA Backtesting
Runs a complete analysis with sensible defaults
"""

import os
import sys
from datetime import datetime, timedelta

def quick_eurusd_analysis():
    """Run a quick EURUSD analysis with your API key"""
    
    print("🚀 Quick Start: EURUSD First Candle Analysis")
    print("=" * 50)
    print("This will run a complete analysis with:")
    print("- EURUSD 30-minute data from Alpha Vantage")
    print("- 6 months of recent data")
    print("- Parameter optimization")
    print("- Walk-forward validation")
    print("- Export optimal parameters for MT5")
    print()
    
    # Calculate date range (last 6 months)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=180)
    
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    print(f"📅 Analysis period: {start_str} to {end_str}")
    
    # Import and run
    try:
        from run_complete_analysis import CompleteAnalysisWorkflow
        
        # Initialize with EURUSD
        workflow = CompleteAnalysisWorkflow(
            symbol='EURUSD',
            start_date=start_str,
            end_date=end_str
        )
        
        # Run complete workflow
        success = workflow.run_complete_workflow(use_alpha_vantage=True)
        
        if success:
            print("\n🎉 Quick analysis completed successfully!")
            print("\n📁 Check these folders for results:")
            print("- backtesting/data/ - Downloaded market data")
            print("- backtesting/results/ - Backtest results")
            print("- backtesting/exports/ - Optimal parameters for MT5")
            print("- backtesting/reports/ - Performance charts")
            
            print("\n📋 Next steps:")
            print("1. Review the optimal parameters in backtesting/exports/")
            print("2. Update your MT5 EA with the optimized settings")
            print("3. Test on demo account before going live")
            
        else:
            print("\n❌ Quick analysis failed. Try the detailed workflow.")
            
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nTry running setup first:")
        print("python setup_and_test.py")

def quick_test_api():
    """Quick test of Alpha Vantage API"""
    
    print("🔑 Quick API Test")
    print("=" * 30)
    
    try:
        from test_api_key import test_alpha_vantage_connection
        
        success = test_alpha_vantage_connection()
        
        if success:
            print("\n✅ API key working! Ready for analysis.")
            return True
        else:
            print("\n❌ API key test failed.")
            return False
            
    except Exception as e:
        print(f"\n❌ Error testing API: {e}")
        return False

def quick_web_interface():
    """Launch the web interface"""
    
    print("🌐 Launching Web Interface")
    print("=" * 30)
    
    try:
        import subprocess
        import sys
        
        print("Starting Streamlit web interface...")
        print("Your browser should open automatically.")
        print("If not, go to: http://localhost:8501")
        print("\nPress Ctrl+C to stop the web interface.")
        
        subprocess.run([sys.executable, '-m', 'streamlit', 'run', 'streamlit_app.py'])
        
    except KeyboardInterrupt:
        print("\n👋 Web interface stopped.")
    except Exception as e:
        print(f"\n❌ Error launching web interface: {e}")
        print("Try installing streamlit: pip install streamlit")

def quick_mt5_import():
    """Quick MT5 CSV import and analysis"""

    print("📊 Quick MT5 Data Import & Analysis")
    print("=" * 40)
    print("This will:")
    print("- Import your MT5 exported CSV files")
    print("- Run complete backtesting analysis")
    print("- Export optimal parameters for MT5")
    print()

    try:
        from import_mt5_data import import_mt5_csv_interactive

        success = import_mt5_csv_interactive()

        if success:
            print("\n🎉 MT5 import and analysis completed!")
        else:
            print("\n❌ MT5 import failed. Check your CSV files.")

    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Make sure you have exported CSV files from MT5")

def quick_wine_mt5_import():
    """Quick Wine MT5 import and analysis"""

    print("🍷 Wine MT5 Data Import & Analysis")
    print("=" * 40)
    print("This will:")
    print("- Search for CSV files exported from MT5 in Wine")
    print("- Copy and analyze the files")
    print("- Import the best data automatically")
    print("- Run complete backtesting analysis")
    print()

    try:
        from import_wine_mt5_data import main as wine_import_main

        success = wine_import_main()

        if success:
            print("\n🎉 Wine MT5 import and analysis completed!")
        else:
            print("\n❌ Wine MT5 import failed. Check your MT5 exports.")

    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Make sure you have exported CSV files from MT5 in Wine")

def main():
    """Main quick start menu"""

    print("🎯 First Candle EA - Quick Start Menu")
    print("=" * 40)
    print("Choose an option:")
    print("1. 🍷 Import Wine MT5 Data & Analyze (Recommended for Wine users)")
    print("2. 📊 Import Regular MT5 CSV Files")
    print("3. 🚀 Quick EURUSD Analysis (Alpha Vantage/Yahoo)")
    print("4. 🔑 Test Alpha Vantage API Key")
    print("5. 🌐 Launch Web Interface")
    print("6. 📋 View Step-by-Step Guide")
    print("7. ⚙️ Run Setup & Test")
    print("0. Exit")
    
    while True:
        try:
            choice = input("\nEnter your choice (0-7): ").strip()

            if choice == '1':
                quick_wine_mt5_import()
                break
            elif choice == '2':
                quick_mt5_import()
                break
            elif choice == '3':
                quick_eurusd_analysis()
                break
            elif choice == '4':
                quick_test_api()
                input("\nPress Enter to continue...")
            elif choice == '5':
                quick_web_interface()
                break
            elif choice == '6':
                print("\n📋 Opening Step-by-Step Guide...")
                print("Please read STEP_BY_STEP_GUIDE.md for detailed instructions.")
                break
            elif choice == '7':
                print("\n⚙️ Running setup and test...")
                try:
                    from setup_and_test import main as setup_main
                    setup_main()
                except Exception as e:
                    print(f"Error running setup: {e}")
                break
            elif choice == '0':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 0-7.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            break

if __name__ == "__main__":
    main()
