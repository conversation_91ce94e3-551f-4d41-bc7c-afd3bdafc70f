#!/usr/bin/env python3
"""
Improved ML-Enhanced MT5 Analysis Tool
Addresses issues from initial results and adds advanced features
"""

import numpy as np
import pandas as pd
import backtrader as bt
from datetime import datetime, time
import json
import os
import warnings
warnings.filterwarnings('ignore')

# Import from the original ML file
from ml_enhanced_mt5_analysis import (
    OptimizedFeatureEngine, BreakoutLabelGenerator, MLBreakoutPredictor,
    find_mt5_csv_files, convert_mt5_csv
)

try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.model_selection import train_test_split, TimeSeriesSplit
    from sklearn.metrics import classification_report, precision_recall_curve
    from sklearn.preprocessing import StandardScaler
    import joblib
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

class ImprovedFeatureEngine(OptimizedFeatureEngine):
    """Enhanced feature engineering with market regime detection"""
    
    def extract_features(self, data):
        """Extract enhanced features including market regime indicators"""
        print("🔧 Extracting enhanced ML features...")
        
        features = super().extract_features(data)
        
        # Add market regime features
        features['volatility_regime'] = self.detect_volatility_regime(data)
        features['trend_strength'] = self.calculate_trend_strength(data)
        features['market_efficiency'] = self.calculate_market_efficiency(data)
        features['session_momentum'] = self.calculate_session_momentum(data)
        features['range_expansion'] = self.detect_range_expansion(data)
        
        # Add interaction features (combinations that might be important)
        features['rsi_volume_interaction'] = features['rsi_14'] * features['volume_ratio']
        features['bb_atr_interaction'] = features['bb_position'] * features['atr_percentile']
        features['time_volatility_interaction'] = features['hour'] * features['volatility_ratio']
        
        print(f"✅ Enhanced features: {len(features.columns)} total features")
        return features
    
    def detect_volatility_regime(self, data, lookback=50):
        """Detect if we're in high/low volatility regime"""
        atr = (data['High'] - data['Low']).rolling(14).mean()
        atr_percentile = atr.rolling(lookback).rank(pct=True)
        
        regime = pd.Series(index=data.index, dtype=float)
        regime[atr_percentile <= 0.33] = 0  # Low volatility
        regime[(atr_percentile > 0.33) & (atr_percentile <= 0.66)] = 1  # Medium
        regime[atr_percentile > 0.66] = 2  # High volatility
        
        return regime
    
    def calculate_trend_strength(self, data, period=20):
        """Calculate trend strength using ADX-like calculation"""
        high = data['High']
        low = data['Low']
        close = data['Close']
        
        # True Range
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # Directional Movement
        dm_plus = np.where((high - high.shift(1)) > (low.shift(1) - low), 
                          np.maximum(high - high.shift(1), 0), 0)
        dm_minus = np.where((low.shift(1) - low) > (high - high.shift(1)), 
                           np.maximum(low.shift(1) - low, 0), 0)
        
        # Smooth and calculate DI
        tr_smooth = pd.Series(tr).rolling(period).mean()
        dm_plus_smooth = pd.Series(dm_plus).rolling(period).mean()
        dm_minus_smooth = pd.Series(dm_minus).rolling(period).mean()
        
        di_plus = 100 * dm_plus_smooth / tr_smooth
        di_minus = 100 * dm_minus_smooth / tr_smooth
        
        # ADX calculation
        dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
        adx = dx.rolling(period).mean()
        
        return adx / 100  # Normalize to 0-1
    
    def calculate_market_efficiency(self, data, period=20):
        """Calculate market efficiency ratio"""
        close = data['Close']
        
        # Price change over period
        price_change = abs(close - close.shift(period))
        
        # Sum of absolute daily changes
        daily_changes = abs(close - close.shift(1))
        sum_daily_changes = daily_changes.rolling(period).sum()
        
        # Efficiency ratio
        efficiency = price_change / sum_daily_changes
        return efficiency.fillna(0)
    
    def calculate_session_momentum(self, data):
        """Calculate momentum within trading sessions"""
        # Group by date and calculate intraday momentum
        momentum = pd.Series(index=data.index, dtype=float)
        
        for date, day_data in data.groupby(data.index.date):
            if len(day_data) > 1:
                day_momentum = (day_data['Close'].iloc[-1] - day_data['Open'].iloc[0]) / day_data['Open'].iloc[0]
                momentum.loc[day_data.index] = day_momentum
        
        return momentum.fillna(0)
    
    def detect_range_expansion(self, data, period=20):
        """Detect if current range is expanding vs contracting"""
        current_range = data['High'] - data['Low']
        avg_range = current_range.rolling(period).mean()
        
        range_expansion = current_range / avg_range
        return range_expansion.fillna(1)

class ImprovedBreakoutStrategy(bt.Strategy):
    """Improved strategy with better risk management and ML integration"""
    
    params = (
        ('risk_percent', 1.0),
        ('risk_reward_ratio', 2.0),
        ('session_start_hour', 8),
        ('max_trade_hours', 8),
        ('min_range_pips', 5.0),
        ('ml_confidence_threshold', 0.7),  # Higher threshold
        ('max_daily_trades', 1),  # Limit trades per day
        ('min_volatility_regime', 1),  # Only trade in medium+ volatility
        ('min_trend_strength', 0.2),  # Minimum trend strength required
        ('dynamic_position_sizing', True),  # Adjust size based on ML confidence
        ('breakeven_enabled', True),  # Move to breakeven
        ('trailing_enabled', True),  # Trailing stop
    )
    
    def __init__(self):
        self.ml_predictor = None
        self.feature_engine = ImprovedFeatureEngine()
        self.trades_log = []
        self.daily_trades = {}
        self.session_high = None
        self.session_low = None
        self.session_start = None
        self.trade_start_time = None
        self.ml_predictions = []
        self.entry_price = None
        self.stop_price = None
        self.target_price = None
        self.breakeven_moved = False
        
    def set_ml_predictor(self, predictor):
        self.ml_predictor = predictor
    
    def get_current_date(self):
        return self.data.datetime.date()
    
    def can_trade_today(self):
        """Check if we can still trade today"""
        current_date = self.get_current_date()
        trades_today = self.daily_trades.get(current_date, 0)
        return trades_today < self.params.max_daily_trades
    
    def extract_current_features(self):
        """Extract features with error handling"""
        try:
            # Get more recent data for better feature calculation
            recent_data = []
            lookback = min(200, len(self.data))  # Increased lookback
            
            for i in range(lookback):
                recent_data.append([
                    self.data.open[-i],
                    self.data.high[-i], 
                    self.data.low[-i],
                    self.data.close[-i],
                    getattr(self.data, 'volume', [1000] * len(self.data))[-i]
                ])
            
            if len(recent_data) < 100:
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(recent_data[::-1], columns=['Open', 'High', 'Low', 'Close', 'Volume'])
            current_time = self.data.datetime.datetime()
            df.index = pd.date_range(end=current_time, periods=len(df), freq='30T')
            
            # Extract enhanced features
            features = self.feature_engine.extract_features(df)
            
            if len(features) > 0:
                return features.iloc[-1]
            return None
            
        except Exception as e:
            print(f"Error extracting features: {e}")
            return None
    
    def calculate_position_size(self, ml_confidence, base_size=1000):
        """Dynamic position sizing based on ML confidence"""
        if not self.params.dynamic_position_sizing:
            return base_size
        
        # Scale position size based on confidence
        # Higher confidence = larger position (up to 1.5x)
        # Lower confidence = smaller position (down to 0.5x)
        confidence_multiplier = 0.5 + (ml_confidence * 1.0)  # 0.5 to 1.5 range
        
        return int(base_size * confidence_multiplier)
    
    def next(self):
        current_time = self.data.datetime.time()
        current_hour = current_time.hour
        current_date = self.get_current_date()
        
        # Session detection
        if current_hour == self.params.session_start_hour and current_time.minute == 0:
            self.session_start = len(self.data)
            self.session_high = self.data.high[0]
            self.session_low = self.data.low[0]
            self.breakeven_moved = False
            
        elif (self.session_start and 
              len(self.data) - self.session_start <= 2 and
              current_hour == self.params.session_start_hour):
            self.session_high = max(self.session_high, self.data.high[0])
            self.session_low = min(self.session_low, self.data.low[0])
        
        # Enhanced trade decision logic
        elif (self.session_start and 
              len(self.data) - self.session_start == 3 and
              not self.position and
              self.can_trade_today()):
            
            range_pips = (self.session_high - self.session_low) * 10000
            
            if range_pips > self.params.min_range_pips:
                current_features = self.extract_current_features()
                
                if current_features is not None:
                    # Enhanced filtering with multiple conditions
                    if self.should_trade(current_features):
                        ml_confidence = self.ml_predictor.predict_probability(current_features)
                        self.execute_enhanced_trade(ml_confidence, current_features)
        
        # Enhanced position management
        if self.position:
            self.manage_position()
    
    def should_trade(self, features):
        """Enhanced trade filtering with multiple conditions"""
        # Check ML confidence
        if self.ml_predictor and self.ml_predictor.is_trained:
            ml_confidence = self.ml_predictor.predict_probability(features)
            if ml_confidence < self.params.ml_confidence_threshold:
                return False
        
        # Check volatility regime
        if hasattr(features, 'volatility_regime'):
            if features['volatility_regime'] < self.params.min_volatility_regime:
                return False
        
        # Check trend strength
        if hasattr(features, 'trend_strength'):
            if features['trend_strength'] < self.params.min_trend_strength:
                return False
        
        # Check market efficiency (avoid choppy markets)
        if hasattr(features, 'market_efficiency'):
            if features['market_efficiency'] < 0.3:  # Too choppy
                return False
        
        return True
    
    def execute_enhanced_trade(self, ml_confidence, features):
        """Execute trade with enhanced logic"""
        buy_price = self.session_high + 0.0001
        sell_price = self.session_low - 0.0001
        
        # Calculate dynamic position size
        position_size = self.calculate_position_size(ml_confidence)
        
        current_date = self.get_current_date()
        
        if self.data.close[0] > buy_price:
            self.buy(size=position_size)
            self.entry_price = buy_price
            self.stop_price = self.session_low - 0.0001
            self.target_price = buy_price + (buy_price - self.stop_price) * self.params.risk_reward_ratio
            self.trade_start_time = self.data.datetime.datetime()
            
            # Update daily trade count
            self.daily_trades[current_date] = self.daily_trades.get(current_date, 0) + 1
            
            self.trades_log.append({
                'action': 'buy',
                'entry_price': buy_price,
                'ml_confidence': ml_confidence,
                'position_size': position_size,
                'volatility_regime': features.get('volatility_regime', 0),
                'trend_strength': features.get('trend_strength', 0),
                'range_pips': (self.session_high - self.session_low) * 10000
            })
            
        elif self.data.close[0] < sell_price:
            self.sell(size=position_size)
            self.entry_price = sell_price
            self.stop_price = self.session_high + 0.0001
            self.target_price = sell_price - (self.stop_price - sell_price) * self.params.risk_reward_ratio
            self.trade_start_time = self.data.datetime.datetime()
            
            # Update daily trade count
            self.daily_trades[current_date] = self.daily_trades.get(current_date, 0) + 1
            
            self.trades_log.append({
                'action': 'sell',
                'entry_price': sell_price,
                'ml_confidence': ml_confidence,
                'position_size': position_size,
                'volatility_regime': features.get('volatility_regime', 0),
                'trend_strength': features.get('trend_strength', 0),
                'range_pips': (self.session_high - self.session_low) * 10000
            })
    
    def manage_position(self):
        """Enhanced position management with breakeven and trailing"""
        # Time-based exit
        if hasattr(self, 'trade_start_time') and self.trade_start_time:
            hours_in_trade = (self.data.datetime.datetime() - self.trade_start_time).total_seconds() / 3600
            if hours_in_trade >= self.params.max_trade_hours:
                self.close()
                return
        
        if self.position.size > 0:  # Long position
            # Move to breakeven when 50% to target
            if (self.params.breakeven_enabled and not self.breakeven_moved and
                self.data.high[0] >= self.entry_price + (self.target_price - self.entry_price) * 0.5):
                self.stop_price = self.entry_price + 0.0001  # 1 pip profit
                self.breakeven_moved = True
            
            # Check exit conditions
            if (self.data.low[0] <= self.stop_price or 
                self.data.high[0] >= self.target_price):
                self.close()
                
        else:  # Short position
            # Move to breakeven when 50% to target
            if (self.params.breakeven_enabled and not self.breakeven_moved and
                self.data.low[0] <= self.entry_price - (self.entry_price - self.target_price) * 0.5):
                self.stop_price = self.entry_price - 0.0001  # 1 pip profit
                self.breakeven_moved = True
            
            # Check exit conditions
            if (self.data.high[0] >= self.stop_price or
                self.data.low[0] <= self.target_price):
                self.close()

def run_improved_analysis(data):
    """Run improved ML analysis with multiple optimizations"""

    print("🚀 Running Improved ML Analysis...")

    # Enhanced feature engineering
    feature_engine = ImprovedFeatureEngine()
    features = feature_engine.extract_features(data)

    # Enhanced label generation with stricter criteria
    label_generator = BreakoutLabelGenerator(
        risk_reward_ratio=2.5,  # Slightly higher R:R
        max_trade_hours=6       # Shorter trade duration
    )
    labels = label_generator.generate_labels(data)

    # Align data
    common_index = features.index.intersection(labels.index)
    if len(common_index) < 200:
        print("❌ Insufficient data for robust ML training")
        return None

    features_aligned = features.loc[common_index]
    labels_aligned = labels.loc[common_index]

    # Enhanced ML training with better model
    ml_predictor = MLBreakoutPredictor()

    # Use Gradient Boosting for better performance
    ml_predictor.model = GradientBoostingClassifier(
        n_estimators=200,
        learning_rate=0.1,
        max_depth=6,
        min_samples_split=20,
        min_samples_leaf=10,
        random_state=42
    )

    success = ml_predictor.train(features_aligned, labels_aligned)
    if not success:
        return None

    # Test multiple parameter combinations
    param_sets = [
        {
            'ml_confidence_threshold': 0.65,
            'min_volatility_regime': 1,
            'min_trend_strength': 0.15,
            'risk_reward_ratio': 2.0,
            'max_trade_hours': 8
        },
        {
            'ml_confidence_threshold': 0.70,
            'min_volatility_regime': 1,
            'min_trend_strength': 0.20,
            'risk_reward_ratio': 2.5,
            'max_trade_hours': 6
        },
        {
            'ml_confidence_threshold': 0.75,
            'min_volatility_regime': 2,
            'min_trend_strength': 0.25,
            'risk_reward_ratio': 3.0,
            'max_trade_hours': 4
        }
    ]

    best_result = None
    best_params = None

    for i, params in enumerate(param_sets):
        print(f"\\n📊 Testing parameter set {i+1}/3...")
        print(f"   ML Threshold: {params['ml_confidence_threshold']}")
        print(f"   Min Volatility: {params['min_volatility_regime']}")
        print(f"   Risk:Reward: {params['risk_reward_ratio']}")

        try:
            result = run_improved_backtest(data, ml_predictor, params)

            print(f"   Return: {result['total_return']:.2f}%")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Trades: {result['total_trades']}")
            print(f"   Sharpe: {result['sharpe_ratio']:.2f}")

            # Select best based on Sharpe ratio and positive returns
            if (result['total_return'] > 0 and
                (best_result is None or result['sharpe_ratio'] > best_result['sharpe_ratio'])):
                best_result = result
                best_params = params

        except Exception as e:
            print(f"   ❌ Error: {e}")
            continue

    return best_result, best_params, ml_predictor

def run_improved_backtest(data, ml_predictor, params):
    """Run backtest with improved strategy"""

    cerebro = bt.Cerebro()
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)

    # Add improved strategy
    cerebro.addstrategy(ImprovedBreakoutStrategy, **params)

    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.0001)

    # Add analyzers
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')

    # Run backtest
    results = cerebro.run()
    strategy = results[0]

    # Set ML predictor
    strategy.set_ml_predictor(ml_predictor)

    final_value = cerebro.broker.getvalue()

    # Extract results
    trade_analyzer = strategy.analyzers.trades.get_analysis()
    sharpe_analyzer = strategy.analyzers.sharpe.get_analysis()
    drawdown_analyzer = strategy.analyzers.drawdown.get_analysis()
    returns_analyzer = strategy.analyzers.returns.get_analysis()

    # Calculate enhanced metrics
    total_return = (final_value - 10000) / 10000 * 100
    total_trades = trade_analyzer.get('total', {}).get('total', 0)
    winning_trades = trade_analyzer.get('won', {}).get('total', 0)
    losing_trades = trade_analyzer.get('lost', {}).get('total', 0)
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    max_drawdown = drawdown_analyzer.get('max', {}).get('drawdown', 0)
    sharpe_ratio = sharpe_analyzer.get('sharperatio', 0) or 0

    # Calculate additional metrics
    avg_win = trade_analyzer.get('won', {}).get('pnl', {}).get('average', 0)
    avg_loss = trade_analyzer.get('lost', {}).get('pnl', {}).get('average', 0)
    profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 else 0

    return {
        'total_return': total_return,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'final_value': final_value,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'parameters': params,
        'trades_log': strategy.trades_log,
        'daily_trades': strategy.daily_trades
    }

def main():
    """Main improved analysis function"""

    print("🚀 Improved ML-Enhanced MT5 Analysis")
    print("=" * 50)
    print("Improvements:")
    print("✅ Enhanced feature engineering (market regime detection)")
    print("✅ Stricter ML filtering (higher confidence thresholds)")
    print("✅ Better risk management (breakeven, trailing)")
    print("✅ Dynamic position sizing based on ML confidence")
    print("✅ Multiple parameter optimization")
    print("✅ Enhanced trade filtering")
    print()

    # Find and convert data
    csv_files = find_mt5_csv_files()
    if not csv_files:
        print("❌ No CSV files found")
        return False

    # Use existing cleaned file if available
    cleaned_files = [f for f in os.listdir('.') if 'cleaned.csv' in f]

    if cleaned_files:
        print(f"📁 Using existing cleaned file: {cleaned_files[0]}")
        data = pd.read_csv(cleaned_files[0], index_col=0, parse_dates=True)
    else:
        print(f"📁 Converting: {csv_files[0]}")
        data, _ = convert_mt5_csv(csv_files[0])
        if data is None:
            return False

    print(f"✅ Data loaded: {len(data)} bars")
    print(f"   Period: {data.index[0].date()} to {data.index[-1].date()}")

    # Run improved analysis
    result = run_improved_analysis(data)

    if result is None:
        print("❌ Analysis failed")
        return False

    best_result, best_params, ml_predictor = result

    if best_result is None:
        print("❌ No profitable parameter combinations found")
        print("💡 Suggestions:")
        print("   - Try different time periods")
        print("   - Adjust risk management parameters")
        print("   - Consider different market conditions")
        return False

    # Display results
    print("\\n🏆 IMPROVED RESULTS:")
    print("=" * 40)
    print(f"Total Return: {best_result['total_return']:.2f}%")
    print(f"Win Rate: {best_result['win_rate']:.1f}%")
    print(f"Total Trades: {best_result['total_trades']}")
    print(f"Profit Factor: {best_result['profit_factor']:.2f}")
    print(f"Sharpe Ratio: {best_result['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {best_result['max_drawdown']:.2f}%")

    print(f"\\n⚙️ Best Parameters:")
    for key, value in best_params.items():
        print(f"   {key}: {value}")

    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    result_file = f'backtesting/results/improved_analysis_{timestamp}.json'

    os.makedirs('backtesting/results', exist_ok=True)

    with open(result_file, 'w') as f:
        json.dump({
            'strategy': 'Improved_ML_Enhanced_FirstCandle',
            'results': best_result,
            'best_parameters': best_params,
            'timestamp': timestamp
        }, f, indent=2, default=str)

    print(f"\\n💾 Results saved to: {result_file}")

    return True

if __name__ == "__main__":
    main()
